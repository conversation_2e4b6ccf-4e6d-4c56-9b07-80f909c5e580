#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);

use JSON::XS qw(encode_json decode_json);

use API::ART;
use WPSOWORKS;
use WPSOWORKS::Collection::System::PROJECT;

my $logger_name  = 'WPSOWORKS::BIN::sync_data';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_CUSTOMER_ID_OR_CONTRACT_ID
	UNABLE_TO_LOAD_LOG_CONFIG
	UNABLE_INIT_CLASS
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOWORKS
	CANNOT_INIT_WPSOWORKS_COLLECTION_SYSTEM_PROJECT
	CANNOT_INIT_WPSOWORKS_SYNC_TYPE
	UNABLE_TO_FETCH_PROJECTS
	UNABLE_TO_CREATE_NEW_SYNC_TYPE
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0 [opzioni] <syncType> <customerId> <contractId>
	
	<opzioni>:
		-f, --force - indica di salvare i dati anche se sono uguali al run precedente
		    --project-id=<PROJECTID> - lavora solo il progetto indicato
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOWORKS_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-D, --daemon=<secondi> - prepara l'ambiente per eseguire come daemon effettuando il polling ogni <secondi>
			utilizzare start_stop_daemon per lo start e lo stop
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	
";
	exit $rc;
}

usage( 1 ) if scalar(@ARGV) < 2;

my ( $help,$syncType,$customerId,$contractId,$artid,$api_script_user,$api_script_password,$commit,$log_config,$debug,$daemon,$waitsec,$force,$only_project_id );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};
my $end_string = '===== END ======================================================================';

if ( defined $ENV{WPSOWORKS_TRANSACTION_MODE} && $ENV{WPSOWORKS_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>				\$help,
	'force|f'=>				\$force,
	'project-id=n'=>		\$only_project_id,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'daemon|D=i'          => sub {
		$daemon     = 1;
		$waitsec    = $_[1];
		die "Con l'opzione --daemon, <secondi> deve essere > 0\n"
			unless $waitsec > 0;
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

$syncType = $ARGV[0];
$customerId = $ARGV[1];
$contractId = $ARGV[2];
usage( ERROR_MISSING_CUSTOMER_ID_OR_CONTRACT_ID )
	unless defined $syncType || defined $customerId || defined $contractId;

eval "use WPSOWORKS::Sync::$syncType";
if($@) {
	print STDERR "Impossibile istanziare la classe WPSOWORKS::Sync::$syncType: $@";
	exit ERROR_UNABLE_INIT_CLASS;
}

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

my $tmp_log_file_prefix = $syncType . '_' . $customerId . '_' . $contractId . '_';
$tmp_log_file_prefix =~ s/ /_/g;
$ENV{LOG_FILE_PREFIX} = $tmp_log_file_prefix;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

HEAD: while ( $keep_going ) {
	
	$keep_going = 0
		unless $daemon;
		
	$logger->debug( "Inizializzo API::ART" );
	my $api_art;
	eval {
		$api_art = API::ART->new(
			ARTID     => $artid
			,USER     => $api_script_user
			,PASSWORD => $api_script_password
			,AUTOSAVE => 0
			,DEBUG    => $debug
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare API::ART: $@" );
		exit ERROR_CANNOT_INIT_API_ART;
	}
	
	my $wpsoworks;
	eval {
		$wpsoworks = WPSOWORKS->new(
			ART => $api_art
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare WPSOWORKS: $@" );
		exit ERROR_CANNOT_INIT_WPSOWORKS;
	}
	
	my $wpsoworks_projects;
	eval {
		$wpsoworks_projects = WPSOWORKS::Collection::System::PROJECT->new(
			ART => $api_art
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare WPSOWORKS::Collection::System::PROJECT: $@" );
		exit ERROR_CANNOT_INIT_WPSOWORKS_COLLECTION_SYSTEM_PROJECT;
	}

	my $class = "WPSOWORKS::Sync::$syncType";
	my $sync_objects;
	eval {
			$sync_objects = 	$class->new(
							WPSOWORKS 	=> $wpsoworks
							,CUSTOMER_ID => $customerId
							,CONTRACT_ID => $contractId
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare $class: $@" );
		exit ERROR_CANNOT_INIT_WPSOWORKS_SYNC_TYPE;
	}
	
	#my $$ref_is_fatal = 0;
	my  $is_fatal = 0; 
	
	# recupero dei progetti gia presenti a sistema
	my $search = {
		customerId => $customerId,
		contractId => $contractId
	};
	if ($only_project_id){
		$logger->info( "Gestisco solo il progetto ".$only_project_id );
		$search->{projectId} = $only_project_id;
	}
	
	my $ret = $wpsoworks_projects->cerca(%{$search});
	
	unless (defined $ret) {
		if($is_fatal) {
			$logger->fatal("Impossibile recuperare i progetti: ".$api_art->last_error());
			exit ERROR_UNABLE_TO_FETCH_PROJECTS;
		} else {
			$logger->error("Impossibile recuperare i progetti: ".$api_art->last_error());
			next HEAD;
		}
	}	
	
	$logger->info("Trovati ".(scalar @{$ret})." progetti da gestire");
	
	foreach my $project ( @{$ret} ) { 
		 		
		my $projectId = $project->property('projectId');
		
		$logger->info( "Recupero i $syncType per il progetto ".$projectId );
		
		$is_fatal = 0;
		my $mt = $sync_objects->importa(PROJECT_ID => $projectId, IS_FATAL=>\$is_fatal, FORCE => $force ? 1 : 0);
		
		if($mt) {
			$logger->info("$syncType relativi a progetto con projectId " . $projectId . " gestiti");
			if( $commit ) {
				$logger->debug( "Eseguo la commit delle modifiche fatte sul db" );
				$api_art->save();
			} else {
				$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
			}
		} else {
			if($is_fatal) {
				$logger->fatal("Impossibile creare o aggiornare $syncType relativi a progetto con projectId ". $projectId.": ".$api_art->last_error());
				exit ERROR_UNABLE_TO_CREATE_NEW_SYNC_TYPE;
			} else {
				$logger->error("Impossibile creare o aggiornare $syncType relativi a progetto con projectId ". $projectId.": ".$api_art->last_error());
			}
		}	
	}
	# per ora la vista materializzata esiste solo per i cavi quindi procedo al refresh solo in questa casistica
	if ($commit && $syncType eq 'CAVI'){
		$logger->info( "Inizio il refresh della vista materializzata per i $syncType" );
		$api_art->_dbh()->do("BEGIN DBMS_SNAPSHOT.REFRESH( 'WORKS.MV_".$syncType."','C'); end;");
		$logger->info( "Terminato il refresh della vista materializzata per i $syncType" );
	}
	
	if ( $daemon ) {
		# undef per liberare le risorse prima dello sleep 
		$ret = undef
		$sync_objects = undef;
		$wpsoworks_projects = undef;
		$wpsoworks = undef;
		$api_art = undef;
		$logger->info( '--------------------------------------------------------------------------------' );
		$logger->info( "Ho lavorato tutti i $syncType trovati, aspetto $waitsec secondi" );
		sleep $waitsec;
		next HEAD;
	}
	
	$logger->info( '===== END ======================================================================' );

}

exit ERROR_OK;
