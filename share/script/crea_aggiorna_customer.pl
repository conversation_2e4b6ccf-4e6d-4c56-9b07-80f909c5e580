#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);

use API::ART;
use WPSOWORKS;

my $logger_name  = 'WPSOWORKS::BIN::crea_aggiorna_customer';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_CUSTOMER_ID_CUSTOMER_NAME_OR_CONTEXT
	BAD_CONTEXT
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOWORKS
	CANNOT_GET_COLL_CUSTOMERS
	CANNOT_GET_CUSTOMERS
	CANNOT_UPDATE_CUSTOMER_NAME
	CANNOT_UPDATE_CUSTOMER_PROPERTIES
	CANNOT_ADD_GROUP
	CANNOT_CREATE_CUSTOMER
);


sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0 [parametri] <customerId> <customerName> <context>
		
	<opzioni>:
		--background-url=<URL> - url relativo alla home di WPSOUI dove reperire l'immagine di background
		--logo-url=<URL> - url relativo alla home di WPSOUI dove reperire l'immagine con il logo del customer
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOWORKS_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	
	<context>: valore del contesto: può valere OF_AB o CREATION_TIM_OLO
	
	Se è necessario aggiungere un contesto ad un customer già presente utilizzare lo script come fosse nuovo:
	lo script adeguerà i gruppi del sistema già presente 
";
	exit $rc;
}

usage( 1 ) if scalar(@ARGV) < 2;

my ( $help,$customerId,$customerName,$context,$backgroundURL,$logoURL,$artid,$api_script_user,$api_script_password,$commit,$log_config,$debug,$daemon,$waitsec );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

if ( defined $ENV{WPSOWORKS_TRANSACTION_MODE} && $ENV{WPSOWORKS_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'background-url=s' =>	\$backgroundURL,
	'logo-url=s' =>			\$logoURL,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" ); 
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

$customerId = $ARGV[0];
$customerName = $ARGV[1];
$context = $ARGV[2];
usage( ERROR_MISSING_CUSTOMER_ID_CUSTOMER_NAME_OR_CONTEXT )
	if (!defined $customerId || !defined $customerName || !defined $context);

unless ($context =~/^(OF_AB|OF_PNRR|CREATION_TIM_OLO|FASTWEB_MNT|WINDTRE_CREATION)$/){
	print STDERR "Parametro context può valere OF_AB|OF_PNRR|CREATION_TIM_OLO|FASTWEB_MNT|WINDTRE_CREATION";
	exit ERROR_BAD_CONTEXT;
}

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID     => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $wpsoworks;
eval {
	$wpsoworks = WPSOWORKS->new(
		ART => $api_art
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOWORKS: $@" );
	exit ERROR_CANNOT_INIT_WPSOWORKS;
}

my $collCustomers = $wpsoworks->customers();
unless (defined $collCustomers){
	$logger->fatal( "Impossibile recuperare collection customer: ".$api_art->last_error() );
	exit ERROR_CANNOT_GET_COLL_CUSTOMERS;
}

# cerco se esiste già un sistema con lo stesso customerId
my $systems = $collCustomers->cerca(
	"customerId" => $customerId
);
unless (defined $systems){
	$logger->fatal( "Impossibile recuperare il sistema customer: ".$api_art->last_error() );
	exit ERROR_CANNOT_GET_CUSTOMERS;
}

# se esisto lo modifico
if (scalar @{$systems}){
	my $customer = $systems->[0];
	
	# verifico che il customerName sia uguale altrimenti do un errore
	if ($customer->property('customerName') ne $customerName){
		$logger->fatal( "Aggiornamento customerName non permesso: atteso ". $customer->property('customerName'));
		exit ERROR_CANNOT_UPDATE_CUSTOMER_NAME;
	}
	
	my %update_properties;
	$update_properties{backgroundURL} = $backgroundURL if defined $backgroundURL;
	$update_properties{logoURL} = $logoURL if defined $logoURL;
	
	if (scalar keys %update_properties){
		unless ($customer->set_property(PROPERTIES => \%update_properties)){
			$logger->fatal( "Impossibile aggiornare le properties del sistema: ". $api_art->last_error());
			exit ERROR_CANNOT_UPDATE_CUSTOMER_PROPERTIES;
		}
	}
	
	unless ($customer->add_context(context => $context)){
		$logger->fatal( "Impossibile aggiungere il gruppo al sistema: ". $api_art->last_error());
		exit ERROR_CANNOT_ADD_GROUP;
	}
	
	$logger->info("Aggiornamento customer '$customerId' eseguito con successo (ID ".$customer->id().")!");
	
} else { # se non esiste lo creo
	my %create_params = (
		 customerId 	=> $customerId
		,customerName 	=> $customerName
		,context		=> $context
	);
	$create_params{backgroundURL} = $backgroundURL if defined $backgroundURL;
	$create_params{logoURL} 	  = $logoURL if defined $logoURL;
	
	my $customer = $collCustomers->crea(%create_params);
	if(defined $customer) {
		$logger->info("Customer '$customerId' creato con successo (ID ".$customer->id().")!");
	} else {
		$logger->fatal("Impossibile creare il customer '$customerId': ".$api_art->last_error());
		exit ERROR_CANNOT_CREATE_CUSTOMER;
	}
}

if( $commit ) {
	$logger->debug( "Eseguo la commit delle modifiche fatte sul db" );
	$api_art->save();
} else {
	$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
}

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;
