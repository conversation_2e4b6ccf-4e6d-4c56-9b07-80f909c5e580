#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);

use API::ART;
use API::ART::Collection::System;
use WPSOWORKS::Collection::System::PROJECT;

my $logger_name  = 'WPSOWORKS::BIN::allinea_progetti_core_vs_works';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_CUSTOMER_ID_OR_CONTRACT_ID
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOWORKS_Collection_System_PROJECT
	CANNOT_INIT_CORE_API_ART
	CANNOT_INIT_CORE_API_ART_COLLECTION_SYSTEM
	CANNOT_GET_CORE_PROJECTS
	CANNOT_GET_PROJECTS
	CANNOT_CREATE_PROJECT
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0	<parametri> <customerId> <contractId>
	
	<opzioni>:
		--project-id=<projectId> - Allinea un singolo progetto
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		--core-artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOCORE_ARTID)
		--core-api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOCORE_SCRIPT_USER)
		--core-api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOCORE_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOWORKS_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	
";
	exit $rc;
}

usage( 1 ) if scalar(@ARGV) < 2;

my ( $help,$customerId,$contractId,$artid,$api_script_user,$api_script_password,$core_artid,$core_api_script_user,$core_api_script_password,$commit,$log_config,$debug,$project_id );

$artid                    = $ENV{WPSOWORKS_ARTID};
$api_script_user          = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password      = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$core_artid               = $ENV{WPSOCORE_ARTID};
$core_api_script_user     = $ENV{WPSOCORE_SCRIPT_USER};
$core_api_script_password = $ENV{WPSOCORE_SCRIPT_PASSWORD};
$log_config               = $ENV{LOG4PERL_CONF};
my $end_string = '===== END ======================================================================';

if ( defined $ENV{WPSOWORKS_TRANSACTION_MODE} && $ENV{WPSOWORKS_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>					\$help,
	'project-id=s' =>			\$project_id,
	'artid|a=s' =>				\$artid,
	'api-user|u=s' =>			\$api_script_user,
	'api-password|p=s' =>		\$api_script_password,
	'core-artid=s' =>			\$core_artid,
	'core-api-user=s' =>		\$core_api_script_user,
	'core-api-password=s' =>	\$core_api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

$customerId = $ARGV[0];
$contractId = $ARGV[1];
usage( ERROR_MISSING_CUSTOMER_ID_OR_CONTRACT_ID )
	unless defined $customerId || defined $contractId;

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

my $tmp_log_file_prefix = $customerId . '_' . $contractId . '_';
$tmp_log_file_prefix =~ s/ /_/g;
$ENV{LOG_FILE_PREFIX} = $tmp_log_file_prefix;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID     => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $projects_collection;
eval {
	$projects_collection = WPSOWORKS::Collection::System::PROJECT->new(ART => $api_art);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOWORKS::Collection::System::PROJECT: $@" );
	exit ERROR_CANNOT_INIT_WPSOWORKS_Collection_System_PROJECT;
}

$logger->debug( "Inizializzo CORE API::ART" );
my $core_api_art;
eval {
	$core_api_art = API::ART->new(
		ARTID     => $core_artid
		,USER     => $core_api_script_user
		,PASSWORD => $core_api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare CORE API::ART: $@" );
	exit ERROR_CANNOT_INIT_CORE_API_ART;
}

my $core_projects_collection;
eval {
	$core_projects_collection = API::ART::Collection::System->new(ART => $core_api_art);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare CORE API::ART::Collection::System: $@" );
	exit ERROR_CANNOT_INIT_CORE_API_ART_COLLECTION_SYSTEM;
}

my $core_search_params = {
	SYSTEM_TYPE_NAME => ['PROJECT'],
	ACTIVE => 1,
	PROPERTIES => {
		customerId => $customerId,
		contractId => $contractId
	},
	SORT => [
		{
			PROPERTY => 
				{
					projectId => 1
				}
		}
	]
};
$core_search_params->{PROPERTIES}->{projectId} = $project_id if defined $project_id;

# recupero tutti i progetti su core
my $core_projects = $core_projects_collection->find_object(%{$core_search_params});
unless ( defined $core_projects ) {
	$logger->fatal( "Impossibile recuperare i progetti su CORE:".$core_api_art->last_error() );
	exit ERROR_CANNOT_GET_CORE_PROJECTS;
}

my $search_params = {
	customerId => $customerId,
	contractId => $contractId
};
$search_params->{projectId} = $project_id if defined $project_id;

# recupero tutti i progetti su works
my $projects = $projects_collection->cerca(%{$search_params});
unless ( defined $projects ) {
	$logger->fatal( "Impossibile recuperare i progetti su WORKS:".$api_art->last_error() );
	exit ERROR_CANNOT_GET_PROJECTS;
}

my $created = 0;

# per ora si procedere ad implementare solo la gestione della creazione
for my $p (@{$core_projects}){
	# se lo trovo localmente non faccio nulla altrimenti lo creo
	unless (grep {$p->property('projectId') eq $_->property('projectId')} @{$projects}){
		my $create_params = $p->property();
		next unless defined $p->parent(); #è un blocco che serve solo in sviluppo
		$create_params->{cityId} = $p->parent()->id();
		$create_params->{cityGroupName} = 'CITY_'.$create_params->{cityId};
		$create_params->{projectGroupName} = 'PROJECT_'.$customerId.'_'.$contractId.'_'.$p->property('projectId');
		$create_params->{projectGroupDescription} = $create_params->{projectGroupName};
		$create_params->{project4RPBGroupName} = 'PROJECT4RPB_'.$customerId.'_'.$contractId.'_'.$p->property('projectId');
		$create_params->{project4RPBGroupDescription} = $create_params->{project4RPBGroupName};
		
		#use Data::Dumper;
		#print STDERR Dumper $create_params;
		my $project_works =  $projects_collection->crea(%{$create_params});
		unless ( defined $project_works ) {
			$logger->fatal( "Impossibile creare il nuovo progetto ".$p->property('projectId').":".$api_art->last_error() );
			exit ERROR_CANNOT_CREATE_PROJECT;
		}
		$created++;
		$logger->info( "Creato sistema PROJECT con id ".$project_works->id()." relativo a projectID ".$p->property('projectId') );
	} 
}
$logger->info( "Creati ".$created." sistemi PROJECT");
if( $commit ) {
	$logger->info( "Eseguo la commit delle modifiche fatte sul db" );
	$api_art->save();
} else {
	$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
}

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;