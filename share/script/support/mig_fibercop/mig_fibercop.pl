#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Text::CSV_XS;

use API::ART;
use WPSOWORKS::Collection::Activity::LAVORO;

my $logger_name  = 'WPSOWORKS::BIN::mig_fibercop';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOWORKS_Collection_Activity_LAVORO
	CANNOT_OPEN_INPUT_FILE
	CANNOT_OPEN_OUTPUT_FILE
	OUTPUT_FILE_EXISTS
	DB
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]
	
	Effettua migrazione lavori armadio

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOCORE_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOCORE_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOCORE_SCRIPT_PASSWORD)
		-f, --filename=<FILENAME> - csv con il tracciato previsto per la migrazione
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $file );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'filename|f=s' =>		\$file,,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

unless (defined $file){
	print STDERR "--filename obbligatorio";
	usage( ERROR_WRONG_OPTIONS );
}

( my $out_file = $file ) =~ s{(\.csv)?$}{_OUTPUT.csv};

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID    => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $csv = Text::CSV_XS->new(
	{
		binary        => 1
		,auto_diag    => 1 
		,sep_char     => ';'
		,quote_char   => '"'
        ,eol          => $/
        ,always_quote => 1
	}
);
my $fh;
unless(open $fh, "<:encoding(utf8)", $file){
	$logger->fatal( "Impossibile aprire in lettura il file $file: $!" );
	exit ERROR_CANNOT_OPEN_INPUT_FILE;
};

if ( -e $out_file ) {
    $logger->fatal(sprintf q{Il file di output '%s' esiste già}, $out_file);
    exit ERROR_OUTPUT_FILE_EXISTS
}

my $out;
unless(open $out, ">:encoding(utf8)", $out_file){
	$logger->fatal( "Impossibile aprire in scrittura il file $out_file: $!" );
	exit ERROR_CANNOT_OPEN_OUTPUT_FILE;
};

my $collLavoro;
eval {
	$collLavoro = WPSOWORKS::Collection::Activity::LAVORO->new(ART => $api_art);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOWORKS::Collection::Activity::LAVORO: $@" );
	exit ERROR_CANNOT_INIT_WPSOWORKS_Collection_Activity_LAVORO;
}

my $csv_mandatory_fields = [
	"workType",
	"maker",
	"assetId",
	"flagFIR",
	"oneForAll",
	"type",
	"description",
	"workingGroupCode",
	"networkId",
	"workerId",
	"workerName",
	"startDate",
	"endDate",
	"ref00",
	"status"
];

my $header = $csv->getline($fh);

$csv->print($out, [ @{$header}, 'ESITO_KO_LAVORAZIONE' ]);

#print Dumper($headers);
my $i = 1;
CSV_LOOP: while (<$fh>){
	$i++;
	#$logger->info( "Elaboro riga $i" );
	my %row;
	my %rowFinal;
	$csv->parse($_);
	my @fields = $csv->fields();

	# read data
	@row{@{$header}} = @fields;
	
	
	for my $k (keys %row){
		$rowFinal{$k} = $row{$k};
	}
	
	#print STDERR Dumper \%rowFinal;

	my %create_params = (
		"customerId"		=> 'TIM',
		,"contractId"		=> 'FTTH',
		,"workType"			=> $rowFinal{workType},
		,"workingGroupCode"	=> $rowFinal{workingGroupCode},
		,"details"			=> [
			{type => $rowFinal{type}},
		],
		"maker"				=> $rowFinal{maker},
		,"description"		=> $rowFinal{description},
		,"reference"		=> {
			ref00 => $rowFinal{ref00},
		}
		,"flagFIR"			=> $rowFinal{flagFIR}
		,"assetId"                      => $rowFinal{assetId}
		,"networkId"                    => $rowFinal{networkId}
		,"oneForAll"                    => $rowFinal{oneForAll}
	);

	if ($rowFinal{maker} eq 'Team'){
		$create_params{team} = {
			"teamName"	=> $rowFinal{workerName},
			,"teamId"	=> $rowFinal{workerId},
			,"slaStart"	=> $rowFinal{startDate},
			,"slaEnd"	=> $rowFinal{endDate},
		}
	} else {
		$create_params{subcontractInfo} = {
			"subContractName"	=> $rowFinal{workerName},
			,"subContractCode"	=> $rowFinal{workerId},
			,"startPlannedDate"	=> $rowFinal{startDate},
			,"endPlannedDate"	=> $rowFinal{endDate},
		}
	}

	$api_art->_dbh()->do("savepoint mig_fibercop");

	# creo il LAVORO
	my $lavoro = $collLavoro->crea(%create_params);

	unless (defined $lavoro){
		my $message = "Riga $i => KO: Errore in creazione LAVORO ".$api_art->last_error();
        $csv->print($out, [ @fields, $message ]);
		$logger->error($message);
		$api_art->_dbh()->do("rollback to savepoint mig_fibercop");
		$api_art->cancel();
		next CSV_LOOP;
	}

	$logger->debug("Riga $i => OK: Creato LAVORO (".$lavoro->[0]->id().")");

	if ($rowFinal{status} eq 'ESPLETATO'){
		unless ($lavoro->[0]->step(
			ACTION => 'FINE_LAVORI',
			PROPERTIES => {
				startWorkDate => $rowFinal{startDate},
				endWorkDate => $rowFinal{endDate},
			}
		)){
			my $message = "Riga $i => KO: Errore in step LAVORO ".$api_art->last_error();
			$csv->print($out, [ @fields, $message ]);
			$logger->error($message);
			$api_art->_dbh()->do("rollback to savepoint mig_fibercop");
			$api_art->cancel();
			next CSV_LOOP;
		};
	}

	my $message = "Riga $i => OK: creata attività LAVORO ".$lavoro->[0]->id()." in stato ".$lavoro->[0]->get_current_status_name();
	$logger->info($message);

	if ($commit){
		$api_art->save();
	} else	{
		$api_art->_dbh()->do("rollback to savepoint mig_fibercop");
		$api_art->cancel();
	}
	
}

close $fh;
close $out or warn "Error - $out_file: $!";

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;

