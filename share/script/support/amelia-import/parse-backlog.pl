#!/usr/bin/env perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Text::CSV_XS;
use JSON;

use API::ART;
use API::ART::Engine;
use API::ART::Collection::System;
use API::ART::Collection::Activity;

use WPSOWORKS::MQ::Sender::Activity::LAVORO;

my $logger_name  = 'WPSOWORKS::BIN::parse-backlog';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_API_ART_ENGINE
	CANNOT_INIT_API_ART_COLLECTION_SYSTEM
	CANNOT_INIT_API_ART_CORE
	CANNOT_INIT_API_ART_COLLECTION_ACTIVITY
	CANNOT_INIT_WPSOWORKS_MQ_Sender_Activity_LAVORO
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]
	
	Gestisce i workOrder

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		--artid-core=<ARTID> - ARTID dell'istanza CORE delle API::ART a cui connettersi
		--api-user-core=<USER> - USER name dell'istanza CORE
		--api-password-core=<PASSWORD> - PASSWORD dello USER dell'istanza CORE
		-C, --customer-id=<CUSTOMER_ID> - Customer Id (OBBLIGATORIO)
		-T, --type=<TYPE_ID> - Type Id (OBBLIGATORIO)
		-f, --filename=<FILENAME> - Nome file da caricare (OBBLIGATORIO)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $artid_core, $api_script_user_core, $api_script_password_core, $commit, $log_config, $debug, $customerId, $typeId, $file );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

GetOptions (
	'help|h'				=>	\$help,
	'artid|a=s'				=>	\$artid,
	'api-user|u=s'			=>	\$api_script_user,
	'api-password|p=s'		=>	\$api_script_password,
	'artid-core=s'			=>	\$artid_core,
	'api-user-core=s'		=>	\$api_script_user_core,
	'api-password-core=s'	=>	\$api_script_password_core,
	'customer-id|C=s'		=>	\$customerId,
	'type|T=s'		=>	\$typeId,
	'file|f=s'				=>	\$file,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

usage( ERROR_WRONG_OPTIONS ) if (!defined $customerId || !defined $typeId || !defined $file || !defined $artid_core || !defined $api_script_user_core || !defined $api_script_password_core );

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID    => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $engine = eval { API::ART::Engine->new(ART => $api_art, BINDING => 0) };
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART::Engine: $@" );
	exit ERROR_CANNOT_INIT_API_ART_ENGINE;
}

my $collSystem = eval { API::ART::Collection::System->new(ART => $api_art) };
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART::Collection::System: $@" );
	exit ERROR_CANNOT_INIT_API_ART_COLLECTION_SYSTEM;
}

my $sender = eval { WPSOWORKS::MQ::Sender::Activity::LAVORO->instance(DB => $api_art->_dbh()) };
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOWORKS::MQ::Sender::Activity::LAVORO: $@" );
	exit ERROR_CANNOT_INIT_WPSOWORKS_MQ_Sender_Activity_LAVORO;
}

my $api_art_core;
my $collActivityCore;

eval {
	$api_art_core = API::ART->new(
		ARTID    => $artid_core
		,USER     => $api_script_user_core
		,PASSWORD => $api_script_password_core
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART CORE: $@" );
	exit ERROR_CANNOT_INIT_API_ART_CORE;
}

$api_art->_dbh()->do("alter session set CURSOR_SHARING = FORCE");

eval {
	$collActivityCore = API::ART::Collection::Activity->new(
		ART	=> $api_art_core
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART::Collection::Activity CORE: $@" );
	exit ERROR_CANNOT_INIT_API_ART_COLLECTION_ACTIVITY;
}

# recupero tutto l'elenco dei subappalti

my $subcontracts = $api_art_core->_dbh()->fetchall_hashref("
	select PARTNER \"companyCode\"
			,RAGIONE_SOCIALE \"companyName\"
	from core.mv_anagrafica_sub
");

my $teams = $api_art_core->_dbh()->fetchall_hashref("
	select username,
		cidsap,
		cognome,
		nome
	from core.V_R_TEAMS_FULL
");

# preparo query di check
my $sql = "
	select 1
	from v_sistemi s
		join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'ameliaId'
		join sistemi_relazione sr on sr.a = s.id
		join v_sistemi s2 on s2.id = sr.b
		join v_attivita vatt on vatt.id_sistema = sr.b
		join v_dt_sistemi dts2 on dts2.id_sistema = sr.b and dts2.nome = 'externalSequence'
	where s.tipo = 'NETWORK'
		and s2.tipo = 'LAVORO'
		and vatt.nome_Tipo_Attivita = 'LAVORO'
		and dts.valore = ?
		and dts2.valore = ?
		and rownum<2
";

my $prepareCheck = $api_art->_create_prepare('___PREPARE__CHECK___', $sql);

my $remap = _get_remapping();

# Apro file in scrittura per gli scarti
my $filenameScarti = $file."_SCARTI_".$api_art->_dbh()->get_sysdate().".csv";
open(my $fhSCARTI, '>', $filenameScarti) or die "Could not open file '$filenameScarti' $!";

# Read/parse CSV
my $csv = Text::CSV_XS->new(
	{
		binary     => 1
		,auto_diag  => 1 
		,sep_char   => ';'
	}
);
open my $fh, "<:encoding(utf8)", $file or die "$file: $!";
my $headers = $csv->getline($fh);
$csv->print ($fhSCARTI, $headers); print $fhSCARTI "\n";

#print Dumper($headers);

my $i = 0;
while (my $row = $csv->getline($fh)) {
	my $r = {};
	for (my $i=0; $i<scalar(@$headers); ++$i) {
		#print sprintf("%03d - %s : %s\n", $i, $headers->[$i], ": ", $row->[$i]);
		$r->{$headers->[$i]} = $row->[$i];
	}
	my $R = {};
	foreach my $k (sort keys %$remap) {
		$R->{$k} = $remap->{$k}->($r);
		#print $k, ': [', ($R->{$k} or ''), "]\n";
	}
	$r = $R;

	$i++;
	
	my $ameliaId = $r->{ameliaId};
	my $properties = $r;
	my $workActivity;

	# in questo caso non emetto messaggio e non scrivo nel file perchè devono essere del tutto ignorate
	if (defined $properties->{__MIG} && $properties->{__MIG} eq 'NO'){
		next;
	}
	
	unless (defined $ameliaId){
		$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
		$logger->warn( "Id Amelia mancante (riga ".$i.")" );
		next;
	}
	
	unless (defined $properties->{externalSequence}){
		$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
		$logger->warn( "externalSequence mancante (riga ".$i.")" );
		next;
	}
	
	my @bind_params = (
		$ameliaId,
		$properties->{externalSequence}
	);
	
	# verifico se il record è già stato elaborato
	my $res = $prepareCheck->fetch_minimalized(@bind_params);
	if ($res){
		$logger->info( "Lavoro con id_amelia ".$ameliaId." e externalSequence ".$properties->{externalSequence}." già gestito: nulla da fare (riga ".$i.")" );
		next;
	}
	
	# recupero il subocontractName/teamName
	if (defined $properties->{subContractCode}){
		my @foundSub = grep {$_->{companyCode} eq $properties->{subContractCode}} @{$subcontracts};
		$properties->{subContractName} = $foundSub[0]->{companyName} if scalar @foundSub && defined $foundSub[0]->{companyName};
	} elsif (defined $properties->{teamId}){
		my @foundTeam = grep {$_->{CIDSAP} eq $properties->{teamId}} @{$teams};
		$properties->{teamName} = $foundTeam[0]->{COGNOME}.' '.(substr($foundTeam[0]->{NOME},0, 1)).'. '.$foundTeam[0]->{CIDSAP} if scalar @foundTeam && defined $foundTeam[0]->{CIDSAP};
	} else {
		$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
		$logger->warn( "teamId o subContractCode non presenti per id_amelia ".$ameliaId." (riga ".$i.")" );
		next;
	}
	
	my $stato; 
	
	if ($customerId eq 'TIM' && $typeId ne 'ROE'){
		# verifico la presenza dello stato
		if (defined $properties->{__STATO_LAVORO}){
			if ($properties->{__STATO_LAVORO} !~/^(APERTE|CHIUSE)$/){
				$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
				$logger->warn( "Stato lavoro ".$properties->{__STATO_LAVORO}." non previsto per id_amelia ".$ameliaId." (riga ".$i.")" );
				next;
			}
		} else {
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->warn( "Stato lavoro non presente per id_amelia ".$ameliaId." (riga ".$i.")" );
			next;
		}
		
		$stato = $properties->{__STATO_LAVORO} eq 'CHIUSE' ? 'ESPLETATO' :
			$properties->{teamName} ? 'IN_LAVORAZIONE_SIRTI' : 'IN_LAVORAZIONE_SUBAPPALTO';
		
		# recupero la network con lo stesso ameliaId
		my $networks_id_amelia = $collSystem->find_object(
			SYSTEM_TYPE_NAME => ['NETWORK'],
			PROPERTIES => {
				ameliaId => $ameliaId
			},
			LIMIT => 1
		);
		unless (scalar @{$networks_id_amelia}){
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->warn( "Network con id_amelia ".$ameliaId." non presente a sistema (riga ".$i.")" );
			next;
		}
		
		my $system_description;
		my $system_groups;
		my $parent_system;
			
		$properties->{"workType"} = 'Network';
		
		my $network = $networks_id_amelia->[0];
		
		my $networkProperties = $network->property();
		
		$properties->{contractId} = $networkProperties->{contractId};
		
		if ($properties->{contractId} eq 'FTTH'){
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->warn( "Id_amelia ".$ameliaId." su network FTTH (riga ".$i.")" );
			next;
		}
		
		$system_description = join("-", $networkProperties->{customerId}, $networkProperties->{contractId}, $networkProperties->{networkId});
		
		$system_groups = $network->info('GROUPS');
		
		# aggiungo i parametri obbligatori
		for ("customerId", "contractId", "networkId"){
			$properties->{$_} = $networkProperties->{$_};
		};
		$properties->{"projectId"} = $properties->{"networkId"};
		$properties->{"assetId"} = [$properties->{"networkId"}];
		
		# dalla network recupero tutti i sistemi figlio di tipo LAVORO
		my $children = $network->get_children(SYSTEM_TYPE_NAME => ['LAVORO']);

		$parent_system = $network;
		
		$api_art->_dbh()->do( "savepoint parsebacklog" );
		
		my $res = $engine->handle(
			SYSTEM_TYPE_NAME			=> 'LAVORO',
			SYSTEM_DESCRIPTION			=> $system_description,
			SYSTEM_GROUPS				=> $system_groups,
			OBJECT_TYPE_NAME			=> 'WPSOWORKS',
			UPDATE_SYSTEM_PROPERTIES	=> 1,
			ACTIVITY_TYPE_NAME			=> 'LAVORO',
			ACTIVITY_DESCRIPTION		=> 'Caricamento massivo per ID_AMELIA '.$ameliaId,
			STATUS_NAME					=> $stato,
			VIRTUAL_ACTION_NAME			=> '__ALLINEAMENTO__',
			SAVE_ALL_PROPERTIES			=> 1,
			PROPERTIES					=> $properties
		);
		unless ( defined $res ) {
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->error( "Impossibile creare il lavoro: ".$api_art->last_error() );
			next;
		}
		
		$workActivity = $engine->get_last_activity();
		
		# l'oggetto creato deve diventare figlio
		unless (defined $parent_system->adopt_children(CHILDREN => [$workActivity->system()])){
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$api_art->_dbh()->do( "rollback to savepoint parsebacklog" );
			$logger->error( "Errore in fase di adozione: ".$api_art->last_error() );
			next;
		}
		
	} elsif ($customerId eq 'TIM' && $typeId eq 'ROE'){
		
		$stato = 'ESPLETATO';
		#$stato = $properties->{__STATO_ROE} =~/^(AN|CT|FL|RL)$/ ? 'ESPLETATO' :
		#	$properties->{teamName} ? 'IN_LAVORAZIONE_SIRTI' : 'IN_LAVORAZIONE_SUBAPPALTO';
		
		# recupero l'attività ROE con lo stesso ameliaId
		my $roes_id_amelia = $collActivityCore->find_object(
			ACTIVITY_TYPE_NAME_EQUAL => 'ROE',
			SYSTEM_PROPERTIES_EQUAL => {
				ameliaId => $ameliaId
			},
			LIMIT => 1
		);
		unless (scalar @{$roes_id_amelia}){
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->warn( "ROE con id_amelia ".$ameliaId." non presente su CORE (riga ".$i.")" );
			next;
		}
			
		$properties->{"workType"} = 'ROE';
		
		my $roe = $roes_id_amelia->[0];
		
		my $roeProperties = $roe->property();
		
		# aggiungo i parametri obbligatori
		$properties->{customerId} = $customerId;
		$properties->{contractId} = $roe->system_property('contractId');
		for ("networkId"){
			$properties->{$_} = $roeProperties->{$_} if defined $roeProperties->{$_};
		};
		$properties->{"projectId"} = $properties->{"networkId"} if exists $properties->{"networkId"};
		$properties->{"assetId"} = [$roe->id()];
		
		my $system_groups = [
			'ADMIN',
			'PM_'.$customerId.'_'.$properties->{contractId},
			'CL_'.$properties->{"workingGroupCode"}
		];
		
		$api_art->_dbh()->do( "savepoint parsebacklog" );
		
		$properties->{$properties->{__WORK_TYPE}} = 1 if (defined $properties->{__WORK_TYPE});
		
		my $res = $engine->handle(
			SYSTEM_TYPE_NAME			=> 'LAVORO',
			SYSTEM_DESCRIPTION			=> join("-", $customerId, $properties->{contractId}, $api_art->_dbh()->get_sysdate()),
			SYSTEM_GROUPS				=> $system_groups,
			OBJECT_TYPE_NAME			=> 'WPSOWORKS',
			UPDATE_SYSTEM_PROPERTIES	=> 1,
			ACTIVITY_TYPE_NAME			=> 'LAVORO',
			ACTIVITY_DESCRIPTION		=> 'Caricamento massivo per ID_AMELIA '.$ameliaId,
			STATUS_NAME					=> $stato,
			VIRTUAL_ACTION_NAME			=> '__ALLINEAMENTO__',
			SAVE_ALL_PROPERTIES			=> 1,
			PROPERTIES					=> $properties
		);
		unless ( defined $res ) {
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$logger->error( "Impossibile creare il lavoro: ".$api_art->last_error() );
			next;
		}
		
		$workActivity = $engine->get_last_activity();
		
	}
	
	# Invio external_sync
	my $property = $workActivity->property();
	my $system_property = $workActivity->system()->property();
	
	my $data = {
		SOURCE_REF => $workActivity->id()
		, TYPE => uc($system_property->{workType})
		, DATA => {
			ACTIVITY_ID				=> $workActivity->id(),
			ACTIVITY				=> to_json($workActivity->dump()),
			STATUS					=> $workActivity->get_current_status_name(),
			customerId				=> $property->{customerId},
			contractId				=> $property->{contractId},
			updateDatabase			=> $system_property->{updateDatabase},
			test					=> $system_property->{test},
			junction				=> $system_property->{junction},
			civil					=> $system_property->{civil},
			cableLaying				=> $system_property->{cableLaying},
			survey					=> $system_property->{survey},
			updateDatabaseF1		=> $system_property->{updateDatabaseF1},
			updateDatabaseF2		=> $system_property->{updateDatabaseF2},
			opticalConnectionOSU	=> $system_property->{opticalConnectionOSU},
			opticalConnectionOLT	=> $system_property->{opticalConnectionOLT},
			restoration				=> $system_property->{restoration},
			design					=> $system_property->{design},
			patchCord				=> $system_property->{patchCord},
		}
	};
	
	$data->{DATA}->{projectId} = $property->{projectId} if defined $property->{projectId};
	$data->{DATA}->{assetId} = $system_property->{assetId} if defined $system_property->{assetId};
	
	unless (defined $sender->notify_work_open(%{$data})){
		$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
		$api_art->_dbh()->do( "rollback to savepoint parsebacklog" );
		$logger->error( "Errore in fase di invio external_sync open: ".$api_art->last_error() );
		next;
	};
	
	if ($workActivity->is_closed()){
		$data->{SCHEDULE_DATE} = $api_art->_dbh()->fetch_minimalized("select to_char(sysdate+2/24,".$api_art->_dbh()->quote($api_art->get_default_date_format()).") from dual");
		unless (defined $sender->notify_work_close_ok(%{$data})){
			$csv->print ($fhSCARTI, $row); print $fhSCARTI "\n";
			$api_art->_dbh()->do( "rollback to savepoint parsebacklog" );
			$logger->error( "Errore in fase di invio external_sync open: ".$api_art->last_error() );
			next;
		};
	}
	
	$logger->info( "Creata attività LAVORO con id ".$workActivity->id()." (".$workActivity->get_current_status_name().") (riga ".$i.")");
	#print STDERR Dumper $workActivity->property();
	#print STDERR Dumper $workActivity->system_property();
	if ($commit){
		$api_art->save() 
	} else {
		$api_art->cancel()
	}
}

close $fh;
close $fhSCARTI;

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;


sub trim {
	my $s = shift;
	return unless defined $s;
	$s =~ s/^\s*?//; 
	$s =~ s/\s*?$//; 
	return $s;
}

sub date_convert {
	my $date = shift;
	return undef unless $date;
	my ($d, $m, $y);
	($d, $m, $y) = split(/[\/\- ]/, $date);
	return sprintf("%4d-%02d-%02d", $y, $m, $d);
}

sub _get_remapping {

	my $remap = {};
	
	# Nota: va introdotto un nuovo dato tecnico 
	$remap->{'ameliaId'} = sub { trim($_[0]->{'PRINTERV'}) };
	
	# prefissare con "10"
	$remap->{'teamId'} = sub {
		return undef unless trim($_[0]->{'IDSQUADRA'});
		return '10'.trim($_[0]->{'IDSQUADRA'});
	};
	
	# paddare a 10 
	$remap->{'subContractCode'} = sub {
		return undef unless trim($_[0]->{'FORN'});
		return sprintf("%010d", trim($_[0]->{'FORN'}))
	};
	
	$remap->{'__DATA_CREAZIONE'} = sub { return date_convert(trim($_[0]->{'DAASS'})) };
	
	$remap->{'startPlannedDate'} = sub {
		return date_convert(trim($_[0]->{'DAIESECPREV'}))
			if $_[0]->{'FORN'};
		return undef;
	};
	$remap->{'slaStartDate'} = sub {
		return date_convert(trim($_[0]->{'DAIESECPREV'}))
			if $_[0]->{'IDSQUADRA'};
		return undef;
	};
	
	# prefissare con "10"
	$remap->{'workingGroupCode'} = sub {
		my $wgc = trim((split(/\./, $_[0]->{'CC'}))[0]);
		$wgc = $wgc =~/^10/ ? $wgc : '10'.$wgc;
		$wgc = '106454' if $wgc eq '106453'; # NB: inglobato
		return $wgc;
	};
	
	
	$remap->{'externalSequence'} = sub { trim($_[0]->{'OPERAZIONE'}) };
	
	$remap->{'maker'} = sub {
		if (defined trim($_[0]->{'FORN'})){
			return 'Subcontract'
		} else {
			return 'Team'
		}
	};
	
	$remap->{'__STATO_LAVORO'} = sub { trim($_[0]->{'Fasi APERTE/CHIUSE'})};
	
	$remap->{'__MIG'} = sub { trim($_[0]->{'MIG SI/NO'}) };
	
	$remap->{'__WORK_TYPE'} = sub {
		my $tipo_lavoro = trim($_[0]->{'FASE'});
		
		my $map = {
			"1.1 Walk Out" => "survey", # Sopralluogo
			"1.3 RFC" => "civil", # Lavori civili
			"1.4 RFJ" => "cableLaying", # Posa cavi
			"1.5 RFU" => "junction", # Giunzione
			"1.6 Banche Dati" => "updateDatabase", # Agg. bache dati
		};
		
		return undef unless defined $tipo_lavoro;
		
		return $map->{$tipo_lavoro};
	};
	
	$remap->{'__STATO_ROE'} = sub { 
		return trim((split(/\./, $_[0]->{'STATO_ROE'}))[0])
			if defined $_[0]->{'STATO_ROE'};
		return undef;
	};
	
	return $remap;

}

