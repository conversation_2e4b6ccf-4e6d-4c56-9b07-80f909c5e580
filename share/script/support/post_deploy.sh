#!/bin/bash

chmod 755 \
  $ETC/profile.sh \
  $ROOT/share/script/sync_data.pl \
  $ROOT/share/script/lavoro_quarantena.pl \
  $ROOT/share/script/manage_workingGroupCode.pl

cd $ROOT/bin && \
  rm -f \
    sync_data \
    lavoro_quarantena \
    manage_workingGroupCode \
    api-art-activity-stream-lavoro \
    api-art-activity-stream-as-built \
    && \
  ln -s ../share/script/sync_data.pl sync_data \
  && \
  ln -s ../share/script/lavoro_quarantena.pl lavoro_quarantena \
  && \
  ln -s ../share/script/manage_workingGroupCode.pl manage_workingGroupCode \
  &&
  ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-lavoro \
  &&
  ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-as-built \


# funzionalità rimossa: lasciato rm per fare in modo che al primo rilascio cancelli il broken link
cd $ROOT/bin && \
  rm -f \
    crea_nuovi_macro_lavori

