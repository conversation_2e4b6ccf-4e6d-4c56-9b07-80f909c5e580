#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);
use MIME::Base32;

use API::ART;

my $logger_name  = 'WPSOWORKS::BIN::allinea_ringId_base32';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_CUSTOMER_ID_OR_CONTRACT_ID
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	UPDATING
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0	<parametri> <customerId> <contractId>
	
	<opzioni>:
		--project-id=<projectId> - Allinea un singolo progetto
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOWORKS_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	
";
	exit $rc;
}

usage( 1 ) if scalar(@ARGV) < 2;

my ( $help,$customerId,$contractId,$artid,$api_script_user,$api_script_password,$commit,$log_config,$debug,$project_id );

$artid                    = $ENV{WPSOWORKS_ARTID};
$api_script_user          = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password      = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config               = $ENV{LOG4PERL_CONF};
my $end_string = '===== END ======================================================================';

if ( defined $ENV{WPSOWORKS_TRANSACTION_MODE} && $ENV{WPSOWORKS_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>					\$help,
	'project-id=s' =>			\$project_id,
	'artid|a=s' =>				\$artid,
	'api-user|u=s' =>			\$api_script_user,
	'api-password|p=s' =>		\$api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

$customerId = $ARGV[0];
$contractId = $ARGV[1];
usage( ERROR_MISSING_CUSTOMER_ID_OR_CONTRACT_ID )
	unless defined $customerId || defined $contractId;

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

my $tmp_log_file_prefix = $customerId . '_' . $contractId . '_';
$tmp_log_file_prefix =~ s/ /_/g;
$ENV{LOG_FILE_PREFIX} = $tmp_log_file_prefix;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID     => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $system_prepare_name = 'ALLINEA_RINGID_BASE32_DT';
my $p_sistemi = $api_art->_create_prepare($system_prepare_name, 
	"update dati_Tecnici
	set descrizione = ?
	where id_sistema = ?
	and id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_tecnici where nome = 'ringId')
	and descrizione = ?
	"
);

my $activity_prepare_name = 'ALLINEA_RINGID_BASE32_DTA';
my $p_attivita = $api_art->_create_prepare($activity_prepare_name, 
	"update dati_Tecnici_attivita
	set descrizione = ?
	where id_attivita = ?
	and id_tipo_Dato_Tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_Dati_tecnici_attivita where descrizione = 'ringId')
	and descrizione = ?
	"
);

my $newRingId = {};

my $results_sistemi = $api_art->_dbh()->fetchall_hashref("
	select dts.id_sistema, dts.valore \"ringId\", dts2.valore \"ring\" From v_dt_sistemi dts
	  join v_dt_Sistemi dts2 on dts2.id_sistema = dts.id_sistema
	  join v_dt_sistemi dts3 on dts3.id_sistema = dts.id_sistema
	  join v_dt_sistemi dts4 on dts4.id_sistema = dts.id_sistema
	  ".(defined $project_id ? 'join v_dt_sistemi dts5 on dts5.id_sistema = dts.id_sistema' : '')."
	where dts.nome = 'ringId'
	and dts2.nome = 'ring'
	and dts3.nome = 'customerId'
	and dts4.nome = 'contractId'
	".(defined $project_id ? 'and dts5.nome = \'projectId\'' : '')."
	and dts3.valore = ".$api_art->_dbh()->quote($customerId)."
	and dts4.valore = ".$api_art->_dbh()->quote($contractId)."
	".(defined $project_id ? 'and dts5.valore = '.$api_art->_dbh()->quote($project_id) : '')."
");

$logger->info("Inizio aggiornamento sistemi");
my $systems_worked = 0;
for my $r (@{$results_sistemi}){
	$newRingId->{$r->{ring}} = encode_base32($r->{ring}) unless defined $newRingId->{$r->{ring}};
	my @bind_params = $newRingId->{$r->{ring}};
	push @bind_params, $r->{ID_SISTEMA};
	push @bind_params, $r->{ringId};
	my $result = $api_art->_create_prepare($system_prepare_name)->execute(@bind_params) or croak 'Errore: '.$api_art->_dbh()->get_errormessage();
	if ($result == 1){
		$systems_worked++;
		$logger->info( "Aggiornamento sistema ".$r->{ID_SISTEMA}." OK: ringId da ".$r->{ringId}." a ".$newRingId->{$r->{ring}}." relativo a ring ".$r->{ring}  );
	} else {
		$logger->error( "Aggiornato sistema ".$r->{ID_SISTEMA}." KO: atteso aggiornamento di una riga, restituito ".$result);
		$api_art->cancel();
		exit ERROR_UPDATING;
	}
}

my $results_attivita = $api_art->_dbh()->fetchall_hashref("
	select dta.id_Attivita, dta.valore \"ringId\", dta2.valore \"ring\"
	from v_dta dta
	  join v_dta dta2 on dta2.id_attivita = dta.id_attivita
	  join v_dta dta3 on dta3.id_attivita = dta.id_attivita
	  join v_dta dta4 on dta4.id_attivita = dta.id_attivita
	  ".(defined $project_id ? 'join v_dta dta5 on dta5.id_attivita = dta.id_attivita' : '')."
	where dta.nome = 'ringId'
	and dta2.nome = 'ring'
	and dta3.nome = 'customerId'
	and dta4.nome = 'contractId'
	".(defined $project_id ? 'and dta5.nome = \'projectId\'' : '')."
	and dta3.valore = ".$api_art->_dbh()->quote($customerId)."
	and dta4.valore = ".$api_art->_dbh()->quote($contractId)."
	".(defined $project_id ? 'and dta5.valore = '.$api_art->_dbh()->quote($project_id) : '')."
");

$logger->info("Inizio aggiornamento attività");
my $activities_worked = 0;
for my $r (@{$results_attivita}){
	$newRingId->{$r->{ring}} = encode_base32($r->{ring}) unless defined $newRingId->{$r->{ring}};
	my @bind_params = $newRingId->{$r->{ring}};
	push @bind_params, $r->{ID_ATTIVITA};
	push @bind_params, $r->{ringId};
	my $result = $api_art->_create_prepare($activity_prepare_name)->execute(@bind_params) or croak 'Errore: '.$api_art->_dbh()->get_errormessage();
	if ($result == 1){
		$activities_worked++;
		$logger->info( "Aggiornamento attivita ".$r->{ID_ATTIVITA}." OK: ringId da ".$r->{ringId}." a ".$newRingId->{$r->{ring}}." relativo a ring ".$r->{ring}  );
	} else {
		$logger->error( "Aggiornato attivita ".$r->{ID_ATTIVITA}." KO: atteso aggiornamento di una riga, restituito ".$result);
		$api_art->cancel();
		exit ERROR_UPDATING;
	}
}

$logger->info( "Aggiornati ".$systems_worked." sistemi");
$logger->info( "Aggiornate ".$activities_worked." attività");

if( $commit ) {
	$logger->info( "Eseguo la commit delle modifiche fatte sul db" );
	$api_art->save();
} else {
	$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
	$api_art->cancel();
}

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;