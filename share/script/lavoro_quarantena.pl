#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);

use API::ART;
use API::ART::Collection::Activity;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

my $logger_name  = 'WPSOWORKS::BIN::lavoro_quarantena';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_ACTIVITY_COLLECTION
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]
	
	...

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOWORKS_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-D, --daemon=<secondi> - prepara l'ambiente per eseguire come daemon effettuando il polling ogni <secondi>
			utilizzare start_stop_daemon per lo start e lo stop
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $daemon, $waitsec );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

if ( defined $ENV{WPSOWORKS_TRANSACTION_MODE} && $ENV{WPSOWORKS_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'daemon|D=i'          => sub {
		$daemon     = 1;
		$waitsec    = $_[1];
		die "Con l'opzione --daemon, <secondi> deve essere > 0\n"
			unless $waitsec > 0;
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

HEAD: while ( $keep_going ) {
	
	$keep_going = 0
		unless $daemon;
	
	$logger->debug( "Inizializzo API::ART" );
	my $api_art;
	eval {
		$api_art = API::ART->new(
			ARTID    => $artid
			,USER     => $api_script_user
			,PASSWORD => $api_script_password
			,AUTOSAVE => 0
			,DEBUG    => $debug
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare API::ART: $@" );
		exit ERROR_CANNOT_INIT_API_ART;
	}
	
	my $collection = eval { API::ART::Collection::Activity->new( ART => $api_art ) };
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare API::ART::Collection::Activity: $@" );
		exit ERROR_CANNOT_INIT_ACTIVITY_COLLECTION;
	}
	
	my $config = [ 
		 {
			 ACTIVITY_TYPE_NAME => [ 'LAVORO' ]
			,STATUS_IN => $api_art->get_action_status( 'LAVORO' , 'TIMEOUT' )
			,CALLBACK => sub {
				my $activity = shift;
				$logger->info( '--------------------------------------------------------------------------------' );
				$api_art->_dbh()->do("savepoint BEGIN_ACTIVITY");
				$logger->info("Working on activity ".$activity->id().", type: WO, status: ".$activity->status_name());
				my $date = $api_art->_dbh()->fetch_minimalized(
					"
						select to_char(to_date(".$api_art->_dbh()->quote($activity->info('LAST_VAR_DATE')).",".$api_art->_dbh()->quote($api_art->get_default_date_format()).")+".$ENV{ACTIVITY_LAVORO_DAYS_QUARANTENA}.",".$api_art->_dbh()->quote($api_art->get_default_date_format()).") from dual
					"
				);
				$logger->info("DATE: ".$date);
				if( $api_art->get_sysdate() ge $date ) {
					if($activity->step(
						 ACTION => 'TIMEOUT'
						,DESCRIPTION => 'Raggiunta data timeout'
					)) {
						$logger->info("Effettuato step TIMEOUT");
						if( $commit ) {
							$logger->debug( "Eseguo la commit delle modifiche fatte sul db" );
							$api_art->save();
						} else {
							$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
						}
					} else {
						$logger->error("Impossibile effettuare step TIMEOUT: ".$api_art->last_error());
						$api_art->_dbh()->do("rollback to savepoint BEGIN_ACTIVITY");
					}
				} else {
					$logger->info("Attivita' non ancora pronta per lavorazione");
				}
			}
		}
	];
	
	for my $conf ( @$config ) {
		$collection->find_exec( %{$conf} );
	}
	
	if ( $daemon ) {
		$logger->info( '--------------------------------------------------------------------------------' );
		$logger->info( "Ho lavorato tutte le attivita', aspetto $waitsec secondi" );
		$api_art->disconnect();
		undef $collection;
		undef $api_art;
		sleep $waitsec;
		next HEAD;
	}
	
	$logger->info( '===== END ======================================================================' );

}

exit ERROR_OK;

