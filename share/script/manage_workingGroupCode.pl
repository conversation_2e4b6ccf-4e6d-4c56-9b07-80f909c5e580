#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);

use API::ART;
use API::ART::Collection::Activity;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

my $logger_name  = 'WPSOWORKS::BIN::manage_workingGroupCode';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_CREATE_NEW_GROUP
	CANNOT_INSERT_CDL
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]
	
	Allineamento centri lavoro

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOWORKS_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOWORKS_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback
		-D, --daemon=<secondi> - prepara l'ambiente per eseguire come daemon effettuando il polling ogni <secondi>
			utilizzare start_stop_daemon per lo start e lo stop
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $daemon, $waitsec );

$artid               = $ENV{WPSOWORKS_ARTID};
$api_script_user     = $ENV{WPSOWORKS_SCRIPT_USER};
$api_script_password = $ENV{WPSOWORKS_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'daemon|D=i'          => sub {
		$daemon     = 1;
		$waitsec    = $_[1];
		die "Con l'opzione --daemon, <secondi> deve essere > 0\n"
			unless $waitsec > 0;
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

HEAD: while ( $keep_going ) {
	
	$keep_going = 0
		unless $daemon;
	
	$logger->debug( "Inizializzo API::ART" );
	my $api_art;
	eval {
		$api_art = API::ART->new(
			ARTID    => $artid
			,USER     => $api_script_user
			,PASSWORD => $api_script_password
			,AUTOSAVE => 0
			,DEBUG    => $debug
		);
	};
	if ( $@ ) {
		$logger->fatal( "Impossibile inizializzare API::ART: $@" );
		exit ERROR_CANNOT_INIT_API_ART;
	}

	# recupero tutti i gruppi a sistema
	my $groups = $api_art->enum_group();
	
	my $db = $api_art->_dbh();
	
	# recupero i centri lavoro dal db di anagrafiche
	my $centri_lavoro = $db->fetchall_hashref("
		select distinct *
		from works.v_r_centri_lavoro
	");
	
	# creo o aggiorno i relativi gruppi
	for my $centro_lavoro(@{$centri_lavoro}){
		my $centro_lavoro_name = 'CL_'.$centro_lavoro->{id}; 
		
		# verifico se il gruppo esiste già o no
		if (exists $groups->{$centro_lavoro_name}){
			# l'aggiornamento del gruppo per ora non è supportato dalle API::ART
			$logger->info( "Gruppo ".$centro_lavoro_name." già presente a sistema" );
		} else { # creo il nuovo gruppo
			my $new_group = $api_art->create_group(
				NAME => $centro_lavoro_name,
				DESCRIPTION => $centro_lavoro->{name},
				IS_AUTOGENERATED => 1
			);
			
			unless (defined $new_group) {
				$logger->fatal( "Impossibile creare gruppo ".$centro_lavoro_name.": ".$api_art->last_error() );
				exit ERROR_CANNOT_CREATE_NEW_GROUP;
			}
			
			$logger->info( "Creato gruppo ".$centro_lavoro_name );
		}
		
	}
	
	# se un centro lavoro non deve essere più presente per ora il gruppo su ART rimane cmq
	
	if( $commit ) {
		$logger->debug( "Eseguo la commit delle modifiche fatte sul db" );
		$api_art->save();
	} else {
		$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
	}
	
	if ( $daemon ) {
		$logger->info( '--------------------------------------------------------------------------------' );
		$logger->info( "Ho lavorato tutte le attivita', aspetto $waitsec secondi" );
		$api_art->disconnect();
		undef $db;
		undef $api_art;
		sleep $waitsec;
		next HEAD;
	}
	
	$logger->info( '===== END ======================================================================' );

}


exit ERROR_OK;

