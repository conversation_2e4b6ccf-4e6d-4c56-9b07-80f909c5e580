// Generated on 2016-04-12 using generator-angular 0.15.1
'use strict';

var semver = require('semver');
var exec = require('child_process').execSync;

// # Globbing
// for performance reasons we're only matching one level down:
// 'test/spec/{,*/}*.js'
// use this if you want to recursively match all subfolders:
// 'test/spec/**/*.js'

var path = require('path');

module.exports = function (grunt) {

  // Time how long tasks take. Can help when optimizing build times
  require('time-grunt')(grunt);

  // Automatically load required Grunt tasks
  require('jit-grunt')(grunt, {
    useminPrepare: 'grunt-usemin',
    ngtemplates: 'grunt-angular-templates',
    cdnify: 'grunt-google-cdn',
    exec: 'grunt-exec',
    less: 'grunt-contrib-less',
    version: 'grunt-version',
    prompt: 'grunt-prompt'
  });

  var pkg = grunt.file.readJSON('package.json');

  var versionUpgradeChoices = [
    {
      name: 'Prerelease (' + semver.inc(pkg.version, 'prerelease') + ')',
      value: 'prerelease'
    },
    {
      name: 'Prepatch (' + semver.inc(pkg.version, 'prepatch') + ')',
      value: 'prepatch'
    },
    {
      name: 'Patch (' + semver.inc(pkg.version, 'patch') + ')',
      value: 'patch'
    },
    {
      name: 'Preminor (' + semver.inc(pkg.version, 'preminor') + ')',
      value: 'preminor'
    },
    {
      name: 'Minor (' + semver.inc(pkg.version, 'minor') + ')',
      value: 'minor'
    },
    {
      name: 'Premajor (' + semver.inc(pkg.version, 'premajor') + ')',
      value: 'premajor'
    },
    {
      name: 'Major (' + semver.inc(pkg.version, 'major') + ')',
      value: 'major'
    },
    {
      name: 'Custom ...',
      value: 'custom'
    }
  ];

  // Configurable paths for the application
  var appConfig = {
    app: require('./bower.json').appPath || 'app',
    dist: 'dist'
  };

  // Define the configuration for all the tasks
  grunt.initConfig({

    // version upgrade
    version: {
      upgradeVersionPackageJson: {
        options: {
          release: 'prerelease'
        },
        src: ['package.json']
      }
    },

    // build on "our own way"
    prompt: {
      continueDespiteWTDirty: {
        options: {
          questions: [
            {
              config: 'echo.continueDespiteWTDirty',
              type: 'confirm',
              default: false,
              message: 'Your Working Tree is dirty: do you wanna continue?'
            }
          ],
          then: function() {
            if(!grunt.config('echo.continueDespiteWTDirty')) {
              grunt.fail.fatal('User won\'t continue. Nothing will be done!');
            }
          }
        }
      },
      configVersionUpgrade: {
        options: {
          questions: [
            {
              config: 'version.upgradeVersionPackageJson.options.release',
              type: 'list',
              message: 'Version upgrade type',
              default: 'prerelease',
              choices: versionUpgradeChoices
            },
            {
              config: 'echo.customVersion',
              type: 'input',
              message: 'Please insert version',
              default: '',
              when: function(answers) {
                return answers['version.upgradeVersionPackageJson.options.release'] === 'custom';
              },
              validate: function(value) {
                return semver.valid(value) ? true : 'Please enter a valid version. See ' + 'http://semver.org/'.blue.underline + ' for more details.';
              }
            },
            {
              config: 'echo.continue',
              type: 'confirm',
              message: function(answers) {
                var newVersion; 
                if(answers['version.upgradeVersionPackageJson.options.release'] === 'custom') {
                  newVersion = answers['echo.customVersion'];
                } else {
                  newVersion = semver.inc(pkg.version, answers['version.upgradeVersionPackageJson.options.release']);
                }
                return 'Version will be upgraded to ' + newVersion + '\n' + 'Are you sure?';
              }
            }
          ],
          then: function() {
            if(!grunt.config('echo.continue')) {
              grunt.fail.fatal('User won\'t continue. Nothing will be done!');
            }
            if(grunt.config('version.upgradeVersionPackageJson.options.release') === 'custom') {
              grunt.config('version.upgradeVersionPackageJson.options.release', grunt.config('echo.customVersion'));
            } else {
              grunt.config('version.upgradeVersionPackageJson.options.release', semver.inc(pkg.version, grunt.config('version.upgradeVersionPackageJson.options.release')));
            }
          }
        }
      }
    },

    // utility for build on "our own way"
    exec: {
      chmodOptipngLinux: 'chmod 755 ./node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-optipng/node_modules/optipng-bin/vendor/optipng',
      chmodGifsicleLinux: 'chmod 755 ./node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-gifsicle/node_modules/gifsicle/vendor/gifsicle',
      deploy: 'git rm -r deploy && mkdir -p deploy && cp -a dist/* deploy/ && find deploy -type f | xargs git add',
      gitStatus: {
        cmd: function() {
          return 'git status';
        }
      },
      gitAdd: {
        cmd: function() {
          var filesToAdd = [
            'package.json',
            'app/scripts/directives/version/version.js'
          ];
          return 'git add ' + filesToAdd.join(' ');
        }
      },
      gitCommit: {
        cmd: function() {
          var msg = 'version upgrade e creazione deploy webapp rendicontazione versione ' + grunt.config('version.upgradeVersionPackageJson.options.release');
          return 'git commit -m "' + msg + '"';
        }
      },
      gitPush: {
        cmd: function() {
          return 'git push';
        }
      }
    },

    'regex-replace': {
      version: {
        src: [
          'app/scripts/directives/version/version.js'
        ],
        actions: [
          {
            search: '(.value\\(\'version\', \')([^\']*)(\'\\); // DON\'T EDIT THIS LINE MANUALLY)',
            replace: function(fullStr, pre, version, post) {
              return pre + grunt.config('version.upgradeVersionPackageJson.options.release') + post;
            }
          }
        ]
      }
    },

    // Project settings
    yeoman: appConfig,

    // Watches files for changes and runs tasks based on the changed files
    watch: {
      bower: {
        files: ['bower.json'],
        tasks: ['wiredep']
      },
      js: {
    	files: ['<%= yeoman.app %>/scripts/**/*.js'],
        tasks: ['newer:jshint:all', 'newer:jscs:all'],
        options: {
          livereload: '<%= connect.options.livereload %>'
        }
      },
      jsTest: {
        files: ['test/spec/**/*.js'],
        tasks: ['newer:jshint:test', 'newer:jscs:test', 'karma']
      },
      styles: {
        files: ['<%= yeoman.app %>/styles/**/*.css'],
        tasks: ['newer:copy:styles', 'postcss']
      },
      less: {
        files: ['less/**/*.less'],
        tasks: ['less:development']
      },
      po: {
        files: ['po/*.po'],
        tasks: ['nggettext_compile_concat']
      },
      gruntfile: {
        files: ['Gruntfile.js']
      },
      livereload: {
        options: {
          livereload: '<%= connect.options.livereload %>'
        },
        files: [
          '<%= yeoman.app %>/**/*.html',
          '.tmp/styles/**/*.css',
          '<%= yeoman.app %>/images/**/*.{png,jpg,jpeg,gif,webp,svg}'
        ]
      }
    },

    // The actual grunt server settings
    connect: {
      options: {
        port: process.env.GRUNT_SERVE_PORT || 9000,
        // Change this to '0.0.0.0' to access the server from outside.
        hostname: 'localhost',
        livereload: process.env.GRUNT_LIVERELOAD_PORT || 35729
      },
      livereload: {
        options: {
          open: true,
          middleware: function (connect) {
            return [
              connect.static('.tmp'),
              connect().use(
                '/bower_components',
                connect.static('./bower_components')
              ),
              connect().use(
                '/app/styles',
                connect.static('./app/styles')
              ),
              connect().use(
                '/i18n/angular-locale.js',
                connect.static('./app/i18n/angular-locale.js.it')
              ),
              connect().use(
                '/i18n/art-locale.js',
                connect.static('./app/i18n/art-locale.js.it')
              ),
              connect.static(appConfig.app)
            ];
          }
        }
      },
      test: {
        options: {
          port: 9001,
          middleware: function (connect) {
            return [
              connect.static('.tmp'),
              connect.static('test'),
              connect().use(
                '/bower_components',
                connect.static('./bower_components')
              ),
              connect.static(appConfig.app)
            ];
          }
        }
      },
      dist: {
        options: {
          open: true,
          base: '<%= yeoman.dist %>'
        }
      }
    },

    // Make sure there are no obvious mistakes
    jshint: {
      options: {
        jshintrc: '.jshintrc',
        reporter: require('jshint-stylish')
      },
      all: {
        src: [
          'Gruntfile.js',
          '<%= yeoman.app %>/scripts/**/*.js'
        ]
      },
      test: {
        options: {
          jshintrc: 'test/.jshintrc'
        },
        src: ['test/spec/**/*.js']
      }
    },

    // Make sure code styles are up to par
    jscs: {
      options: {
        config: '.jscsrc',
        verbose: true
      },
      all: {
        src: [
          'Gruntfile.js',
          '<%= yeoman.app %>/scripts/**/*.js'
        ],
        filter: function(filepath) {
          return (path.join('app', 'scripts', 'l10n.js') !== filepath);
        }
      },
      test: {
        src: ['test/spec/**/*.js']
      }
    },

    // Empties folders to start fresh
    clean: {
      dist: {
        files: [{
          dot: true,
          src: [
            '.tmp',
            '<%= yeoman.dist %>/**/*',
            '!<%= yeoman.dist %>/.git**/*'
          ]
        }]
      },
      server: '.tmp'
    },

    // Add vendor prefixed styles
    postcss: {
      options: {
        processors: [
          require('autoprefixer-core')({browsers: ['last 1 version']})
        ]
      },
      server: {
        options: {
          map: true
        },
        files: [{
          expand: true,
          cwd: '.tmp/styles/',
          src: '**/*.css',
          dest: '.tmp/styles/'
        }]
      },
      dist: {
        files: [{
          expand: true,
          cwd: '.tmp/styles/',
          src: '**/*.css',
          dest: '.tmp/styles/'
        }]
      }
    },

    // Automatically inject Bower components into the app
    wiredep: {
      app: {
        src: ['<%= yeoman.app %>/index.html'],
        ignorePath:  /\.\.\//
      },
      test: {
        devDependencies: true,
        src: '<%= karma.unit.configFile %>',
        ignorePath:  /\.\.\//,
        fileTypes:{
          js: {
            block: /(([\s\t]*)\/{2}\s*?bower:\s*?(\S*))(\n|\r|.)*?(\/{2}\s*endbower)/gi,
              detect: {
                js: /'(.*\.js)'/gi
              },
              replace: {
                js: '\'{{filePath}}\','
              }
            }
          }
      }
    },

    // Renames files for browser caching purposes
    filerev: {
      dist: {
        src: [
          '<%= yeoman.dist %>/scripts/**/*.js',
          '<%= yeoman.dist %>/styles/**/*.css',
          '<%= yeoman.dist %>/images/**/*.{png,jpg,jpeg,gif,webp,svg}',
          '<%= yeoman.dist %>/styles/fonts/*'
        ]
      }
    },

    // Reads HTML for usemin blocks to enable smart builds that automatically
    // concat, minify and revision files. Creates configurations in memory so
    // additional tasks can operate on them
    useminPrepare: {
      html: '<%= yeoman.app %>/index.html',
      options: {
        dest: '<%= yeoman.dist %>',
        flow: {
          html: {
            steps: {
              js: ['concat', 'uglifyjs'],
              css: ['cssmin']
            },
            post: {}
          }
        }
      }
    },
    uglify: {
      options: {
        mangle: false // FIXME: workaround dovuto a bug nostri, probabilmente nei provider dei moduli dove mettiamo la resolve alle rotte
      }
    },
    // Performs rewrites based on filerev and the useminPrepare configuration
    usemin: {
      html: ['<%= yeoman.dist %>/**/*.html'],
      css: ['<%= yeoman.dist %>/styles/**/*.css'],
      js: ['<%= yeoman.dist %>/scripts/**/*.js'],
      options: {
        assetsDirs: [
          '<%= yeoman.dist %>',
          '<%= yeoman.dist %>/images',
          '<%= yeoman.dist %>/styles'
        ],
        patterns: {
          js: [[/(images\/[^''""]*\.(png|jpg|jpeg|gif|webp|svg))/g, 'Replacing references to images']]
        }
      }
    },

    // The following *-min tasks will produce minified files in the dist folder
    // By default, your `index.html`'s <!-- Usemin block --> will take care of
    // minification. These next options are pre-configured if you do not wish
    // to use the Usemin blocks.
    // cssmin: {
    //   dist: {
    //     files: {
    //       '<%= yeoman.dist %>/styles/main.css': [
    //         '.tmp/styles/{,*/}*.css'
    //       ]
    //     }
    //   }
    // },
    // uglify: {
    //   dist: {
    //     files: {
    //       '<%= yeoman.dist %>/scripts/scripts.js': [
    //         '<%= yeoman.dist %>/scripts/scripts.js'
    //       ]
    //     }
    //   }
    // },
    // concat: {
    //   dist: {}
    // },

    imagemin: {
      dist: {
        files: [{
          expand: true,
          cwd: '<%= yeoman.app %>/images',
          src: '**/*.{png,jpg,jpeg,gif}',
          dest: '<%= yeoman.dist %>/images'
        }]
      }
    },

    svgmin: {
      dist: {
        files: [{
          expand: true,
          cwd: '<%= yeoman.app %>/images',
          src: '**/*.svg',
          dest: '<%= yeoman.dist %>/images'
        }]
      }
    },

    htmlmin: {
      dist: {
        options: {
          collapseWhitespace: true,
          conservativeCollapse: true,
          collapseBooleanAttributes: true,
          removeCommentsFromCDATA: true
        },
        files: [{
          expand: true,
          cwd: '<%= yeoman.dist %>',
          src: ['*.html'],
          dest: '<%= yeoman.dist %>'
        }]
      }
    },

    ngtemplates: {
      dist: {
        options: {
          module: 'rendicontazione',
          htmlmin: '<%= htmlmin.dist.options %>',
          usemin: 'scripts/scripts.js'
        },
        cwd: '<%= yeoman.app %>',
        src: 'views/**/*.html',
        dest: '.tmp/templateCache.js'
      }
    },

    // ng-annotate tries to make the code safe for minification automatically
    // by using the Angular long form for dependency injection.
    ngAnnotate: {
      dist: {
        files: [{
          expand: true,
          cwd: '.tmp/concat/scripts',
          src: '*.js',
          dest: '.tmp/concat/scripts'
        }]
      }
    },

    // Replace Google CDN references
    cdnify: {
      dist: {
        html: ['<%= yeoman.dist %>/*.html']
      }
    },

    // Copies remaining files to places other tasks can use
    copy: {
      dist: {
        files: [{
          expand: true,
          dot: true,
          cwd: '<%= yeoman.app %>',
          dest: '<%= yeoman.dist %>',
          src: [
            '*.{ico,png,txt}',
            '*.html',
            'images/**/*.{webp}',
            'styles/fonts/**/*.*'
          ]
        }, {
          expand: true,
          cwd: '.tmp/images',
          dest: '<%= yeoman.dist %>/images',
          src: ['generated/*']
        }, {
          expand: true,
          cwd: 'bower_components/bootstrap/dist',
          src: 'fonts/*',
          dest: '<%= yeoman.dist %>'
        }, {
          expand: true,
          cwd: 'bower_components/components-font-awesome',
          src: 'fonts/*',
          dest: '<%= yeoman.dist %>'
        }]
      },
      wsfiles: {
        files: [{
          expand: true,
          cwd: '<%= yeoman.app %>/ws/',
          src: '**',
          dest: './dist/ws/'
        }]
      },
      i18nfiles: {
        files: [{
          expand: true,
          cwd: '<%= yeoman.app %>/i18n/',
          src: '*',
          dest: './dist/i18n/'
        }]
      },
      styles: {
        expand: true,
        cwd: '<%= yeoman.app %>/styles',
        dest: '.tmp/styles/',
        src: '**/*.css'
      },
      preparaAmbienteDevWin: {
        files: [{
          src: './contrib/preparaAmbienteDev/win/optipng-0.7.6-win32/optipng.exe',
          dest: './node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-optipng/node_modules/optipng-bin/vendor/optipng.exe'
        }, {
          src: './contrib/preparaAmbienteDev/win/gifsicle-1.88-win64/gifsicle.exe',
          dest: './node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-gifsicle/node_modules/gifsicle/vendor/gifsicle.exe'
        }]
      },
      preparaAmbienteDevLinux: {
        files: [{
          src: './contrib/preparaAmbienteDev/linux/optipng-0.7.6/optipng',
          dest: './node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-optipng/node_modules/optipng-bin/vendor/optipng'
        }, {
          src: './contrib/preparaAmbienteDev/linux/gifsicle-1.88/gifsicle',
          dest: './node_modules/grunt-contrib-imagemin/node_modules/imagemin/node_modules/imagemin-gifsicle/node_modules/gifsicle/vendor/gifsicle'
        }]
      }
    },

    // Run some tasks in parallel to speed up the build process
    concurrent: {
      server: [
        'copy:styles'
      ],
      test: [
        'copy:styles'
      ],
      dist: [
        'copy:styles',
        'imagemin',
        'svgmin'
      ]
    },

    // Test settings
    karma: {
      unit: {
        configFile: 'test/karma.conf.js',
        singleRun: true
      }
    },

    less : {
      development : {
        files : [
          {
            expand: true,
            cwd: './less',
            src: [ '*.less' ],
            dest: '<%= yeoman.app %>/styles/',
            ext: '.css'
          }
        ]
      }
    },

    concat: {
      l10n: {
        options: {
          banner: '\'use strict\';',
        },
        src: ['<%= yeoman.app %>/scripts/l10n.js'],
        dest: '<%= yeoman.app %>/scripts/l10n.js',
      },
    },

    'nggettext_extract': {
      pot: {
        files: {
          'po/rendicontazione.pot': [
            '<%= yeoman.app %>/views/**/*.html',
            '<%= yeoman.app %>/scripts/**/*.js'
          ]
        }
      },
    },
    'nggettext_compile': {
      all: {
        files: {
          '<%= yeoman.app %>/scripts/l10n.js': [
            'po/*.po'
          ]
        }
      },
    }
  });


  // Compile then start a connect web server
  grunt.registerTask('serve', function (target) {
    if (target === 'dist') {
      return grunt.task.run(['build', 'connect:dist:keepalive']);
    }

    grunt.task.run([
      'clean:server',
      'wiredep',
      'less:development',
      'nggettext_compile_concat',
      'concurrent:server',
      'postcss:server',
      'connect:livereload',
      'watch'
    ]);
  });

  grunt.registerTask('server', 'DEPRECATED TASK. Use the "serve" task instead', function (target) {
    grunt.log.warn('The `server` task has been deprecated. Use `grunt serve` to start a server.');
    grunt.task.run(['serve:' + target]);
  });

  grunt.registerTask('test', [
    'clean:server',
    'wiredep',
    'less:development',
    'concurrent:test',
    'postcss',
    'connect:test',
    'karma'
  ]);

  grunt.registerTask('build', [
    'nggettext_compile',
    'clean:dist',
    'wiredep',
    'useminPrepare',
    'less:development',
    'concurrent:dist',
    'postcss',
    'ngtemplates',
    'concat',
    'newer:jshint',
    'newer:jscs',
    'ngAnnotate',
    'copy:dist',
    'cdnify',
    'cssmin',
    'uglify',
    'filerev',
    'usemin',
    'htmlmin',
    'copy:wsfiles',
    'copy:i18nfiles'
  ]);

  grunt.registerTask('default', [
    'serve'
  ]);

  grunt.registerTask('preparaAmbienteDev:win', [
    'copy:preparaAmbienteDevWin'
  ]);

  grunt.registerTask('preparaAmbienteDev:linux', [
    'copy:preparaAmbienteDevLinux',
    'exec:chmodOptipngLinux',
    'exec:chmodGifsicleLinux'
  ]);

  grunt.loadNpmTasks('grunt-angular-gettext');

  grunt.registerTask('nggettext_compile_concat', [
    'nggettext_compile',
    'concat:l10n'
  ]);

  grunt.loadNpmTasks('grunt-regex-replace');

  // build on "our own way"
  grunt.registerTask('check-wt', function() {
    var isWTDirty = function() {
      return !/^0/.test(exec('git status --porcelain | wc -l').toString());
    };
    if(isWTDirty()) {
      grunt.log.writeln(('WARNING: Git Working Tree dirty').white.bold);
      grunt.task.run('prompt:continueDespiteWTDirty');
    } else {
      grunt.log.writeln(('Git Working Tree clean').white.bold);
    }
  });

  grunt.registerTask('print-current-version', function() {
    grunt.log.writeln(('Current version is ' + pkg.version).white.bold);
  });

  grunt.registerTask('build-plus-plus', [
    'exec:gitStatus',
    'check-wt',
    'print-current-version',
    'prompt:configVersionUpgrade',
    'version:upgradeVersionPackageJson',
    'regex-replace:version',
    'build',
    'exec:deploy',
    'exec:gitAdd',
    'exec:gitCommit',
    'exec:gitPush'
  ]);

};
