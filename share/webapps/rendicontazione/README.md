# RENDICONTAZIONE-GUI

This project is generated with [yo angular generator](https://github.com/yeoman/generator-angular)
version 0.15.1.

## Before you begin

```
npm install -g grunt-cli bower
npm install
bower install
grunt nggettext_compile_concat
grunt less:development
```

La procedura (*sperimentale*) che segue serve per preparare l'ambiente a superare le limitazioni di proxy, ecc.

* windows
```
grunt preparaAmbienteDev:win
```
* linux
```
grunt preparaAmbienteDev:linux
```

## Creazione build per rilascio in produzione o test

Il task `grunt build-plus-plus` crea la build e la porta nella cartella `deploy/` dove sarà servita dal web-server.

I ws a cui si collega la webapp sono sviluppati come script `perl APACHE::ASP` nella cartella `app/ws/` e verranno deployati nella cartella `deploy/ws/` con la procedura al punto precedente.

Per gestire il webserver *apache* utilizzare il comando:
```shell
apachectl.sh -C $ETC/httpd.RENDICONTAZIONE.conf <start|stop>
```

## Note per lo sviluppo

`grunt serve` lancia un http-server con livereload (porta 35729) in http://localhost:9000. E' possibile utilizzare un'altra porta per il server web configurandone il valore nella variabile d'ambiente `GRUNT_SERVE_PORT`, un'altra porta dove è in ascolto il livereload configurandone il valore nella variabile d'ambiente `GRUNT_LIVERELOAD_PORT`.

## Localizzazione

Il multi-lingua viene gestito con il modulo [https://angular-gettext.rocketeer.be/](angular-gettext).

Se vengono inserite delle nuove stringhe da tradurre è necessario lanciare il task grunt `grunt nggettext_extract` che andrà a compilare il template
`po/rendicontazione.pot`, che dovrà essere utilizzato come template per aggiornare i file `po/*.po` con le traduzioni.

Alla modifica dei file `po/*.po` se si sviluppa con `grunt serve` attivo il file `scripts/l10n.js` verrà aggiornato automaticamente, altrimenti per utilizzare le nuove traduzioni sarà necessario lanciare il task grunt `grunt nggettext_compile_concat`.

## Internazionalizzazione

I file riguardanti l'internazionalizzazione si trovano nella cartella `app/i18n`:
* `angular-locale.js.$LANG`: sovrascrive i default di **AngularJS**. Al variare della versione di **AngularJS** devono essere sostituiti con le versioni corrispondenti dei file per le lingue supportate che si trovano nella cartella `src/ngLocale` del repository di [https://github.com/angular/angular.js/](AngularJS)
* `art-locale.js.$LANG`: definisce dei filtri personalizzati per **restART**: se si aggiunge una nuova lingua sarà necessario creare un nuovo file con l'estensione adeguta.

Tali file verranno serviti da **Apache** nella versione corrsipondente alla lingua del browser attraverso il modulo `MultiViews`.

**ATTENZIONE**: in ambiente di sviluppo se viene utilizzato `grunt serve` verranno servite le versioni in italiano (ovviamente il supporto per l'internazionalizzazione non sarà attivo).

## LESS

***FIXME: DOCUMENTARE***
