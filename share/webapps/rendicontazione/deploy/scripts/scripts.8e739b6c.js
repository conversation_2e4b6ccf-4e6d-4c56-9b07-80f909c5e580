"use strict";function rendicontazioneCaviCtrl($stateParams,$scope,_,gettextCatalog,Alert,rendicontazioneCaviCheckService,rendicontazioneCaviRendicontaService){$scope.workId=1*$stateParams.workId,$scope.planningServiceId=$stateParams.planningServiceId,$scope.user=$stateParams.user,$scope.loading=!0,$scope.canGoOn=!1,$scope.fatalError=!1,$scope.fatalErrorObj={},$scope.accountingDone=!1,$scope.title=gettextCatalog.getString("Cables accounting form"),$scope.formData={plannedLengths:{},poseTypeChanges:!1,note:void 0,doneLengths:{},valid:void 0};var checkParams={workId:$scope.workId,planningServiceId:$scope.planningServiceId,user:$scope.user};rendicontazioneCaviCheckService().get(checkParams).$promise.then(function(response){$scope.formData.plannedLengths={underground:Number(response.system.properties.plannedUndergroundLength)||void 0,aerial:Number(response.system.properties.plannedAerialLength)||void 0,facade:Number(response.system.properties.plannedFacadeLength)||void 0,fromStock:Number(response.system.properties.plannedFromStockLength)||void 0,toStock:Number(response.system.properties.plannedToStockLength)||void 0},$scope.loading=!1,$scope.canGoOn=!0}).catch(function(err){$scope.loading=!1,$scope.fatalError=!0,$scope.fatalErrorObj=err.data}),$scope.resetForm=function(){$scope.$broadcast("formRendicontazioneCaviReset")},$scope.rendiconta=function(){$scope.$broadcast("formRendicontazioneCaviDisableAllFields"),$scope.submitConfirmation=!0},$scope.confirm=function(ok){if(!ok)return $scope.$broadcast("formRendicontazioneCaviEnableAllFields"),void($scope.submitConfirmation=!1);var toSubmit={workId:$scope.workId,planningServiceId:$scope.planningServiceId,user:$scope.user,poseTypeChanges:$scope.formData.poseTypeChanges,note:$scope.formData.note};_.each($scope.formData.doneLengths,function(value,key){_.isUndefined(value)||_.isNull(value)||(toSubmit[key]=value)}),rendicontazioneCaviRendicontaService().rendiconta(toSubmit).$promise.then(function(){$scope.submitConfirmation=!1,$scope.accountingDone=!0}).catch(function(err){$scope.$broadcast("formRendicontazioneCaviEnableAllFields"),$scope.submitConfirmation=!1,Alert.error(err.data)})}}function rendicontazioneCaviCheckService($resource){return function(){return $resource("ws/check-cavi.html",{},{get:{method:"GET",params:{}}})}}function rendicontazioneCaviRendicontaService($resource){return function(){return $resource("ws/rendicontazione-cavi.html",{},{rendiconta:{method:"POST",params:{}}})}}angular.module("rendicontazione",["ui.router","ui.bootstrap","ngResource","ngAnimate","ngSanitize","gettext","angular-growl","Alerts","Utils","art.locale","version","rendicontazioneCaviModule"]).config(["$stateProvider","$urlRouterProvider","rendicontazioneCaviConfigProvider",function($stateProvider,$urlRouterProvider,rendicontazioneCaviConfigProvider){$urlRouterProvider.otherwise("/not-authorized"),$stateProvider.state("rendicontazioneCavi",{url:"/rendicontazione/cavi/:workId/:planningServiceId?user",templateUrl:"views/rendicontazione-cavi.html",controller:"rendicontazioneCaviCtrl"}).state("notAuthorized",{url:"/not-authorized",templateUrl:"not-authorized.html"}),rendicontazioneCaviConfigProvider.setThreshold(.15)}]).run(["gettextCatalog","$locale",function(gettextCatalog,$locale){gettextCatalog.setCurrentLanguage($locale.id),gettextCatalog.debug=!1}]),angular.module("Utils",[]).factory("_",["$window",function($window){return $window._}]),angular.module("gettext").run(["gettextCatalog",function(gettextCatalog){gettextCatalog.setStrings("it",{Done:{"form-rendicontazione-cavi":"Realizzata"},Planned:{"form-rendicontazione-cavi":"Progettata"},"Accounting done successfully!":"Rendicontazione avvenuta con successo!",Blowing:"Blowing","Cables accounting form":"Form rendicontazione cavi","Changes in the pose type":"Modifiche nel tipo di posa",Clear:"Cancella",Close:"Chiudi","Do you confirm form submission?":"Confermi la sottomissione del form?","End Element Stock":"Scorta elemento finale","End work date":"Data fine lavori","End work date must be greater or equal than start work date":"La data fine lavori deve essere maggiore o uguale alla data inizio lavori","Error detail":"Dettaglio errore","Error message":"Messaggio errore","Facade Air Laying":"Posa aerea in facciata",Handmaid:"Manuale","Loading...":"Caricamento...",No:"No",Note:"Note","Optical fiber laying in mini-pipe":"Posa fibra ottica in minitubo","Piled Aerial Laying":"Posa aerea palificata",'Please don\'t execute any operation except end works with button "Fine"':'Non eseguire alcuna operazione eccetto il fine lavori con il bottone "Fine"','Please reload "Dettaglio attività" page until end works button "Fine" is enabled':'Aggiornare la pagina "Dettaglio attività" finché non si abilita il bottone di fine lavori "Fine"',"Pose type":"Tipo posa","Previous note":"Note precedenti",Reset:"Ripristina","Start Element Stock":"Scorta elemento iniziale","Start work date":"Data inizio lavori",Submit:"Invia","The entered value differs from the planned value above the {{perc}}% threshold":"Il valore inserito differisce dal valore pianificato oltre la soglia del {{perc}}%",Today:"Oggi",Yes:"Sì","mt.":"mt."})}]),angular.module("Alerts",[]).config(["growlProvider",function(growlProvider){growlProvider.globalTimeToLive({success:3e3,error:3e3,warning:3e3,info:3e3}),growlProvider.globalPosition("top-center"),growlProvider.globalInlineMessages(!1)}]).factory("Alert",["growl","gettextCatalog",function(growl,gettextCatalog){var messages=[];return{fatal:function(o){var msg,config={ttl:-1,disableCloseButton:!0};angular.isObject(o)?(msg=o.message,o.internalMessage&&(msg+='<div ng-if="error.internalMessage"><p><em>'+gettextCatalog.getString("Error detail")+"</em>: "+o.internalMessage+"</p></div>"),o.UUID&&(msg+='<div ng-if="error.UUID" class="push-right small uuid">(UUID: <code>'+o.UUID+"</code>)</div>")):msg=o,growl.error(msg,config)},error:function(o,config){angular.isObject(config)||(config={ttl:-1,enableHtml:!0});var msg;angular.isObject(o)?(msg=o.message,o.internalMessage&&(msg+='<div ng-if="error.internalMessage"><p><em>'+gettextCatalog.getString("Error detail")+"</em>: "+o.internalMessage+"</p></div>"),o.UUID&&(msg+='<div ng-if="error.UUID" class="push-right small uuid">(UUID: <code>'+o.UUID+"</code>)</div>")):msg=o,config.position&&(growl.position=function(){return config.position}),messages.push(growl.error(msg,config)),growl.error(msg,config)},info:function(msg,config){angular.isObject(config)||(config={}),config.position&&(growl.position=function(){return config.position}),messages.push(growl.info(msg,config)),growl.info(msg,config)},warning:function(msg,o,config){angular.isObject(config)||(config={enableHtml:!0}),o&&(o.message&&(msg+="<div><p><em>"+gettextCatalog.getString("Error message")+"</em>: "+o.message+"</p></div>"),o.internalMessage&&(msg+='<div ng-if="error.internalMessage"><p><em>'+gettextCatalog.getString("Error detail")+"</em>: "+o.internalMessage+"</p></div>"),o.UUID&&(msg+='<div ng-if="error.UUID" class="push-right small uuid">(UUID: <code>'+o.UUID+"</code>)</div>")),config.position&&(growl.position=function(){return config.position}),messages.push(growl.warning(msg,{})),growl.warning(msg,{})},success:function(msg,config){angular.isObject(config)||(config={}),config.position&&(growl.position=function(){return config.position}),messages.push(growl.success(msg,config)),growl.success(msg,config)},deleteMessage:function(){for(var i=0;i<messages.length;i++)angular.isDefined(messages[i])&&messages[i].destroy();messages=[]}}}]),angular.module("version",["version.interpolate-filter","version.version-directive"]).value("version","0.1.1"),angular.module("version.version-directive",[]).directive("appVersion",["version",function(version){return function(scope,elm){elm.text(version)}}]),angular.module("version.interpolate-filter",[]).filter("interpolate",["version",function(version){return function(text){return String(text).replace(/\%VERSION\%/gm,version)}}]),rendicontazioneCaviCtrl.$inject=["$stateParams","$scope","_","gettextCatalog","Alert","rendicontazioneCaviCheckService","rendicontazioneCaviRendicontaService"],rendicontazioneCaviCheckService.$inject=["$resource"],rendicontazioneCaviRendicontaService.$inject=["$resource"],angular.module("rendicontazione").controller("rendicontazioneCaviCtrl",rendicontazioneCaviCtrl).factory("rendicontazioneCaviCheckService",rendicontazioneCaviCheckService).factory("rendicontazioneCaviRendicontaService",rendicontazioneCaviRendicontaService),function(){function rendicontazioneCaviCtrl($scope,$timeout,_,gettextCatalog,rendicontazioneCaviConfig){$scope.$on("formRendicontazioneCaviReset",function(){reset()}),$scope.$on("formRendicontazioneCaviDisableAllFields",function(){$scope.allFieldsDisabled=!0}),$scope.$on("formRendicontazioneCaviEnableAllFields",function(){$scope.allFieldsDisabled=!1}),$scope.$watchGroup(["workDates.start","workDates.end","formRendicontazione.$valid"],function(newValue){var start=newValue[0],end=newValue[1],formValid=newValue[2];$scope._handleWorkDates?($scope.valid=!1,$scope.datesAreValid=!0,start&&end&&($scope.datesAreValid=end>=start),start&&end&&formValid&&end>=start&&($scope.valid=!0)):$scope.valid=formValid,$scope.startWorkDate=start,$scope.endWorkDate=end});var threshold=Number($scope.threshold)||rendicontazioneCaviConfig.threshold;$scope.thresholdWarning=gettextCatalog.getString("The entered value differs from the planned value above the {{perc}}% threshold",{perc:100*threshold}),$scope.fuoriSoglia={},!0!==$scope.poseTypeChanges&&!1!==$scope.poseTypeChanges&&($scope.poseTypeChanges=!1),$scope.allFieldsDisabled=!1,$scope._handleWorkDates="1"===$scope.handleWorkDates,$scope.workDates={},$scope.startDateOptions={format:$scope.dateFormat,startingDay:1},$scope.endDateOptions={format:$scope.dateFormat,startingDay:1},$scope.datesAreValid=!0,$scope.changeMinAndMaxDates=function(){$scope.workDates.start>$scope.workDates.end&&($scope.workDates.end=void 0),$scope.endDateOptions.minDate=new Date($scope.workDates.start)};var originalStartWorkDate,originalEndWorkDate,originalPoseTypeChanges=$scope.poseTypeChanges,originalNote=$scope.note,originalLengths=angular.copy($scope.doneLengths);$scope._handleWorkDates&&(_.isUndefined($scope.startWorkDate)&&($scope.startWorkDate=null),_.isUndefined($scope.endWorkDate)&&($scope.endWorkDate=null),originalStartWorkDate=angular.copy($scope.startWorkDate),originalEndWorkDate=angular.copy($scope.endWorkDate),$scope.workDates={start:$scope.startWorkDate,end:$scope.endWorkDate},$scope.changeMinAndMaxDates()),$scope.poseTypeChangesOnChange=function(){$scope.poseTypeChanges||((_.isUndefined($scope.plannedLengths.underground)||_.isNull($scope.plannedLengths.underground))&&($scope.doneLengths.underground=originalLengths.underground,$scope.doneLengths.handmaid=originalLengths.handmaid,$scope.verificaSoglia("underground")),_.each(["aerial","facade"],function(field){(_.isUndefined($scope.plannedLengths[field])||_.isNull($scope.plannedLengths[field]))&&($scope.doneLengths[field]=originalLengths[field]),$scope.verificaSoglia(field)}))};var reset=function(){$scope.poseTypeChanges=originalPoseTypeChanges,$scope.note=originalNote,_.each(["underground","handmaid","aerial","facade","fromStock","toStock"],function(field){$scope.doneLengths[field]=originalLengths[field],$scope.formRendicontazione[field].$setPristine()}),$scope._handleWorkDates&&($scope.workDates={start:originalStartWorkDate,end:originalEndWorkDate},$scope.formRendicontazione.startWorkDate.$setPristine(),$scope.formRendicontazione.endWorkDate.$setPristine(),$scope.changeMinAndMaxDates()),verificaSoglie()};$scope.verificaSoglia=function(field){var plannedValue,doneValue;if("underground"===field||"handmaid"===field){if($scope.formRendicontazione.underground.$invalid||$scope.formRendicontazione.handmaid.$invalid)return;plannedValue=($scope.plannedLengths.underground||0)+($scope.plannedLengths.handmaid||0),doneValue=($scope.doneLengths.underground||0)+($scope.doneLengths.handmaid||0),field="underground"}else{if($scope.formRendicontazione[field].$invalid)return;plannedValue=$scope.plannedLengths[field]||0,doneValue=$scope.doneLengths[field]||0}$scope.fuoriSoglia[field]=!(!plannedValue||!(doneValue<plannedValue*(1-threshold)||doneValue>plannedValue*(1+threshold)))};var verificaSoglie=function(){_.each(["underground","handmaid","aerial","facade","fromStock","toStock"],function(field){$scope.verificaSoglia(field)})};$timeout(function(){verificaSoglie()})}rendicontazioneCaviCtrl.$inject=["$scope","$timeout","_","gettextCatalog","rendicontazioneCaviConfig"],angular.module("rendicontazioneCaviModule",["ui.bootstrap","gettext"]).provider("rendicontazioneCaviConfig",function(){this.threshold=.15,this.setThreshold=function(threshold){this.threshold=threshold},this.$get=function(){return this}}).filter("rendicontazioneCaviNl2brFilter",["$sce",function($sce){return function(msg,isXhtml){return isXhtml=isXhtml||!0,msg=(msg+"").replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g,"$1"+(isXhtml?"<br />":"<br>")+"$2"),$sce.trustAsHtml(msg)}}]).filter("rendicontazioneCaviEncodeHtmlEntitiesFilter",function(){return function(msg){return msg.replace(/[\u00A0-\u9999<>\&]/gim,function(i){return"&#"+i.charCodeAt(0)+";"})}}).directive("rendicontazioneCavi",function(){return{restrict:"E",scope:{plannedLengths:"=",poseTypeChanges:"=",note:"=",doneLengths:"=",handleWorkDates:"@",startWorkDate:"=",endWorkDate:"=",dateFormat:"@",valid:"=",threshold:"@",previousNote:"@"},templateUrl:"views/directives/rendicontazione-cavi/rendicontazione-cavi.html",controller:rendicontazioneCaviCtrl}})}(),angular.module("rendicontazione").run(["$templateCache",function($templateCache){$templateCache.put("views/alerts.html",'<div growl limit-messages="5"></div> <div growl limit-messages="5" reference="1" inline="true"></div>'),$templateCache.put("views/directives/rendicontazione-cavi/rendicontazione-cavi.html",'<form class="form" name="formRendicontazione" novalidate> <div class="form-group" ng-if="_handleWorkDates"> <div class="col-sm-12"> <label for="startWorkDate"> <translate>Start work date</translate><sup>*</sup> <span class="glyphicon glyphicon-alert text-danger" ng-if="!datesAreValid" uib-tooltip="{{ \'End work date must be greater or equal than start work date\' | translate }}"></span> </label> <div class="input-group" ng-class="{ \'has-error\': (formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) || !datesAreValid }"> <span class="input-group-addon"><i class="fa fa-clock-o"></i></span> <input type="text" class="form-control" id="startWorkDate" name="startWorkDate" ng-model="workDates.start" ng-required="true" ng-disabled="allFieldsDisabled" ng-click="startWorkDateOpen = true" ng-change="changeMinAndMaxDates()" uib-datepicker-popup="{{startDateOptions.format}}" is-open="startWorkDateOpen" datepicker-options="startDateOptions" current-text="{{ \'Today\' | translate }}" clear-text="{{ \'Clear\' | translate }}" close-text="{{ \'Close\' | translate }}"> <span class="input-group-btn"> <button type="button" class="btn" ng-class="{ \'btn-danger\': (formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) || !datesAreValid, \'btn-default\': !(formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) && datesAreValid }" ng-disabled="allFieldsDisabled" ng-click="startWorkDateOpen = true"><i class="glyphicon glyphicon-calendar"></i> </button> </span> </div> </div> </div> <div class="form-group" ng-if="_handleWorkDates"> <div class="col-sm-12"> <label for="endWorkDate"> <translate>End work date</translate><sup>*</sup> <span class="glyphicon glyphicon-alert text-danger" ng-if="!datesAreValid" uib-tooltip="{{ \'End work date must be greater or equal than start work date\' | translate }}"></span> </label> <div class="input-group" ng-class="{ \'has-error\': (formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) || !datesAreValid }"> <span class="input-group-addon"><i class="fa fa-clock-o"></i></span> <input type="text" class="form-control" id="endWorkDate" name="endWorkDate" ng-model="workDates.end" ng-required="true" ng-disabled="allFieldsDisabled" ng-click="endWorkDateOpen = true" ng-change="changeMinAndMaxDates()" uib-datepicker-popup="{{endDateOptions.format}}" is-open="endWorkDateOpen" datepicker-options="endDateOptions" current-text="{{ \'Today\' | translate }}" clear-text="{{ \'Clear\' | translate }}" close-text="{{ \'Close\' | translate }}"> <span class="input-group-btn"> <button type="button" class="btn" ng-class="{ \'btn-danger\': (formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) || !datesAreValid, \'btn-default\': !(formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) && datesAreValid }" ng-disabled="allFieldsDisabled" ng-click="endWorkDateOpen = true"><i class="glyphicon glyphicon-calendar"></i> </button> </span> </div> </div> </div> <div class="form-group"> <div class="col-sm-12"> <div class="checkbox"> <label> <input type="checkbox" ng-model="poseTypeChanges" ng-disabled="allFieldsDisabled" ng-change="poseTypeChangesOnChange()" name="poseTypeChanges" id="poseTypeChanges" ng-true-value="true" ng-false-value="false"> <translate>Changes in the pose type</translate> </label> </div> </div> </div> <div class="form-group"> <div class="col-sm-12"> <table class="table table-bordered table-condensed table-rendicontazione"> <thead> <th class="col-sm-5 text-left" translate>Pose type</th> <th class="col-sm-2 text-right" translate translate-context="form-rendicontazione-cavi">Planned</th> <th colspan="2" class="col-sm-5 text-center" translate translate-context="form-rendicontazione-cavi">Done</th> </thead> <tbody> <tr ng-class="{ \'has-error\': formRendicontazione.underground.$dirty && formRendicontazione.underground.$invalid }"> <td class="text-left" rowspan="2"> <translate>Optical fiber laying in mini-pipe</translate><sup ng-if="plannedLengths.underground != null">*</sup> <span class="glyphicon glyphicon-alert text-warning" ng-if="formRendicontazione.underground.$valid && formRendicontazione.handmaid.$valid && fuoriSoglia.underground" uib-tooltip="{{ thresholdWarning }}"></span> </td> <td class="text-right" rowspan="2">{{plannedLengths.underground}}&nbsp;<translate ng-if="plannedLengths.underground">mt.</translate></td> <td class="text-right"><i translate>Blowing</i></td> <td style="white-space: nowrap"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.underground" name="underground" id="underground" class="form-control input-sm text-right" ng-min="0" ng-disabled="allFieldsDisabled || (plannedLengths.underground == null && !poseTypeChanges)" ng-required="!allFieldsDisabled && plannedLengths.underground != null" ng-blur="verificaSoglia(\'underground\')"> </td> </tr> <tr ng-class="{ \'has-error\': formRendicontazione.handmaid.$dirty && formRendicontazione.handmaid.$invalid }"> <td class="text-right" style="border-top: 1px solid #fff"><i translate>Handmaid</i></td> <td style="white-space: nowrap; border-top: 1px solid #fff"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.handmaid" name="handmaid" id="handmaid" class="form-control input-sm text-right" ng-min="0" ng-disabled="allFieldsDisabled || (plannedLengths.underground == null && !poseTypeChanges)" ng-required="!allFieldsDisabled && plannedLengths.underground != null" ng-blur="verificaSoglia(\'handmaid\')"> </td> </tr> <tr ng-class="{ \'has-error\': formRendicontazione.aerial.$dirty && formRendicontazione.aerial.$invalid }"> <td class="text-left"> <translate>Piled Aerial Laying</translate><sup ng-if="plannedLengths.aerial != null">*</sup> <span class="glyphicon glyphicon-alert text-warning" ng-if="formRendicontazione.aerial.$valid && fuoriSoglia.aerial" uib-tooltip="{{ thresholdWarning }}"></span> </td> <td class="text-right">{{plannedLengths.aerial}}&nbsp;<translate ng-if="plannedLengths.aerial">mt.</translate></td> <td class="text-right"><i>&nbsp;</i></td> <td style="white-space: nowrap"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.aerial" name="aerial" id="aerial" class="form-control input-sm text-right" ng-min="0" ng-disabled="allFieldsDisabled || (plannedLengths.aerial == null && !poseTypeChanges)" ng-required="!allFieldsDisabled && plannedLengths.aerial != null" ng-blur="verificaSoglia(\'aerial\')"> </td> </tr> <tr ng-class="{ \'has-error\': formRendicontazione.facade.$dirty && formRendicontazione.facade.$invalid }"> <td class="text-left"> <translate>Facade Air Laying</translate><sup ng-if="plannedLengths.facade != null">*</sup> <span class="glyphicon glyphicon-alert text-warning" ng-if="formRendicontazione.facade.$valid && fuoriSoglia.facade" uib-tooltip="{{ thresholdWarning }}"></span> </td> <td class="text-right">{{plannedLengths.facade}}&nbsp;<translate ng-if="plannedLengths.facade">mt.</translate></td> <td class="text-right"><i>&nbsp;</i></td> <td style="white-space: nowrap"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.facade" name="facade" id="facade" class="form-control input-sm text-right" ng-min="0" ng-disabled="allFieldsDisabled || (plannedLengths.facade == null && !poseTypeChanges)" ng-required="!allFieldsDisabled && plannedLengths.facade != null" ng-blur="verificaSoglia(\'facade\')"> </td> </tr> <tr ng-class="{ \'has-error\': formRendicontazione.fromStock.$dirty && formRendicontazione.fromStock.$invalid }"> <td class="text-left"> <translate>Start Element Stock</translate><sup>*</sup> <span class="glyphicon glyphicon-alert text-warning" ng-if="formRendicontazione.fromStock.$valid && fuoriSoglia.fromStock" uib-tooltip="{{ thresholdWarning }}"></span> </td> <td class="text-right">{{plannedLengths.fromStock}}&nbsp;<translate ng-if="plannedLengths.fromStock">mt.</translate></td> <td class="text-right"><i>&nbsp;</i></td> <td style="white-space: nowrap"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.fromStock" name="fromStock" id="fromStock" class="form-control input-sm text-right" ng-min="0" pattern="[0-9]+" ng-disabled="allFieldsDisabled" ng-required="!allFieldsDisabled" ng-blur="verificaSoglia(\'fromStock\')"> </td> </tr> <tr ng-class="{ \'has-error\': formRendicontazione.toStock.$dirty && formRendicontazione.toStock.$invalid }"> <td class="text-left"> <translate>End Element Stock</translate><sup>*</sup> <span class="glyphicon glyphicon-alert text-warning" ng-if="formRendicontazione.toStock.$valid && fuoriSoglia.toStock" uib-tooltip="{{ thresholdWarning }}"></span> </td> <td class="text-right">{{plannedLengths.toStock}}&nbsp;<translate ng-if="plannedLengths.toStock">mt.</translate></td> <td class="text-right"><i>{{&nbsp;}}</i></td> <td style="white-space: nowrap"> \x3c!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile\n\t\t\t\t\t\t\tcompilare solo i campi che erano stati pianificati --\x3e <input type="number" ng-model="doneLengths.toStock" name="toStock" id="toStock" class="form-control input-sm text-right" ng-min="0" pattern="[0-9]+" ng-disabled="allFieldsDisabled" ng-required="!allFieldsDisabled" ng-blur="verificaSoglia(\'toStock\')"> </td> </tr> </tbody> </table> </div> </div> <div class="form-group" ng-if="previousNote"> <div class="col-sm-12"> <label translate>Previous note</label> <div name="previousNote" ng-bind-html="previousNote|rendicontazioneCaviEncodeHtmlEntitiesFilter|rendicontazioneCaviNl2brFilter:true"></div> </div> </div> <div class="form-group"> <div class="col-sm-12"> <label for="note" translate>Note</label> <textarea class="form-control" ng-model="note" ng-disabled="allFieldsDisabled" name="note" id="note"></textarea> </div> </div> </form> <style>.table-rendicontazione input[type=number]::-webkit-inner-spin-button, \n.table-rendicontazione input[type=number]::-webkit-outer-spin-button { \n\t-webkit-appearance: none; \n\tmargin: 0; \n}\n.table-rendicontazione tr td,\n.table-rendicontazione tr th {\n\tvertical-align: middle !important;\n}\n.table-rendicontazione label {\n\tfont-weight: normal;\n}</style>'),$templateCache.put("views/rendicontazione-cavi.html",'<div class="container-fluid"> <div class="col-sm-12 col-md-8 col-md-offset-2"> <div> <div class="pull-right hidden-xs"> <b>Version</b> <span app-version></span> </div> <h3>{{title}}</h3> </div> <div ng-if="loading" class="col-sm-12 text-center" style="margin-top: 100px"> <h4 class="text-center"><span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> <translate>Loading...</translate> </h4> </div> <div ng-if="fatalError" class="row alert alert-danger" ng-if="accountingDone"> <p>{{fatalErrorObj.message}}</p> <div ng-if="fatalErrorObj.internalMessage"><p><em translate>Error detail</em>: {{fatalErrorObj.internalMessage}}</p></div> <div ng-if="fatalErrorObj.UUID" class="push-right small uuid">(UUID: <code>{{fatalErrorObj.UUID}}</code>)</div> </div> <div class="row" ng-if="canGoOn"> <rendicontazione-cavi planned-lengths="formData.plannedLengths" pose-type-changes="formData.poseTypeChanges" note="formData.note" done-lengths="formData.doneLengths" valid="formData.valid"></rendicontazione-cavi> </div> <div ng-if="canGoOn" style="margin-top: 20px"> <div class="form-group" ng-hide="accountingDone || submitConfirmation"> <button type="submit" class="btn btn-primary" ng-click="rendiconta()" ng-disabled="!formData.valid"> <span class="glyphicon glyphicon-ok"></span> <translate>Submit</translate> </button> <button type="button" class="btn btn-default" ng-click="resetForm()"> <span class="glyphicon glyphicon-remove"></span> <translate>Reset</translate> </button> </div> <div class="alert alert-warning" ng-show="submitConfirmation"> <p translate>Do you confirm form submission?</p> <p> <button type="submit" class="btn btn-primary" ng-click="confirm(true)" translate> Yes </button> <button type="button" class="btn btn-default" ng-click="confirm(false)" translate> No </button> </p> </div> </div> <div class="alert alert-success" ng-if="accountingDone"> <translate>Accounting done successfully!</translate><br> <translate>Please reload "Dettaglio attività" page until end works button "Fine" is enabled</translate> <div class="alert alert-warning" style="margin-top: 10px"> <h4><span class="glyphicon glyphicon-alert"></span> <translate>Please don\'t execute any operation except end works with button "Fine"</translate></h4> </div> </div> </div> </div>')}]);