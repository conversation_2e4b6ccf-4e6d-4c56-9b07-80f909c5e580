# Language file for rendicontazione.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: rendicontazione\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> "
"Vasile <<EMAIL>, <EMAIL>, <EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:109
msgctxt "form-rendicontazione-cavi"
msgid "Done"
msgstr "Realizzata"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:108
msgctxt "form-rendicontazione-cavi"
msgid "Planned"
msgstr "Progettata"

#: app/views/rendicontazione-cavi.html:85
msgid "Accounting done successfully!"
msgstr "Rendicontazione avvenuta con successo!"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:124
msgid "Blowing"
msgstr "Blowing"

#: app/scripts/controllers/rendicontazione-cavi.js:15
msgid "Cables accounting form"
msgstr "Form rendicontazione cavi"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:97
msgid "Changes in the pose type"
msgstr "Modifiche nel tipo di posa"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:27
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:67
msgid "Clear"
msgstr "Cancella"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:28
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:68
msgid "Close"
msgstr "Chiudi"

#: app/views/rendicontazione-cavi.html:59
msgid "Do you confirm form submission?"
msgstr "Confermi la sottomissione del form?"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:248
msgid "End Element Stock"
msgstr "Scorta elemento finale"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:46
msgid "End work date"
msgstr "Data fine lavori"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:10
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:50
msgid "End work date must be greater or equal than start work date"
msgstr ""
"La data fine lavori deve essere maggiore o uguale alla data inizio lavori"

#: app/scripts/services/alerts.js:35 app/scripts/services/alerts.js:47
#: app/scripts/services/alerts.js:68 app/views/rendicontazione-cavi.html:24
msgid "Error detail"
msgstr "Dettaglio errore"

#: app/scripts/services/alerts.js:67
msgid "Error message"
msgstr "Messaggio errore"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:191
msgid "Facade Air Laying"
msgstr "Posa aerea in facciata"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:143
msgid "Handmaid"
msgstr "Manuale"

#: app/views/rendicontazione-cavi.html:14
msgid "Loading..."
msgstr "Caricamento..."

#: app/views/rendicontazione-cavi.html:69
msgid "No"
msgstr "No"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:289
msgid "Note"
msgstr "Note"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:116
msgid "Optical fiber laying in mini-pipe"
msgstr "Posa fibra ottica in minitubo"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:163
msgid "Piled Aerial Laying"
msgstr "Posa aerea palificata"

#: app/views/rendicontazione-cavi.html:91
msgid ""
"Please don't execute any operation except end works with button \"Fine\""
msgstr ""
"Non eseguire alcuna operazione eccetto il fine lavori con il bottone \"Fine\""

#: app/views/rendicontazione-cavi.html:86
msgid ""
"Please reload \"Dettaglio attività\" page until end works button \"Fine\" is "
"enabled"
msgstr ""
"Aggiornare la pagina \"Dettaglio attività\" finché non si abilita il bottone "
"di fine lavori \"Fine\""

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:107
msgid "Pose type"
msgstr "Tipo posa"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:282
msgid "Previous note"
msgstr "Note precedenti"

#: app/views/rendicontazione-cavi.html:55
msgid "Reset"
msgstr "Ripristina"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:219
msgid "Start Element Stock"
msgstr "Scorta elemento iniziale"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:6
msgid "Start work date"
msgstr "Data inizio lavori"

#: app/views/rendicontazione-cavi.html:47
msgid "Submit"
msgstr "Invia"

#. Italiano: Il valore inserito differisce dal valore pianificato oltre la soglia del 15%
#: app/scripts/directives/rendicontazione-cavi/rendicontazione-cavi.js:49
msgid ""
"The entered value differs from the planned value above the {{perc}}% "
"threshold"
msgstr ""
"Il valore inserito differisce dal valore pianificato oltre la soglia del "
"{{perc}}%"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:26
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:66
msgid "Today"
msgstr "Oggi"

#: app/views/rendicontazione-cavi.html:61
msgid "Yes"
msgstr "Sì"

#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:123
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:170
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:198
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:226
#: app/views/directives/rendicontazione-cavi/rendicontazione-cavi.html:255
msgid "mt."
msgstr "mt."
