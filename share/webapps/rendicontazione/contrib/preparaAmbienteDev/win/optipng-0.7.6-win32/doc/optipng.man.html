<!-- Creator     : groff version 1.22.3 -->
<!-- CreationDate: Sun Apr  3 11:39:00 2016 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>OPTIPNG</title>

</head>
<body>

<h1 align="center">OPTIPNG</h1>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>OptiPNG</b>
&minus; Optimize Portable Network Graphics files</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>optipng</b>
[<b>&minus;?</b> | <b>&minus;h</b> | <b>&minus;help</b>]
<b><br>
optipng</b> [<i>options...</i>] <i>files...</i></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>OptiPNG</b> program shall attempt to <i>optimize</i> PNG
files, i.e. reduce their size to a minimum, without losing
semantic information. In addition, this program shall
perform a suite of auxiliary functions like integrity
checks, metadata recovery and pixmap-to-PNG conversion.</p>

<p style="margin-left:11%; margin-top: 1em">The
optimization attempts are not guaranteed to succeed. Valid
PNG files that cannot be optimized by this program are
normally left intact; their size will not grow. The user may
request to override this default behavior.</p>

<h2>FILES
<a name="FILES"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The input files
are raster image files encoded either in PNG format (the
native format), or in an external format. The currently
supported external formats are GIF, BMP, PNM and TIFF.</p>

<p style="margin-left:11%; margin-top: 1em"><b>OptiPNG</b>
processes each image file given in the command line as
follows:</p>

<p style="margin-left:11%; margin-top: 1em">&minus; If the
image is in PNG format:</p>

<p style="margin-left:22%; margin-top: 1em">Attempts to
optimize the given file in-place. If optimization is
successful, or if the option <b>&minus;force</b> is enabled,
replaces the original file with its optimized version. The
original file is backed up if the option <b>&minus;keep</b>
is enabled.</p>

<p style="margin-left:11%; margin-top: 1em">&minus; If the
image is in an external format:</p>

<p style="margin-left:22%; margin-top: 1em">Creates an
optimized PNG version of the given file. The output file
name is composed from the original file name and the
<tt>.png</tt> extension.</p>

<p style="margin-left:11%; margin-top: 1em">Existing files
are <i>not</i> overwritten, unless the option
<b>&minus;clobber</b> is enabled.</p>

<h2>OPTIONS
<a name="OPTIONS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>General
options <br>
&minus;?</b>, <b>&minus;h</b>, <b>&minus;help</b></p>

<p style="margin-left:22%;">Show a complete summary of
options.</p>

<p style="margin-left:11%;"><b>&minus;backup</b>,
<b>&minus;keep</b></p>

<p style="margin-left:22%;">Keep a backup of the modified
files.</p>

<p style="margin-left:11%;"><b>&minus;clobber</b></p>

<p style="margin-left:22%;">Overwrite the existing output
and backup files. <br>
Under this option, if the option <b>&minus;backup</b> is not
enabled, the old backups of the overwritten files are
deleted.</p>

<p style="margin-left:11%;"><b>&minus;dir</b>
<i>directory</i></p>

<p style="margin-left:22%;">Write the output files to
<i>directory</i>.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="6%">


<p><b>&minus;fix</b></p></td>
<td width="5%"></td>
<td width="78%">


<p>Enable error recovery. This option has no effect on
valid input files.</p></td></tr>
</table>

<p style="margin-left:22%;">The program will spend a
reasonable amount of effort to recover as much data as
possible, without increasing the output file size, but the
success cannot be generally guaranteed. The program may even
increase the file size, e.g., by reconstructing missing
critical data. Under this option, integrity shall take
precedence over file size. <br>
When this option is not used, the invalid input files are
left unprocessed.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p style="margin-top: 1em"><b>&minus;force</b></p></td>
<td width="2%"></td>
<td width="56%">


<p style="margin-top: 1em">Enforce writing of a new output
file.</p> </td>
<td width="22%">
</td></tr>
</table>

<p style="margin-left:22%;">This option overrides the
program&rsquo;s decision not to write such file, e.g. when
the PNG input is digitally signed (using dSIG), or when the
PNG output becomes larger than the PNG input.</p>

<p style="margin-left:11%;"><b>&minus;log</b>
<i>file</i></p>

<p style="margin-left:22%;">Log messages to <i>file</i>.
For safety reasons, <i>file</i> must have the extension
<tt>.log</tt>. <br>
This option is deprecated and will be removed eventually.
Use shell redirection.</p>

<p style="margin-left:11%;"><b>&minus;out</b>
<i>file</i></p>

<p style="margin-left:22%;">Write output file to
<i>file</i>. The command line must contain exactly one input
file.</p>

<p style="margin-left:11%;"><b>&minus;preserve</b></p>

<p style="margin-left:22%;">Preserve file attributes (time
stamps, file access rights, etc.) where applicable.</p>

<p style="margin-left:11%;"><b>&minus;quiet</b>,
<b>&minus;silent</b></p>

<p style="margin-left:22%;">Run in quiet mode. <br>
The messages are still written to the log file if the option
<b>&minus;log</b> is enabled.</p>

<p style="margin-left:11%;"><b>&minus;simulate</b></p>

<p style="margin-left:22%;">Run in simulation mode: perform
the trials, but do not create output files.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>&minus;v</b></p></td>
<td width="8%"></td>
<td width="63%">


<p>Enable the options <b>&minus;verbose</b> and
<b>&minus;version</b>.</p> </td>
<td width="15%">
</td></tr>
</table>

<p style="margin-left:11%;"><b>&minus;verbose</b></p>

<p style="margin-left:22%;">Run in verbose mode.</p>

<p style="margin-left:11%;"><b>&minus;version</b></p>

<p style="margin-left:22%;">Show copyright, version and
build info.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p><b>&minus;&minus;</b></p></td>
<td width="8%"></td>
<td width="41%">


<p>Stop option switch parsing.</p></td>
<td width="37%">
</td></tr>
</table>

<p style="margin-left:11%; margin-top: 1em"><b>PNG encoding
and optimization options <br>
&minus;o</b> <i>level</i></p>

<p style="margin-left:22%;">Select the optimization level.
<br>
The optimization level 0 enables a set of optimization
operations that require minimal effort. There will be no
changes to image attributes like bit depth or color type,
and no recompression of existing IDAT datastreams. <br>
The optimization level 1 enables a single IDAT compression
trial. The trial chosen is what <b>OptiPNG</b> <i>thinks</i>
it&rsquo;s probably the most effective. <br>
The optimization levels 2 and higher enable multiple IDAT
compression trials; the higher the level, the more trials.
<br>
The behavior and the default value of this option may change
across different program versions. Use the option
<b>&minus;h</b> to see the details pertaining to your
specific version.</p>

<p style="margin-left:11%;"><b>&minus;f</b>
<i>filters</i></p>

<p style="margin-left:22%;">Select the PNG delta filters.
<br>
The <i>filters</i> argument is specified as a rangeset (e.g.
<b>&minus;f0&minus;5</b>), and the default <i>filters</i>
value depends on the optimization level set by the option
<b>&minus;o</b>. <br>
The filter values 0, 1, 2, 3 and 4 indicate static
filtering, and correspond to the standard PNG filter codes
(<i>None</i>, <i>Left</i>, <i>Up</i>, <i>Average</i> and
<i>Paeth</i>, respectively). The filter value 5 indicates
adaptive filtering, whose effect is defined by the
<b>libpng</b>(3) library used by <b>OptiPNG</b>.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">


<p><b>&minus;full</b></p></td>
<td width="4%"></td>
<td width="78%">


<p>Produce a full report on IDAT. This option might slow
down the trials.</p></td></tr>
</table>

<p style="margin-left:11%;"><b>&minus;i</b> <i>type</i></p>

<p style="margin-left:22%;">Select the interlace type
(0&minus;1). <br>
If the interlace type 0 is selected, the output image shall
be non-interlaced (i.e. progressive-scanned). If the
interlace type 1 is selected, the output image shall be
interlaced using the <i>Adam7</i> method. <br>
By default, the output shall have the same interlace type as
the input.</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">


<p><b>&minus;nb</b></p></td>
<td width="7%"></td>
<td width="78%">


<p>Do not apply bit depth reduction.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">


<p><b>&minus;nc</b></p></td>
<td width="7%"></td>
<td width="78%">


<p>Do not apply color type reduction.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">


<p><b>&minus;np</b></p></td>
<td width="7%"></td>
<td width="78%">


<p>Do not apply palette reduction.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">


<p><b>&minus;nx</b></p></td>
<td width="7%"></td>
<td width="78%">


<p>Do not apply any lossless image reduction: enable the
options <b>&minus;nb</b>, <b>&minus;nc</b> and
<b>&minus;np</b>.</p> </td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">


<p><b>&minus;nz</b></p></td>
<td width="7%"></td>
<td width="78%">


<p>Do not recode IDAT datastreams.</p></td></tr>
</table>

<p style="margin-left:22%;">The IDAT optimization
operations that do not require recoding (e.g. IDAT chunk
concatenation) are still performed. <br>
This option has effect on PNG input files only.</p>

<p style="margin-left:11%;"><b>&minus;zc</b>
<i>levels</i></p>

<p style="margin-left:22%;">Select the zlib compression
levels used in IDAT compression. <br>
The <i>levels</i> argument is specified as a rangeset (e.g.
<b>&minus;zc6&minus;9</b>), and the default <i>levels</i>
value depends on the optimization level set by the option
<b>&minus;o</b>. <br>
The effect of this option is defined by the <b>zlib</b>(3)
library used by <b>OptiPNG</b>.</p>

<p style="margin-left:11%;"><b>&minus;zm</b>
<i>levels</i></p>

<p style="margin-left:22%;">Select the zlib memory levels
used in IDAT compression. <br>
The <i>levels</i> argument is specified as a rangeset (e.g.
<b>&minus;zm8&minus;9</b>), and the default <i>levels</i>
value depends on the optimization level set by the option
<b>&minus;o</b>. <br>
The effect of this option is defined by the <b>zlib</b>(3)
library used by <b>OptiPNG</b>.</p>

<p style="margin-left:11%;"><b>&minus;zs</b>
<i>strategies</i></p>

<p style="margin-left:22%;">Select the zlib compression
strategies used in IDAT compression. <br>
The <i>strategies</i> argument is specified as a rangeset
(e.g. <b>&minus;zs0&minus;3</b>), and the default
<i>strategies</i> value depends on the optimization level
set by the option <b>&minus;o</b>. <br>
The effect of this option is defined by the <b>zlib</b>(3)
library used by <b>OptiPNG</b>.</p>

<p style="margin-left:11%;"><b>&minus;zw</b>
<i>size</i></p>

<p style="margin-left:22%;">Select the zlib window size
(32k,16k,8k,4k,2k,1k,512,256) used in IDAT compression. <br>
The <i>size</i> argument can be specified either in bytes
(e.g. 16384) or kilobytes (e.g. 16k). The default
<i>size</i> value is set to the lowest window size that
yields an IDAT output as big as if yielded by the value
32768. <br>
The effect of this option is defined by the <b>zlib</b>(3)
library used by <b>OptiPNG</b>.</p>

<p style="margin-left:11%; margin-top: 1em"><b>Editing
options</b></p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">


<p><b>&minus;snip</b></p></td>
<td width="4%"></td>
<td width="78%">


<p>Cut one image out of multi-image, animation or video
files.</p> </td></tr>
</table>

<p style="margin-left:22%;">Depending on the input format,
this may be either the first or the most relevant (e.g. the
largest) image.</p>

<p style="margin-left:11%;"><b>&minus;strip</b>
<i>objects</i></p>

<p style="margin-left:22%;">Strip metadata objects from a
PNG file. <br>
PNG metadata is the information stored in any ancillary
chunk except tRNS. (tRNS represents the alpha channel,
which, even if ignored in rendering, is still a proper image
channel in the RGBA color space.) <br>
The only option currently supported is <b>&minus;strip
all</b>.</p>

<p style="margin-left:11%; margin-top: 1em"><b>Notes</b>
<br>
Options may come in any order (except for
<b>&minus;&minus;</b>), before, after, or alternating with
file names. Option names are case-insensitive and may be
abbreviated to their shortest unique prefix.</p>

<p style="margin-left:11%; margin-top: 1em">Some options
may have arguments that follow the option name, separated by
whitespace or the equal sign (&rsquo;<b>=</b>&rsquo;). If
the option argument is a number or a rangeset, the separator
may be omitted. For example:</p>


<p style="margin-left:22%; margin-top: 1em"><b>&minus;out</b>
<tt>newfile.png &nbsp;</tt>&lt;=&gt;
&nbsp;<b>&minus;out=</b><tt>newfile.png</tt> <b><br>
&minus;o3 &nbsp;</b>&lt;=&gt; &nbsp;<b>&minus;o 3
&nbsp;</b>&lt;=&gt; &nbsp;<b>&minus;o=3 <br>
&minus;f0,3&minus;5 &nbsp;</b>&lt;=&gt; &nbsp;<b>&minus;f
0,3&minus;5 &nbsp;</b>&lt;=&gt;
&nbsp;<b>&minus;f=0,3&minus;5</b></p>

<p style="margin-left:11%; margin-top: 1em">Rangeset
arguments are cumulative; e.g.</p>

<p style="margin-left:22%; margin-top: 1em"><b>&minus;f0
&minus;f3&minus;5 &nbsp;</b>&lt;=&gt;
&nbsp;<b>&minus;f0,3&minus;5 <br>
&minus;zs0 &minus;zs1 &minus;zs2&minus;3 &nbsp;</b>&lt;=&gt;
&nbsp;<b>&minus;zs0,1,2,3 &nbsp;</b>&lt;=&gt;
&nbsp;<b>&minus;zs0&minus;3</b></p>

<h2>EXTENDED DESCRIPTION
<a name="EXTENDED DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The PNG
optimization algorithm consists of the following steps:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p>1.</p></td>
<td width="3%"></td>
<td width="83%">


<p>Reduce the bit depth, the color type and the color
palette of the image. This step may reduce the size of the
uncompressed image, which, indirectly, may reduce the size
of the compressed image (i.e. the size of the output PNG
file).</p> </td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p>2.</p></td>
<td width="3%"></td>
<td width="83%">


<p>Run a suite of compression methods and strategies and
select the compression parameters that yield the smallest
output file.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p>3.</p></td>
<td width="3%"></td>
<td width="83%">


<p>Store all IDAT contents into a single chunk, eliminating
the overhead incurred by repeated IDAT headers and CRCs.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">


<p>4.</p></td>
<td width="3%"></td>
<td width="83%">


<p>Set the zlib window size inside IDAT to a mininum that
does not affect the compression ratio, reducing the memory
requirements of PNG decoders.</p></td></tr>
</table>

<p style="margin-left:11%; margin-top: 1em">Not all of the
above steps need to be executed. The behavior depends on the
actual input files and user options.</p>

<p style="margin-left:11%; margin-top: 1em">Step 1 may be
customized via the no-reduce options <b>&minus;nb</b>,
<b>&minus;nc</b>, <b>&minus;np</b> and <b>&minus;nx</b>.
Step 2 may be customized via the <b>&minus;o</b> option, and
may be fine-tuned via the options <b>&minus;zc</b>,
<b>&minus;zm</b>, <b>&minus;zs</b> and <b>&minus;zw</b>.
Step 3 is always executed. Step 4 is executed only if a new
IDAT is being created, and may be fine-tuned via the option
<b>&minus;zw</b>.</p>

<p style="margin-left:11%; margin-top: 1em">Extremely
exhaustive searches are not generally expected to yield
significant improvements in compression ratio, and are
recommended to advanced users only.</p>

<h2>EXAMPLES
<a name="EXAMPLES"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><tt>optipng
file.png &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt># default speed
<tt><br>
optipng -o5 file.png &nbsp;</tt># slow <tt><br>
optipng -o7 file.png &nbsp;</tt># very slow</p>

<h2>BUGS
<a name="BUGS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">Lossless image
reductions are not completely implemented. (This does
<i>not</i> affect the integrity of the output files.) Here
are the missing pieces:</p>

<p style="margin-left:22%; margin-top: 1em">&minus; The
color palette reductions are implemented only partially.
<br>
&minus; The bit depth reductions below 8, for grayscale
images, are not implemented yet.</p>

<p style="margin-left:11%; margin-top: 1em">Encoding of
images whose total IDAT size exceeds 2GB is not
supported.</p>

<p style="margin-left:11%; margin-top: 1em">TIFF support is
limited to uncompressed, PNG-compatible (grayscale, RGB and
RGBA) images.</p>

<p style="margin-left:11%; margin-top: 1em">Metadata is not
imported from the external image formats.</p>

<p style="margin-left:11%; margin-top: 1em">There is no
support for pipes, streams, extended file attributes or
access control lists.</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>png</b>(5),
<b>libpng</b>(3), <b>zlib</b>(3), <b>pngcrush</b>(1),
<b>pngrewrite</b>(1).</p>

<h2>STANDARDS
<a name="STANDARDS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The files
produced by <b>OptiPNG</b> are compliant with
<b>PNG&minus;2003</b>: <br>
Glenn Randers-Pehrson et al. <i>Portable Network Graphics
(PNG) Specification, Second Edition</i>. <br>
W3C Recommendation 10 November 2003; ISO/IEC IS 15948:2003
(E). <tt><br>
http://www.w3.org/TR/PNG/</tt></p>

<h2>AUTHOR
<a name="AUTHOR"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><tt><b>OptiPNG</b></tt>
is written and maintained by Cosmin Truta.</p>

<p style="margin-left:11%; margin-top: 1em">This manual
page was originally written by Nelson A. de Oliveira for the
Debian Project. It was later updated by Cosmin Truta, and is
now part of the <b>OptiPNG</b> distribution.</p>
<hr>
</body>
</html>
