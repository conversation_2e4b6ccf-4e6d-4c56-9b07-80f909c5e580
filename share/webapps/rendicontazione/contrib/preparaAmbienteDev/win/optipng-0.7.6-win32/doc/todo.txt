OptiPNG - TO-DO list
====================

- Compression improvements:
  Use zlib's deflateTune().
  Use 7zip's and/or zop<PERSON>li's powerful deflation engine.

- Speed improvements:
  Avoid repeated filtering when trying a filter value more than once.

- Text chunk optimization:
  Smart selection between tEXt and zTXt.
  Smart selection between uncompressed iTXt and compressed iTXt.

- Improved support for reading external image formats
  (e.g. compressed TIFF).

- More file recovery features.

- Input from stdin; output to stdout:
    cat infile | optipng > outfile
    optipng -stdout infile > outfile

- Optimization of an entire directory, with and without subdirectory
  recursion:
    optipng dir/
    optipng -recursive dir/

- Support for conversion to a desired bit depth and color type:
    optipng -b16 -c6 files ...

- Support for handling metadata, e.g.:
    optipng -set tEXt=<keyword>,<text>     # add or update chunk
    optipng -set sRGB=0                    # add or update chunk
    optipng -set image.precision=<n>       # set the precision of all samples
    optipng -set image.alpha.precision=<n> # set the precision of alpha samples
    optipng -reset image.alpha             # make the image fully opaque
    optipng -strip hIST,sPLT               # strip hIST and sPLT
    optipng -strip all -protect iCCP       # strip all metadata except iCCP

- Parallelization on multi-processor/multi-core machines:
    optipng -jobs 4 files ...

- A shared library (e.g. optipng.dll), to facilitate the development of
  PNG-optimizing GUI applications and plugins.
