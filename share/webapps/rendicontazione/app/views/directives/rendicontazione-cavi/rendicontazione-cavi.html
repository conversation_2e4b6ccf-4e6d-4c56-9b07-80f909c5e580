<form class="form" name="formRendicontazione" novalidate>

	<div class="form-group" ng-if="_handleWorkDates">
		<div class="col-sm-12">
			<label for="startWorkDate">
				<translate>Start work date</translate><sup>*</sup>
				<span
					class="glyphicon glyphicon-alert text-danger"
					ng-if="!datesAreValid"
					uib-tooltip="{{ 'End work date must be greater or equal than start work date' | translate }}"
				></span>
			</label>
			<div class="input-group" ng-class="{ 'has-error': (formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) || !datesAreValid }">
				<span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
				<input type="text" class="form-control"
					id="startWorkDate"
					name="startWorkDate"
					ng-model="workDates.start"
					ng-required="true"
					ng-disabled="allFieldsDisabled"
					ng-click="startWorkDateOpen = true"
					ng-change="changeMinAndMaxDates()"
					uib-datepicker-popup="{{startDateOptions.format}}"
					is-open="startWorkDateOpen"
					datepicker-options="startDateOptions"
					current-text="{{ 'Today' | translate }}"
					clear-text="{{ 'Clear' | translate }}"
					close-text="{{ 'Close' | translate }}"
				/>
				<span class="input-group-btn">
					<button type="button"
						class="btn"
						ng-class="{ 'btn-danger': (formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) || !datesAreValid, 'btn-default': !(formRendicontazione.startWorkDate.$dirty && formRendicontazione.startWorkDate.$invalid) && datesAreValid }"
						ng-disabled="allFieldsDisabled"
						ng-click="startWorkDateOpen = true"><i
						class="glyphicon glyphicon-calendar"></i>
					</button>
				</span>
			</div>
		</div>
	</div>

	<div class="form-group" ng-if="_handleWorkDates">
		<div class="col-sm-12">
			<label for="endWorkDate">
				<translate>End work date</translate><sup>*</sup>
				<span
					class="glyphicon glyphicon-alert text-danger"
					ng-if="!datesAreValid"
					uib-tooltip="{{ 'End work date must be greater or equal than start work date' | translate }}"
				></span>
			</label>
			<div class="input-group" ng-class="{ 'has-error': (formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) || !datesAreValid }">
				<span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
				<input type="text" class="form-control"
					id="endWorkDate"
					name="endWorkDate"
					ng-model="workDates.end"
					ng-required="true"
					ng-disabled="allFieldsDisabled"
					ng-click="endWorkDateOpen = true"
					ng-change="changeMinAndMaxDates()"
					uib-datepicker-popup="{{endDateOptions.format}}"
					is-open="endWorkDateOpen"
					datepicker-options="endDateOptions"
					current-text="{{ 'Today' | translate }}"
					clear-text="{{ 'Clear' | translate }}"
					close-text="{{ 'Close' | translate }}"
				/>
				<span class="input-group-btn">
					<button type="button"
						class="btn"
						ng-class="{ 'btn-danger': (formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) || !datesAreValid, 'btn-default': !(formRendicontazione.endWorkDate.$dirty && formRendicontazione.endWorkDate.$invalid) && datesAreValid }"
						ng-disabled="allFieldsDisabled"
						ng-click="endWorkDateOpen = true"><i
						class="glyphicon glyphicon-calendar"></i>
					</button>
				</span>
			</div>
		</div>
	</div>

	<div class="form-group">
		<div class="col-sm-12">
			<div class="checkbox">
				<label>
					<input
						type="checkbox"
						ng-model="poseTypeChanges"
						ng-disabled="allFieldsDisabled"
						ng-change="poseTypeChangesOnChange()"
						name="poseTypeChanges"
						id="poseTypeChanges"
						ng-true-value="true"
						ng-false-value="false"
					>
					<translate>Changes in the pose type</translate>
				</label>
			</div>
		</div>
	</div>

	<div class="form-group">
		<div class="col-sm-12">
			<table class="table table-bordered table-condensed table-rendicontazione">
				<thead>
					<th class="col-sm-5 text-left" translate>Pose type</th>
					<th class="col-sm-2 text-right" translate translate-context="form-rendicontazione-cavi">Planned</th>
					<th colspan="2" class="col-sm-5 text-center" translate translate-context="form-rendicontazione-cavi">Done</th>
				</thead>

				<tbody>

					<tr ng-class="{ 'has-error': formRendicontazione.underground.$dirty && formRendicontazione.underground.$invalid }">
						<td class="text-left" rowspan="2">
							<translate>Optical fiber laying in mini-pipe</translate><sup ng-if="plannedLengths.underground != null">*</sup>
							<span
								class="glyphicon glyphicon-alert text-warning"
								ng-if="formRendicontazione.underground.$valid && formRendicontazione.handmaid.$valid && fuoriSoglia.underground"
								uib-tooltip="{{ thresholdWarning }}"
							></span>
						</td>
						<td class="text-right" rowspan="2">{{plannedLengths.underground}}&nbsp;<translate ng-if="plannedLengths.underground">mt.</translate></td>
						<td class="text-right"><i translate>Blowing</i></td>
						<td style="white-space: nowrap">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.underground"
								name="underground"
								id="underground"
								class="form-control input-sm text-right"
								ng-min="0"
								ng-disabled="allFieldsDisabled || (plannedLengths.underground == null && !poseTypeChanges)"
								ng-required="!allFieldsDisabled && plannedLengths.underground != null"
								ng-blur="verificaSoglia('underground')"
							>
						</td>
					</tr>
	
					<tr ng-class="{ 'has-error': formRendicontazione.handmaid.$dirty && formRendicontazione.handmaid.$invalid }">
						<td class="text-right" style="border-top: 1px solid #fff;"><i translate>Handmaid</i></td>
						<td style="white-space: nowrap; border-top: 1px solid #fff;">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.handmaid"
								name="handmaid"
								id="handmaid"
								class="form-control input-sm text-right"
								ng-min="0"
								ng-disabled="allFieldsDisabled || (plannedLengths.underground == null && !poseTypeChanges)"
								ng-required="!allFieldsDisabled && plannedLengths.underground != null"
								ng-blur="verificaSoglia('handmaid')"
							>
						</td>
					</tr>
	
					<tr ng-class="{ 'has-error': formRendicontazione.aerial.$dirty && formRendicontazione.aerial.$invalid }">
						<td class="text-left">
							<translate>Piled Aerial Laying</translate><sup ng-if="plannedLengths.aerial != null">*</sup>
							<span
								class="glyphicon glyphicon-alert text-warning"
								ng-if="formRendicontazione.aerial.$valid && fuoriSoglia.aerial"
								uib-tooltip="{{ thresholdWarning }}"
							></span>
						</td>
						<td class="text-right">{{plannedLengths.aerial}}&nbsp;<translate ng-if="plannedLengths.aerial">mt.</translate></td>
						<td class="text-right"><i>&nbsp;</i></td>
						<td style="white-space: nowrap">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.aerial"
								name="aerial"
								id="aerial"
								class="form-control input-sm text-right"
								ng-min="0"
								ng-disabled="allFieldsDisabled || (plannedLengths.aerial == null && !poseTypeChanges)"
								ng-required="!allFieldsDisabled && plannedLengths.aerial != null"
								ng-blur="verificaSoglia('aerial')"
							>
						</td>
					</tr>
	
					<tr ng-class="{ 'has-error': formRendicontazione.facade.$dirty && formRendicontazione.facade.$invalid }">
						<td class="text-left">
							<translate>Facade Air Laying</translate><sup ng-if="plannedLengths.facade != null">*</sup>
							<span
								class="glyphicon glyphicon-alert text-warning"
								ng-if="formRendicontazione.facade.$valid && fuoriSoglia.facade"
								uib-tooltip="{{ thresholdWarning }}"
							></span>
						</td>
						<td class="text-right">{{plannedLengths.facade}}&nbsp;<translate ng-if="plannedLengths.facade">mt.</translate></td>
						<td class="text-right"><i>&nbsp;</i></td>
						<td style="white-space: nowrap">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.facade"
								name="facade"
								id="facade"
								class="form-control input-sm text-right"
								ng-min="0"
								ng-disabled="allFieldsDisabled || (plannedLengths.facade == null && !poseTypeChanges)"
								ng-required="!allFieldsDisabled && plannedLengths.facade != null"
								ng-blur="verificaSoglia('facade')"
							>
						</td>
					</tr>
	
					<tr ng-class="{ 'has-error': formRendicontazione.fromStock.$dirty && formRendicontazione.fromStock.$invalid }">
						<td class="text-left">
							<translate>Start Element Stock</translate><sup>*</sup>
							<span
								class="glyphicon glyphicon-alert text-warning"
								ng-if="formRendicontazione.fromStock.$valid && fuoriSoglia.fromStock"
								uib-tooltip="{{ thresholdWarning }}"
							></span>
						</td>
						<td class="text-right">{{plannedLengths.fromStock}}&nbsp;<translate ng-if="plannedLengths.fromStock">mt.</translate></td>
						<td class="text-right"><i>&nbsp;</i></td>
						<td style="white-space: nowrap">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.fromStock"
								name="fromStock"
								id="fromStock"
								class="form-control input-sm text-right"
								ng-min="0"
								pattern="[0-9]+"
								ng-disabled="allFieldsDisabled"
								ng-required="!allFieldsDisabled"
								ng-blur="verificaSoglia('fromStock')"
							>
						</td>
					</tr>
	
					<tr ng-class="{ 'has-error': formRendicontazione.toStock.$dirty && formRendicontazione.toStock.$invalid }">
						<td class="text-left">
							<translate>End Element Stock</translate><sup>*</sup>
							<span
								class="glyphicon glyphicon-alert text-warning"
								ng-if="formRendicontazione.toStock.$valid && fuoriSoglia.toStock"
								uib-tooltip="{{ thresholdWarning }}"
							></span>
						</td>
						<td class="text-right">{{plannedLengths.toStock}}&nbsp;<translate ng-if="plannedLengths.toStock">mt.</translate></td>
						<td class="text-right"><i>{{&nbsp;}}</i></td>
						<td style="white-space: nowrap">
							<!-- ng-disabled: se non ci sono modifiche nel tipo di posa sarà possibile
							compilare solo i campi che erano stati pianificati -->
							<input
								type="number"
								ng-model="doneLengths.toStock"
								name="toStock"
								id="toStock"
								class="form-control input-sm text-right"
								ng-min="0"
								pattern="[0-9]+"
								ng-disabled="allFieldsDisabled"
								ng-required="!allFieldsDisabled"
								ng-blur="verificaSoglia('toStock')"
							>
						</td>
					</tr>

				</tbody>
			</table>
		</div>
	</div>

	<div class="form-group" ng-if="previousNote">
		<div class="col-sm-12">
			<label translate>Previous note</label>
			<div name="previousNote" ng-bind-html="previousNote|rendicontazioneCaviEncodeHtmlEntitiesFilter|rendicontazioneCaviNl2brFilter:true"></div>
		</div>
	</div>

	<div class="form-group">
		<div class="col-sm-12">
			<label for="note" translate>Note</label>
			<textarea
				class="form-control"
				ng-model="note"
				ng-disabled="allFieldsDisabled"
				name="note"
				id="note"
			></textarea>
		</div>
	</div>

</form>

<style>
.table-rendicontazione input[type=number]::-webkit-inner-spin-button, 
.table-rendicontazione input[type=number]::-webkit-outer-spin-button { 
	-webkit-appearance: none; 
	margin: 0; 
}
.table-rendicontazione tr td,
.table-rendicontazione tr th {
	vertical-align: middle !important;
}
.table-rendicontazione label {
	font-weight: normal;
}
</style>
