<div class="container-fluid">

	<div class="col-sm-12 col-md-8 col-md-offset-2">

		<div>
			<div class="pull-right hidden-xs">
				<b>Version</b> <span app-version></span>
			</div>
			<h3>{{title}}</h3>
		</div>

		<div ng-if="loading" class="col-sm-12 text-center" style="margin-top: 100px">
			<h4 class="text-center"><span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span>
				<translate>Loading...</translate>
			</h4>
		</div>

		<div
			ng-if="fatalError"
			class="row alert alert-danger"
			ng-if="accountingDone"
		>
			<p>{{fatalErrorObj.message}}</p>
			<div ng-if="fatalErrorObj.internalMessage"><p><em translate>Error detail</em>: {{fatalErrorObj.internalMessage}}</p></div>
			<div ng-if="fatalErrorObj.UUID" class="push-right small uuid">(UUID: <code>{{fatalErrorObj.UUID}}</code>)</div>
		</div>

		<div class="row" ng-if="canGoOn">
			<rendicontazione-cavi
				planned-lengths="formData.plannedLengths"
				pose-type-changes="formData.poseTypeChanges"
				note="formData.note"
				done-lengths="formData.doneLengths"
				valid="formData.valid"
			></rendicontazione-cavi>
		</div>

		<div ng-if="canGoOn" style="margin-top: 20px">
			<div class="form-group" ng-hide="accountingDone || submitConfirmation">
				<button
					type="submit"
					class="btn btn-primary"
					ng-click="rendiconta()"
					ng-disabled="!formData.valid"
				>
					<span class="glyphicon glyphicon-ok"></span>
					<translate>Submit</translate>
				</button>
				<button
					type="button"
					class="btn btn-default"
					ng-click="resetForm()"
				>
					<span class="glyphicon glyphicon-remove"></span>
					<translate>Reset</translate>
				</button>
			</div>
			<div class="alert alert-warning" ng-show="submitConfirmation">
				<p translate>Do you confirm form submission?</p>
				<p>
					<button
						type="submit"
						class="btn btn-primary"
						ng-click="confirm(true)"
						translate
					>
						Yes
					</button>
					<button
						type="button"
						class="btn btn-default"
						ng-click="confirm(false)"
						translate
					>
						No
					</button>
				</p>
			</div>
		</div>
		
		<div
			class="alert alert-success"
			ng-if="accountingDone"
		>
			<translate>Accounting done successfully!</translate><br>
			<translate>Please reload "Dettaglio attività" page until end works button "Fine" is enabled</translate>
			<div
				class="alert alert-warning"
				style="margin-top: 10px"
			>
				<h4><span class="glyphicon glyphicon-alert"></span> <translate>Please don't execute any operation except end works with button "Fine"</translate></h4>
			</div>
		</div>
	
	</div>
</div>
				
