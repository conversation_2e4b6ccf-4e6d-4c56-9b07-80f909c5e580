<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>Working+ Service Orchestrator</title>
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width">
		<!-- Place favicon.ico and apple-touch-icon.png in the root directory -->
		<!-- build:css(.) styles/vendor.css -->
		<!-- bower:css -->
		<link rel="stylesheet" href="bower_components/bootstrap/dist/css/bootstrap.css" />
		<link rel="stylesheet" href="bower_components/bootstrap/dist/css/bootstrap-theme.css" />
		<link rel="stylesheet" href="bower_components/components-font-awesome/css/font-awesome.css" />
		<link rel="stylesheet" href="bower_components/Ionicons/css/ionicons.css" />
		<link rel="stylesheet" href="bower_components/glyphicons/styles/glyphicons.css" />
		<link rel="stylesheet" href="bower_components/angular-growl-v2/build/angular-growl.css" />
		<!-- endbower -->

		<!-- endbuild -->

		<!-- build:css(.tmp) styles/main.css -->
		<link rel="stylesheet" href="styles/rendicontazione.css">
		<!-- endbuild -->

	</head>
	<body ng-app="rendicontazione">
		<!--[if lte IE 8]>
		<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
		<![endif]-->

		<main role="main" class="container">

			<div ui-view></div>

			<div ng-include="'views/alerts.html'"></div>

		</main>

		<!-- build:js(.) scripts/vendor.js -->
		<!-- bower:js -->
		<script src="bower_components/jquery/dist/jquery.js"></script>
		<script src="bower_components/angular/angular.js"></script>
		<script src="bower_components/bootstrap/dist/js/bootstrap.js"></script>
		<script src="bower_components/angular-bootstrap/ui-bootstrap-tpls.js"></script>
		<script src="bower_components/angular-ui-router/release/angular-ui-router.js"></script>
		<script src="bower_components/angular-resource/angular-resource.js"></script>
		<script src="bower_components/underscore/underscore.js"></script>
		<script src="bower_components/angular-gettext/dist/angular-gettext.js"></script>
		<script src="bower_components/angular-sanitize/angular-sanitize.js"></script>
		<script src="bower_components/angular-animate/angular-animate.js"></script>
		<script src="bower_components/angular-growl-v2/build/angular-growl.js"></script>
		<!-- endbower -->

		<!-- endbuild -->

		<!-- build:js({.tmp,app}) scripts/scripts.js -->
		<script src="scripts/app.js"></script>
		<script src="scripts/services/utils.js"></script>
		<script src="scripts/l10n.js"></script>
		<script src="scripts/services/alerts.js"></script>
		<script src="scripts/directives/version/version.js"></script>
		<script src="scripts/directives/version/version-directive.js"></script>
		<script src="scripts/directives/version/interpolate-filter.js"></script>

		<script src="scripts/controllers/rendicontazione-cavi.js"></script>
		
		<script src="scripts/directives/rendicontazione-cavi/rendicontazione-cavi.js"></script>

		<!-- endbuild -->
		<script src="i18n/angular-locale.js"></script>
		<script src="i18n/art-locale.js"></script>

	</body>
</html>
