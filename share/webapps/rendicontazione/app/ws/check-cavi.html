<%
use strict;
use warnings;
use Carp qw(verbose);
use Data::Dumper;
$Data::Dumper::Terse = 1;
use JSON;
use HTTP::Status qw(:constants :is status_message);

use WebService::Rendicontazione::Utils;

use API::ART;
use API::ART::Activity::Factory;

use WPSOWORKS;

use Locale::TextDomain ("APACHE_ASP__rendicontazione__ws_check-cavi", "$ENV{ROOT}/share/locale" );

WebService::Rendicontazione::Utils::handle_cors_request($Request, $Response, METHODS => ['GET']);

if( !WebService::Rendicontazione::Utils::is_options($Request) ) {
	
	my $cgi = new CGI;
	
	for my $k ('planningServiceId', 'user') {
		unless(defined $cgi->param($k)) {
			WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} is mandatory", param => $k));
		}
	}
	
	for my $k ('workId') {
		unless(defined $cgi->param($k) && $cgi->param($k) =~ /^\d+$/) {
			WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} is mandatory and must be an integer", param => $k));
		}
	}
	
	my $workId = $cgi->param('workId');
	my $planningServiceId = $cgi->param('planningServiceId');
	my $user = $cgi->param('user');

	my $api;
	eval {
		$api = API::ART->new(
			 ARTID		=> $ENV{ARTID}
			,USER		=> $ENV{WPSOWORKS_USER_USERNAME}
			,PASSWORD	=> $ENV{WPSOWORKS_USER_PASSWORD}
			,AUTOSAVE	=> 0
			,DEBUG		=> 0
		);
	};
	if($@) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}

	my $wpsoworks;
	eval {
		$wpsoworks = WPSOWORKS->new(
			ART => $api
		);
	};
	if ( $@ ) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}
	
	my $act = eval { API::ART::Activity::Factory->new(ART => $api, ID => $workId); };
	if ( $@ ) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}
	unless(defined $act) {
		WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_NOT_FOUND, ERROR_MSG => __('Work not found'));
	}
	
	unless($act->can_do_automatic_accounting(
		 accountingUser		=> $user
		,planningServiceId	=> $planningServiceId
	)) {
		WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
	}
	
	WebService::Rendicontazione::Utils::send_ok($Response, MSG => $act->dump());
	
}

%>