<%
use strict;
use warnings;
use Carp qw(verbose);
use Data::Dumper;
$Data::Dumper::Terse = 1;
use JSON;
use HTTP::Status qw(:constants :is status_message);

use WebService::Rendicontazione::Utils;

use API::ART;
use API::ART::Activity::Factory;

use WPSOWORKS;

use Locale::TextDomain ("APACHE_ASP__rendicontazione__ws_rendicontazione-cavi", "$ENV{ROOT}/share/locale" );

WebService::Rendicontazione::Utils::handle_cors_request($Request, $Response, METHODS => ['POST']);

if( !WebService::Rendicontazione::Utils::is_options($Request) ) {
	
	my $cgi = new CGI;
	my $body = eval { from_json($cgi->param('POSTDATA')); };
	if ( $@ ) {
		WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"), INTERNAL_ERROR_MSG => $@);
	}

	for my $k ('planningServiceId', 'user') {
		unless(defined $body->{$k}) {
			WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} is mandatory", param => $k));
		}
	}
	
	for my $k ('workId') {
		unless(defined $body->{$k} && $body->{$k} =~ /^\d+$/) {
			WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} is mandatory and must be an integer", param => $k));
		}
	}
	
	my $api;
	eval {
		$api = API::ART->new(
			 ARTID		=> $ENV{ARTID}
			,USER		=> $ENV{WPSOWORKS_USER_USERNAME}
			,PASSWORD	=> $ENV{WPSOWORKS_USER_PASSWORD}
			,AUTOSAVE	=> 0
			,DEBUG		=> 0
		);
	};
	if($@) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}

	my $wpsoworks;
	eval {
		$wpsoworks = WPSOWORKS->new(
			ART => $api
		);
	};
	if ( $@ ) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}
	

	my $act = eval { API::ART::Activity::Factory->new(ART => $api, ID => $body->{workId}); };
	if ( $@ ) {
		WebService::Rendicontazione::Utils::send_ko($Response, INTERNAL_ERROR_MSG => $@);
	}
	unless(defined $act) {
		WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_NOT_FOUND, ERROR_MSG => __('Work not found'));
	}
	
	my $props = {
		accountingData => {}
	};
	
	$props->{accountingUser} = $body->{user} if defined $body->{user};
	$props->{accountingNote} = $body->{note} if defined $body->{note};
	$props->{planningServiceId} = $body->{planningServiceId} if defined $body->{planningServiceId};
	$props->{accountingData}->{poseTypeChanges} = $body->{poseTypeChanges} if defined $body->{poseTypeChanges};
	$props->{accountingData}->{doneUndergroundLength} = $body->{underground} if defined $body->{underground};
	$props->{accountingData}->{doneHandmaidLength} = $body->{handmaid} if defined $body->{handmaid};
	$props->{accountingData}->{doneAerialLength} = $body->{aerial} if defined $body->{aerial};
	$props->{accountingData}->{doneFacadeLength} = $body->{facade} if defined $body->{facade};
	$props->{accountingData}->{doneFromStockLength} = $body->{fromStock} if defined $body->{fromStock};
	$props->{accountingData}->{doneToStockLength} = $body->{toStock} if defined $body->{toStock};
	
	unless($act->step(
		 ACTION			=> 'RENDICONTAZIONE'
		,DESCRIPTION	=> __('Automatic accounting')
		,PROPERTIES		=> $props
	)) {
		WebService::Rendicontazione::Utils::send_ko($Response, CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
	}
	
	$api->save();
	
	WebService::Rendicontazione::Utils::send_ok($Response, CODE => HTTP_NO_CONTENT);
	
}

%>
