<%
use strict;
use warnings;
use Carp qw(verbose);
use Data::Dumper;
$Data::Dumper::Terse = 1;
use JSON;
use HTTP::Status qw(:constants :is status_message);

$ENV{_RENDICONTAZIONE_UUID} = $ENV{REDIRECT__RENDICONTAZIONE_UUID};

WebService::Rendicontazione::Utils::handle_cors_request($Request, $Response, METHODS => [$ENV{REDIRECT_REQUEST_METHOD}]);
$Response->{Charset} = 'utf-8';
$Response->AddHeader("content-type", "application/json");
print $ENV{REDIRECT_WS_ERROR_JSON};
%>