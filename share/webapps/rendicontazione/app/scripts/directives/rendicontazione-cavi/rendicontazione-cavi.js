(function () {

	'use strict';

	function rendicontazioneCaviCtrl($scope, $timeout, _, gettextCatalog, rendicontazioneCaviConfig) {

		$scope.$on('formRendicontazioneCaviReset', function () {
			reset();
		});

		$scope.$on('formRendicontazioneCaviDisableAllFields', function () {
			$scope.allFieldsDisabled = true;
		});

		$scope.$on('formRendicontazioneCaviEnableAllFields', function () {
			$scope.allFieldsDisabled = false;
		});

		$scope.$watchGroup([ 'workDates.start', 'workDates.end', 'formRendicontazione.$valid' ], function(newValue) {
			var start = newValue[0];
			var end = newValue[1];
			var formValid = newValue[2];
			if($scope._handleWorkDates) {
				$scope.valid = false;
				$scope.datesAreValid = true;
				if(start && end) {
					if(end >= start) {
						$scope.datesAreValid = true;
					} else {
						$scope.datesAreValid = false;
					}
				}
				if(start && end && formValid) {
					if(end >= start) {
						$scope.valid = true;
					}
				}
			} else {
				$scope.valid = formValid;
			}
			$scope.startWorkDate = start;
			$scope.endWorkDate = end;
		});

		// soglia percentuale che fa emettere un warning se il valore inserito va oltre soglia rispetto al valore planned
		var threshold = Number($scope.threshold) || rendicontazioneCaviConfig.threshold;

		/// Italiano: Il valore inserito differisce dal valore pianificato oltre la soglia del 15%
		$scope.thresholdWarning = gettextCatalog.getString('The entered value differs from the planned value above the {{perc}}% threshold', { perc: threshold*100});

		$scope.fuoriSoglia = {};
		if($scope.poseTypeChanges !== true && $scope.poseTypeChanges !== false) {
			$scope.poseTypeChanges = false;
		}

		$scope.allFieldsDisabled = false;

		$scope._handleWorkDates = $scope.handleWorkDates === '1';
		$scope.workDates = {};
		$scope.startDateOptions = {
			format: $scope.dateFormat,
			startingDay: 1
		};
		$scope.endDateOptions = {
			format: $scope.dateFormat,
			startingDay: 1
		};
		$scope.datesAreValid = true;

		$scope.changeMinAndMaxDates = function() {
			if($scope.workDates.start > $scope.workDates.end){
				$scope.workDates.end = undefined;
			}
			$scope.endDateOptions.minDate = new Date($scope.workDates.start);
		};

		var originalPoseTypeChanges = $scope.poseTypeChanges;
		var originalNote = $scope.note;
		var originalLengths = angular.copy($scope.doneLengths);
		var originalStartWorkDate;
		var originalEndWorkDate;
		if($scope._handleWorkDates) {
			if(_.isUndefined($scope.startWorkDate)) {
				$scope.startWorkDate = null;
			}
			if(_.isUndefined($scope.endWorkDate)) {
				$scope.endWorkDate = null;
			}
			originalStartWorkDate = angular.copy($scope.startWorkDate);
			originalEndWorkDate = angular.copy($scope.endWorkDate);
			$scope.workDates = {
				start: $scope.startWorkDate,
				end: $scope.endWorkDate
			};
			$scope.changeMinAndMaxDates();
		}

		$scope.poseTypeChangesOnChange = function() {
			// se non ci sono modifiche nel tipo di posa sarà possibile
			// compilare solo i campi che erano stati pianificati
			if(!$scope.poseTypeChanges) {
				if(_.isUndefined($scope.plannedLengths.underground) || _.isNull($scope.plannedLengths.underground)) {
					$scope.doneLengths.underground = originalLengths.underground;
					$scope.doneLengths.handmaid = originalLengths.handmaid;
					$scope.verificaSoglia('underground');
				}
				_.each(['aerial', 'facade'], function(field) {
					if(_.isUndefined($scope.plannedLengths[field]) || _.isNull($scope.plannedLengths[field])) {
						$scope.doneLengths[field] = originalLengths[field];
					}
					$scope.verificaSoglia(field);
				});
			}
		};

		var reset = function() {
			$scope.poseTypeChanges = originalPoseTypeChanges;
			$scope.note = originalNote;
			_.each(['underground', 'handmaid', 'aerial', 'facade', 'fromStock', 'toStock'], function(field) {
				$scope.doneLengths[field] = originalLengths[field];
				$scope.formRendicontazione[field].$setPristine();
			});
			if($scope._handleWorkDates) {
				$scope.workDates = {
					start: originalStartWorkDate,
					end: originalEndWorkDate
				};
				$scope.formRendicontazione.startWorkDate.$setPristine();
				$scope.formRendicontazione.endWorkDate.$setPristine();
				$scope.changeMinAndMaxDates();
			}
			verificaSoglie();
		};

		$scope.verificaSoglia = function(field) {
			var plannedValue;
			var doneValue;
			if(field === 'underground' || field === 'handmaid') {
				if($scope.formRendicontazione.underground.$invalid || $scope.formRendicontazione.handmaid.$invalid) {
					return;
				}
				plannedValue = ($scope.plannedLengths.underground || 0) + ($scope.plannedLengths.handmaid || 0);
				doneValue = ($scope.doneLengths.underground || 0) + ($scope.doneLengths.handmaid || 0);
				field = 'underground';
			} else {
				if($scope.formRendicontazione[field].$invalid) {
					return;
				}
				plannedValue = $scope.plannedLengths[field] || 0;
				doneValue = $scope.doneLengths[field] || 0;
			}
			// il fuori soglia viene calcolato solo se c'è un valore progettato
			if(plannedValue && (doneValue < plannedValue*(1-threshold) || doneValue > plannedValue*(1+threshold))) {
				$scope.fuoriSoglia[field] = true;
			} else {
				$scope.fuoriSoglia[field] = false;
			}
		};

		var verificaSoglie = function() {
			_.each(['underground', 'handmaid', 'aerial', 'facade', 'fromStock', 'toStock'], function(field) {
				$scope.verificaSoglia(field);
			});
		};

		// il timeout serve per eseguire dopo che il form si è creato
		$timeout(function() {
			verificaSoglie();
		});

	}

	angular.module('rendicontazioneCaviModule', ['ui.bootstrap', 'gettext'])

		.provider('rendicontazioneCaviConfig', function () {
			// default
			this.threshold = 0.15;

			this.setThreshold = function(threshold) {
				this.threshold = threshold;
			};
			this.$get = function () {
				return this;
			};
		})

		.filter('rendicontazioneCaviNl2brFilter', function($sce){
			return function(msg,isXhtml) {
				isXhtml = isXhtml || true;
				var breakTag = (isXhtml) ? '<br />' : '<br>';
				msg = (msg + '').replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1'+ breakTag +'$2');
				return $sce.trustAsHtml(msg);
			};
		})

		.filter('rendicontazioneCaviEncodeHtmlEntitiesFilter', function(){
			return function(msg) {
				return msg.replace(/[\u00A0-\u9999<>\&]/gim, function(i) {
					return '&#'+i.charCodeAt(0)+';';
				});
			};
		})

		/**
		 * direttiva rendicontazione-cavi
		 * 
		 * attributi:
		 * - planned-lengths (=): object con le lunghezze progettate
		 *   chiavi:
		 *   - underground -> activity.system.properties.plannedUndergroundLength
		 *   - aerial      -> activity.system.properties.plannedAerialLength
		 *   - facade      -> activity.system.properties.plannedFacadeLength
		 *   - fromStock   -> activity.system.properties.fromStockLength
		 *   - toStock     -> activity.system.properties.toStockLength
		 * - pose-type-changes (=): boolean, indica lo stato del radio button "Modifiche nel tipo di posa"
		 * - handle-work-dates (@): number, se vale 1 vengono visualizzati i date-picker per inserire le date lavori
		 * - start-work-date (=): Date, data inizio lavori (visualizzato solo se handle-work-dates="1")
		 * - end-work-date (=): Date, data fine lavori (visualizzato solo se handle-work-dates="1")
		 * - date-format (@): string, formato visualizzazione date
		 * - note (=): string: note
		 * - done-length (=): object con le lunghezze posate, ha le stesse chiavi dell'oggetto planned-lengths
		 *   più la chiave "handmaid"
		 * - valid (=): boolean, contiene lo stato di validità del form
		 * - threshold (@): number, se il valore done è diverso dal progettato oltre tale soglia viene visualizzato
		 *   un warning, default 0.15
		 * - previous-note (@): eventuali note precedenti
		 * 
		 * la direttiva è in ascolto sui seguenti eventi:
		 * - formRendicontazioneCaviReset: imposta il form ai valori originali
		 * - formRendicontazioneCaviDisableAllFields: disabilita tutti i campi del form
		 * - formRendicontazioneCaviEnableAllFields: abilita tutti i campi del form che è corretto siano abilitati
		 */
		.directive('rendicontazioneCavi', function() {
			return {
				restrict: 'E',
				scope: {
					plannedLengths: '=',
					poseTypeChanges: '=',
					note: '=',
					doneLengths: '=',
					handleWorkDates: '@',
					startWorkDate: '=',
					endWorkDate: '=',
					dateFormat: '@',
					valid: '=',
					threshold: '@',
					previousNote: '@'
				},
				templateUrl: 'views/directives/rendicontazione-cavi/rendicontazione-cavi.html',
				controller: rendicontazioneCaviCtrl
			};
		})

	;

})();