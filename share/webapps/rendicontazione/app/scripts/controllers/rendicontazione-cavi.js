'use strict';

function rendicontazioneCaviCtrl($stateParams, $scope, _, gettextCatalog, Alert, rendicontazioneCaviCheckService, rendicontazioneCaviRendicontaService) {
	
	$scope.workId = $stateParams.workId * 1;
	$scope.planningServiceId = $stateParams.planningServiceId;
	$scope.user = $stateParams.user; // passato via query string

	$scope.loading = true;
	$scope.canGoOn = false;
	$scope.fatalError = false;
	$scope.fatalErrorObj = {};
	$scope.accountingDone = false;

	$scope.title = gettextCatalog.getString('Cables accounting form');

	$scope.formData = {
		plannedLengths: {},
		poseTypeChanges: false,
		note: undefined,
		doneLengths: {},
		valid: undefined
	};

	var checkParams = {
		workId: $scope.workId,
		planningServiceId: $scope.planningServiceId,
		user: $scope.user
	};
	rendicontazioneCaviCheckService().get(checkParams).$promise
		.then(function(response) {
			$scope.formData.plannedLengths = {
				underground: Number(response.system.properties.plannedUndergroundLength) || undefined,
				aerial: Number(response.system.properties.plannedAerialLength) || undefined,
				facade: Number(response.system.properties.plannedFacadeLength) || undefined,
				fromStock: Number(response.system.properties.plannedFromStockLength) || undefined,
				toStock: Number(response.system.properties.plannedToStockLength) || undefined
			};
			$scope.loading = false;
			$scope.canGoOn = true;
		})
		.catch(function(err) {
			$scope.loading = false;
			$scope.fatalError = true;
			$scope.fatalErrorObj = err.data;
		});

	$scope.resetForm = function() {
		$scope.$broadcast('formRendicontazioneCaviReset');
	};

	$scope.rendiconta = function() {
		$scope.$broadcast('formRendicontazioneCaviDisableAllFields');
		$scope.submitConfirmation = true;
	};
	
	$scope.confirm = function(ok) {
		if(!ok) {
			$scope.$broadcast('formRendicontazioneCaviEnableAllFields');
			$scope.submitConfirmation = false;
			return;
		}
		var toSubmit = {
			workId: $scope.workId,
			planningServiceId: $scope.planningServiceId,
			user: $scope.user,
			poseTypeChanges: $scope.formData.poseTypeChanges,
			note: $scope.formData.note
		};
		_.each($scope.formData.doneLengths, function(value, key) {
			if(!_.isUndefined(value) && !_.isNull(value)) {
				toSubmit[key] = value;
			}
		});
		rendicontazioneCaviRendicontaService().rendiconta(toSubmit).$promise
			.then(function() {
				$scope.submitConfirmation = false;
				$scope.accountingDone = true;
			})
			.catch(function(err) {
				$scope.$broadcast('formRendicontazioneCaviEnableAllFields');
				$scope.submitConfirmation = false;
				Alert.error(err.data);
			});
	};

}

function rendicontazioneCaviCheckService($resource) {
	return function () {
		return $resource('ws/check-cavi.html', {}, {
			get: {
				method: 'GET',
				params: {}
			}
		});
	};
}

function rendicontazioneCaviRendicontaService($resource) {
	return function () {
		return $resource('ws/rendicontazione-cavi.html', {}, {
			rendiconta: {
				method: 'POST',
				params: {}
			}
		});
	};
}

angular
	.module('rendicontazione')
	.controller('rendicontazioneCaviCtrl', rendicontazioneCaviCtrl)
	.factory('rendicontazioneCaviCheckService', rendicontazioneCaviCheckService)
	.factory('rendicontazioneCaviRendicontaService', rendicontazioneCaviRendicontaService)
;

