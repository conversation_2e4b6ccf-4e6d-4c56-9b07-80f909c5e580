'use strict';

angular
	
	.module('rendicontazione', ['ui.router', 'ui.bootstrap', 'ngResource', 'ngAnimate', 'ngSanitize', 'gettext', 'angular-growl', 'Alerts', 'Utils', 'art.locale', 'version', 'rendicontazioneCaviModule'])

	.config(function($stateProvider, $urlRouterProvider, rendicontazioneCaviConfigProvider) {
		
		$urlRouterProvider.otherwise('/not-authorized');
		
		// NB: per ora è implementata solo la rendicontazione dei cavi
		$stateProvider
			.state('rendicontazioneCavi', {
				url: '/rendicontazione/cavi/:workId/:planningServiceId?user',
				templateUrl: 'views/rendicontazione-cavi.html',
				controller: 'rendicontazioneCaviCtrl'
			})
			.state('notAuthorized', {
				url: '/not-authorized',
				templateUrl: 'not-authorized.html'
			});

		rendicontazioneCaviConfigProvider.setThreshold(0.15);

	})

	.run(function (gettextCatalog, $locale) {
		// run gettextCatalog
		gettextCatalog.setCurrentLanguage($locale.id);
		gettextCatalog.debug = false;
	})

;
