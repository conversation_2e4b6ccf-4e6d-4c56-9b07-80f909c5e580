# Gestione memcached

Memcached pu� risiedere anche su una terza macchina.

Nell'attuale situazione di produzione <NAME_EMAIL>

## Start
Il comando seguenti avvia memcached
```shell
memcached -d -p 11219 -v -P $ROOT/var/lock/memcached.$(hostname).pid >>$ROOT/var/logs/memcached.$(hostname).log 2>&1
```
## Stop
Il comando seguente ferma memcached
```shell
kill `cat $ROOT/var/lock/memcached.$(hostname).pid` && rm $ROOT/var/lock/memcached.$(hostname).pid
```

