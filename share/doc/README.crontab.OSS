#
################ PROCEDURE IN CRONTAB
#
### invio SIGHUP ai demoni per far loro riaprire i file di log con la data corrente
00 00 * * * . $HOME/.bash_profile; daemons_signal_hup.sh -q
#
### aggiornamento bundle certificati
10 00 * * * . ~/.bash_profile; cat /etc/ssl/certs/*.crt /etc/ssl/certs/*.pem $HOME/var/ssl/ca/elastic_http_ca.*.crt > $HOME/var/ssl/ca/bundle.crt
#
### archiviazione log
21 04 * * * . ~/.bash_profile; YYYY=$(date +\%Y); mkdir -p $LOG_PATH/old/$YYYY/; for f in $(find $LOG_PATH/ -path $LOG_PATH/old -prune -o -type f -mtime +5 ! -name 'daemon.*' -printf '\%P\n' | grep -v '^./old$'); do if [ $(dirname $f) == '.' ]; then gzip $LOG_PATH/$f; mv $LOG_PATH/$f.gz $LOG_PATH/old/$YYYY/ >/dev/null; else gzip $LOG_PATH/$f; mkdir -p $LOG_PATH/old/$YYYY/$(dirname $f)/; mv $LOG_PATH/$f.gz $LOG_PATH/old/$YYYY/$(dirname $f)/ >/dev/null; fi; done; unset YYYY
#
