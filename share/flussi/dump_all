#!/usr/bin/env bash

# Incompatibile con bash, dove non so come istruire lo shell a gestire correttamente l'output delle liste
# senza suddividere i nomi dei tipi attività o sistema che contengano spazi

cd ${ROOT}/share/flussi

IFS=$'\n'

pushd ta
    for ta in $(./listaTipiAttivita.pl);do
        echo "TA: $ta"
        dump_flusso "$ta" > "$ta.yml"
    done
popd

pushd ts
    for ts in $(./listaTipiSistema.pl);do
        echo "TS: $ts"
        dump_ts "$ts" > "$ts.yml"
    done
popd

