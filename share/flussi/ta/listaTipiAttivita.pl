#!/usr/bin/env perl
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);

use HTML::Entities;
use List::Util qw(max);
use API::ART;

sub main {

    my $art = API::ART->new(
        ARTID    => $ENV{ARTID},
        USER     => $ENV{SCRIPT_USER} // $ENV{ART_SCRIPT_USER},
        PASSWORD => $ENV{SCRIPT_PASSWORD} // $ENV{ART_SCRIPT_PASSWORD},
    );

    my $q = q{
        select  ta.nome_tipo_attivita
        from    tipi_attivita ta
        where   ta.nome_tipo_attivita not in ('API::TEST::01','API::TEST::03','tmp1','tmp2','kartatt','KART_HISTORY')
        order by 1
    };
    my $p = $art->_dbh->create_prepare($q);
    my $r = $p->fetchall_arrayref;
	print join( qq(\n), map { $_->[0] } @{$r} ), qq(\n);
    $art->cancel; #Just in case
    return;
}

main( );

