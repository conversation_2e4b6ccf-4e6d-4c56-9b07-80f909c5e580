---
tipo_attivita: AS_BUILT
tipi_sistema:
  - SQUADRA
  - SUBAPPALTO
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: contractId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: ids
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractStartWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractEndWorkDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: RICHIESTA_APERTURA_TT
    stato_finale: ATTESA_APERTURA_TT
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_APERTURA_TT
    action: APERTURA_TT_KO
    stato_finale: KO
    gruppi:
      - ADMIN
      - AT_CIVILE
      - AT_GIUNZIONI
      - COORDINATORE_AT_CIVILE
      - COORDINATORE_AT_GIUNZIONI
      - PLAN_AND_PROGRAM
      - ROOT
  - stato_iniziale: ATTESA_APERTURA_TT
    action: APERTURA_TT_OK
    stato_finale: ATTESA_CHIUSURA_TT
    gruppi:
      - ADMIN
      - AT_CIVILE
      - AT_GIUNZIONI
      - COORDINATORE_AT_CIVILE
      - COORDINATORE_AT_GIUNZIONI
      - PLAN_AND_PROGRAM
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ttId
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_CHIUSURA_TT
    action: CHIUSURA_TT_KO
    stato_finale: KO_TT
    gruppi:
      - ADMIN
      - AT_CIVILE
      - AT_GIUNZIONI
      - COORDINATORE_AT_CIVILE
      - COORDINATORE_AT_GIUNZIONI
      - PLAN_AND_PROGRAM
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ttKOreason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_CHIUSURA_TT
    action: CHIUSURA_TT_OK
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT_CIVILE
      - AT_GIUNZIONI
      - COORDINATORE_AT_CIVILE
      - COORDINATORE_AT_GIUNZIONI
      - PLAN_AND_PROGRAM
      - ROOT
