---
nome_tipo_sistema: MACROLAVORO
descrizione: MACROLAVORO
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: category
    descrizione: category
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: category
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: estimatedDuration
    descrizione: Durata stimata dell'attività espressa in ore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: estimatedDuration
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: permitsAreaId
    descrizione: permitsAreaId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: permitsAreaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ring
    descrizione: ring
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ring
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ringId
    descrizione: ringId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ringId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: subCategory
    descrizione: subCategory
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: subCategory
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: toAssignQuantity
    descrizione: toAssignQuantity
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: toAssignQuantity
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: unitOfMeasure
    descrizione: Unità di misura
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: unitOfMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
