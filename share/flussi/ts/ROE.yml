---
nome_tipo_sistema: ROE
descrizione: ROE
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: roeId
    descrizione: roeId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: roeId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
