---
nome_tipo_sistema: CAVO
descrizione: CAVO
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: assistantNote
    descrizione: assistantNote
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: assistantNote
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: branchesCut
    descrizione: branchesCut
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: branchesCut
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cableId
    descrizione: cableId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cableId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cableName
    descrizione: cableName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cableName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cableType
    descrizione: Tipologia cavo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cableType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cadastralCode
    descrizione: cadastralCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cadastralCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cityId
    descrizione: cityId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cityId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneAerialLength
    descrizione: doneAerialLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneAerialLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneAerialLengthV
    descrizione: doneAerialLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneAerialLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneFacadeLength
    descrizione: doneFacadeLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneFacadeLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneFacadeLengthV
    descrizione: doneFacadeLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneFacadeLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneFromStockLength
    descrizione: doneFromStockLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneFromStockLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneFromStockLengthV
    descrizione: doneFromStockLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneFromStockLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneHandmaidLength
    descrizione: doneHandmaidLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneHandmaidLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneHandmaidLengthV
    descrizione: doneHandmaidLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneHandmaidLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneToStockLength
    descrizione: doneToStockLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneToStockLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneToStockLengthV
    descrizione: doneToStockLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneToStockLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneUndergroundLength
    descrizione: doneUndergroundLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneUndergroundLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: doneUndergroundLengthV
    descrizione: doneUndergroundLengthV
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: doneUndergroundLengthV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: endPlannedDate
    descrizione: endPlannedDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: endPlannedDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: endWorkDate
    descrizione: endWorkDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: endWorkDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: estimatedDuration
    descrizione: Durata stimata dell'attività espressa in ore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: estimatedDuration
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: fromNetworkElementGeoLocation
    descrizione: fromNetworkElementGeoLocation
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: fromNetworkElementGeoLocation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: fromNetworkElementId
    descrizione: Identificativo SiNFO dell'elemento di rete di partenza
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: fromNetworkElementId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: fromNetworkElementName
    descrizione: Identificativo mnemonico (nome) dell'elemento di rete di partenza
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: fromNetworkElementName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: fromNetworkElementType
    descrizione: fromNetworkElementType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: fromNetworkElementType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: infrastructureCheck
    descrizione: infrastructureCheck
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: infrastructureCheck
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: interruptedMinitubes
    descrizione: interruptedMinitubes
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: interruptedMinitubes
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: onFieldAssistant
    descrizione: onFieldAssistant
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: onFieldAssistant
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pfpName
    descrizione: pfpName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pfpName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pilingCheck
    descrizione: pilingCheck
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pilingCheck
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedAerialLength
    descrizione: Lunghezza tratta aerea progettata
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedAerialLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedBreakdown
    descrizione: plannedBreakdown
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedBreakdown
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedFacadeLength
    descrizione: Lunghezza tratta su facciata progettata
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedFacadeLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedFromStockLength
    descrizione: plannedFromStockLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedFromStockLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedToStockLength
    descrizione: plannedToStockLength
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedToStockLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedUndergroundLength
    descrizione: Lunghezza tratta sotterranea progettata
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedUndergroundLength
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pop
    descrizione: pop
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pop
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: popId
    descrizione: popId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: popId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: poseTypeChanges
    descrizione: poseTypeChanges
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: poseTypeChanges
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: poseTypeChangesV
    descrizione: poseTypeChangesV
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: poseTypeChangesV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: potential
    descrizione: Potenzialità del cavo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: potential
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: recoveryCable
    descrizione: recoveryCable
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: recoveryCable
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requestType
    descrizione: requestType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: requestType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ring
    descrizione: ring
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ring
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ringId
    descrizione: ringId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ringId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: spentTime
    descrizione: spentTime
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: spentTime
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: startPlannedDate
    descrizione: startPlannedDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: startPlannedDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: startWorkDate
    descrizione: startWorkDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: startWorkDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: teamId
    descrizione: teamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: teamId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: teamName
    descrizione: teamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: teamName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: teamNote
    descrizione: teamNote
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: teamNote
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: toNetworkElementGeoLocation
    descrizione: toNetworkElementGeoLocation
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: toNetworkElementGeoLocation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: toNetworkElementId
    descrizione: Identificativo SiNFO dell'elemento di rete d'arrivo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: toNetworkElementId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: toNetworkElementName
    descrizione: Identificativo mnemonico (nome) dell'elemento di rete d'arrivo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: toNetworkElementName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: toNetworkElementType
    descrizione: toNetworkElementType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: toNetworkElementType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: unitOfMeasure
    descrizione: Unità di misura
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: unitOfMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: workingGroupCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
