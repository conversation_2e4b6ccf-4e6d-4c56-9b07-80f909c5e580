---
nome_tipo_sistema: CANTIERE
descrizione: CANTIERE
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: cadastralCode
    descrizione: cadastralCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cadastralCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cityId
    descrizione: cityId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cityId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: estimatedDuration
    descrizione: Durata stimata dell'attività espressa in ore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: estimatedDuration
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: permitsAreaId
    descrizione: permitsAreaId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: permitsAreaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pop
    descrizione: pop
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pop
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: popId
    descrizione: popId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: popId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requestType
    descrizione: requestType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: requestType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ring
    descrizione: ring
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ring
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ringId
    descrizione: ringId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ringId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: spentTime
    descrizione: spentTime
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: spentTime
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: workingGroupCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
