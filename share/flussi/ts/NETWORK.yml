---
nome_tipo_sistema: NETWORK
descrizione: NETWORK
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: ameliaId
    descrizione: ameliaId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ameliaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: businessProject
    descrizione: businessProject
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: businessProject
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: businessProjectDesc
    descrizione: businessProjectDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: businessProjectDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: buyer
    descrizione: buyer
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: buyer
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: buyerDesc
    descrizione: buyerDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: buyerDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: canOpenPermits
    descrizione: canOpenPermits
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: canOpenPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: canOpenWorks
    descrizione: canOpenWorks
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: canOpenWorks
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: centralId
    descrizione: centralId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: centralId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerGoodsTotalAmount
    descrizione: customerGoodsTotalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerGoodsTotalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerProjectType
    descrizione: customerProjectType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerProjectType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerProjectTypeDesc
    descrizione: customerProjectTypeDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerProjectTypeDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerTechnicalAssistant
    descrizione: customerTechnicalAssistant
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerTechnicalAssistant
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerTechnicalAssistantDesc
    descrizione: customerTechnicalAssistantDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerTechnicalAssistantDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerWBE
    descrizione: customerWBE
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerWBE
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerWBEDesc
    descrizione: customerWBEDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerWBEDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerWorkingArea
    descrizione: customerWorkingArea
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerWorkingArea
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: enhancementFactorDesc
    descrizione: enhancementFactorDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: enhancementFactorDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: forecastEndDate
    descrizione: forecastEndDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: forecastEndDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: forecastStartDate
    descrizione: forecastStartDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: forecastStartDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: goodsRecipient
    descrizione: goodsRecipient
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: goodsRecipient
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: goodsSupplier
    descrizione: goodsSupplier
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: goodsSupplier
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: goodsSupplierDesc
    descrizione: goodsSupplierDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: goodsSupplierDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: isoReference
    descrizione: Riferimento ISO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: isoReference
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: messageId
    descrizione: messageId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: messageId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: MOSTotalAmount
    descrizione: MOSTotalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: MOSTotalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkDesc
    descrizione: networkDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkId
    descrizione: networkId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkStatus
    descrizione: networkStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkStatus
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: operation
    descrizione: operation
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: operation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: operationalContext
    descrizione: operationalContext
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: operationalContext
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: operationDesc
    descrizione: operationDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: operationDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: privatePermitsClosedKO
    descrizione: privatePermitsClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: privatePermitsClosedKO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: privatePermitsClosedOK
    descrizione: privatePermitsClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: privatePermitsClosedOK
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: privatePermitsOnGoing
    descrizione: privatePermitsOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: privatePermitsOnGoing
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: privatePermitsTotal
    descrizione: privatePermitsTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: privatePermitsTotal
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: propositionContractId
    descrizione: propositionContractId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: propositionContractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: propositionCurrency
    descrizione: propositionCurrency
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: propositionCurrency
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: propositionDate
    descrizione: propositionDate
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: propositionDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: propositionNumber
    descrizione: propositionNumber
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: propositionNumber
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: publicPermitsClosedKO
    descrizione: publicPermitsClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: publicPermitsClosedKO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: publicPermitsClosedOK
    descrizione: publicPermitsClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: publicPermitsClosedOK
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: publicPermitsOnGoing
    descrizione: publicPermitsOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: publicPermitsOnGoing
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: publicPermitsTotal
    descrizione: publicPermitsTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: publicPermitsTotal
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requestorContext
    descrizione: requestorContext
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: requestorContext
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: resourcesTotalAmount
    descrizione: resourcesTotalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: resourcesTotalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: SAPDivision
    descrizione: SAPDivision
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: SAPDivision
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: SAPDivisionDesc
    descrizione: SAPDivisionDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: SAPDivisionDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: servicesTotalAmount
    descrizione: servicesTotalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: servicesTotalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: summaryNetwork
    descrizione: summaryNetwork
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: summaryNetwork
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: supplier
    descrizione: supplier
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: supplier
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: supplierDesc
    descrizione: supplierDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: supplierDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: supplierGoodsTotalAmount
    descrizione: supplierGoodsTotalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: supplierGoodsTotalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: technicalSite
    descrizione: technicalSite
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: technicalSite
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: technicalSiteDesc
    descrizione: technicalSiteDesc
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: technicalSiteDesc
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: totalAmount
    descrizione: totalAmount
    tipo: FLOAT
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: totalAmount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: workingGroupCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workOrderId
    descrizione: workOrderId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workOrderId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: worksClosedKO
    descrizione: worksClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: worksClosedKO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: worksClosedOK
    descrizione: worksClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: worksClosedOK
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: worksOnGoing
    descrizione: worksOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: worksOnGoing
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: worksTotal
    descrizione: worksTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: worksTotal
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
