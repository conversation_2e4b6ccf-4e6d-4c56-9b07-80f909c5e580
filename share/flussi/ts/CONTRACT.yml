---
nome_tipo_sistema: CONTRACT
descrizione: CONTRACT
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: backgroundURL
    descrizione: backgroundURL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: backgroundURL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: companyAbbreviation
    descrizione: Sigla azienda
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: companyAbbreviation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: companyCode
    descrizione: Codice azienda
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: companyCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: companyName
    descrizione: Ragione sociale azienda
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: companyName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractClaim
    descrizione: contractClaim
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractClaim
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractName
    descrizione: contractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: externalWorkTypeId
    descrizione: Tipo Avviso / Tipo Intervento
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: externalWorkTypeId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: logoURL
    descrizione: logoURL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: logoURL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: operationalContext
    descrizione: operationalContext
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: operationalContext
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requiredBlocks
    descrizione: requiredBlocks
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    alias: requiredBlocks
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
