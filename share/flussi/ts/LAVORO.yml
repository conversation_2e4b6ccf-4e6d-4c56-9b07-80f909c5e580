---
nome_tipo_sistema: LAVORO
descrizione: LAVORO
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: accountingOperation
    descrizione: Operazione contabile
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: accountingOperation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: assetId
    descrizione: assetId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    alias: assetId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: assetIdOld
    descrizione: assetIdOld
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    alias: assetIdOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cableLaying
    descrizione: Posa Cavo
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cableLaying
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: civil
    descrizione: Lavori Civili
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: civil
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: defectWithDisservice
    descrizione: Guasto Disservito
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: defectWithDisservice
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: defectWithoutDisservice
    descrizione: Guasto Non Disservito
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: defectWithoutDisservice
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: design
    descrizione: Progettazione
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: design
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: externalSequence
    descrizione: externalSequence
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: externalSequence
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: generic
    descrizione: Generico
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: generic
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: infrastructure
    descrizione: Infrastruttura
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: infrastructure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: installationPlaceSurvey
    descrizione: Ispezione Sede Posa
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: installationPlaceSurvey
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: installationReview
    descrizione: Revisione Impianti
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: installationReview
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: junction
    descrizione: Giunzione
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: junction
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: laying
    descrizione: Posa
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: laying
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: maintenanceId
    descrizione: maintenanceId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: maintenanceId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: measurements
    descrizione: Misure
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: measurements
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkId
    descrizione: networkId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkIdOld
    descrizione: networkIdOld
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkIdOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: onFieldIntegrationDisabled
    descrizione: onFieldIntegrationDisabled
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: onFieldIntegrationDisabled
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: opticalConnectionOLT
    descrizione: opticalConnectionOLT
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: opticalConnectionOLT
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: opticalConnectionOSU
    descrizione: opticalConnectionOSU
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: opticalConnectionOSU
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: patchCord
    descrizione: Bretellaggio
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: patchCord
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pathSurvey
    descrizione: Visita Tracciato
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pathSurvey
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: planning
    descrizione: Impresa Progettazione
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: planning
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectIdOld
    descrizione: projectIdOld
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectIdOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: quarterSummary
    descrizione: Riepilogo Trimestre
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: quarterSummary
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requestType
    descrizione: requestType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: requestType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: restoration
    descrizione: restoration
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: restoration
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: spentTime
    descrizione: spentTime
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: spentTime
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: survey
    descrizione: Sopralluogo
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: survey
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: test
    descrizione: Collaudo
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: test
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: testApp
    descrizione: Collaudo App
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: testApp
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: testOTDR
    descrizione: Collaudo OTDR
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: testOTDR
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: updateDatabase
    descrizione: Aggiornamento Banca Dati
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: updateDatabase
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: updateDatabaseF1
    descrizione: updateDatabaseF1
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: updateDatabaseF1
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: updateDatabaseF2
    descrizione: updateDatabaseF2
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: updateDatabaseF2
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: workingGroupCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: worksManning
    descrizione: Sorveglianza Lavori
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: worksManning
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workType
    descrizione: workType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
