#!/usr/bin/env perl
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);

use HTML::Entities;
use List::Util qw(max);
use API::ART;

sub main {

    my $art = API::ART->new(
        ARTID    => $ENV{ARTID},
        USER     => $ENV{SCRIPT_USER} // $ENV{ART_SCRIPT_USER},
        PASSWORD => $ENV{SCRIPT_PASSWORD} // $ENV{ART_SCRIPT_PASSWORD},
    );

    my $q = q{
        select  ts.nome_tipo_sistema
        from    tipi_sistema ts
        where   ts.nome_tipo_sistema not in ('API::TEST','kart','KART_HISTORY','kart1','kart2')
        order by 1
    };
    my $p = $art->_dbh->create_prepare($q);
    my $r = $p->fetchall_arrayref;
    print join( qq(\n), map { $_->[0] } @{$r} ), qq(\n);
    $art->cancel; #Just in case
    return;
}

main( );

