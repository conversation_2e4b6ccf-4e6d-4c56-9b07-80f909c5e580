---
nome_tipo_sistema: TERMINAZIONE
descrizione: TERMINAZIONE
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: accountingQuantity
    descrizione: accountingQuantity
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: accountingQuantity
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cablingType
    descrizione: cablingType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cablingType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: inputCableId
    descrizione: inputCableId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: inputCableId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: inputCableName
    descrizione: inputCableName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: inputCableName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: inputCablePotential
    descrizione: inputCablePotential
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: inputCablePotential
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: inputCablePotentialPlanned
    descrizione: inputCablePotentialPlanned
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: inputCablePotentialPlanned
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementId
    descrizione: networkElementId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: outputCableId
    descrizione: outputCableId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: outputCableId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: outputCableName
    descrizione: outputCableName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: outputCableName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: outputCablePotential
    descrizione: outputCablePotential
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: outputCablePotential
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: outputCablePotentialPlanned
    descrizione: outputCablePotentialPlanned
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: outputCablePotentialPlanned
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: status
    descrizione: status
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: status
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: terminatedFibers
    descrizione: terminatedFibers
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: terminatedFibers
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: totalAccountingQuantity
    descrizione: totalAccountingQuantity
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: totalAccountingQuantity
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: totalAccountingQuantityV
    descrizione: totalAccountingQuantityV
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: totalAccountingQuantityV
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
