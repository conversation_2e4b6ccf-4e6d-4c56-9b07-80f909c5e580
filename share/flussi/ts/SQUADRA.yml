---
nome_tipo_sistema: SQUADRA
descrizione: SQUADRA
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: onFieldAssistant
    descrizione: onFieldAssistant
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: onFieldAssistant
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: teamId
    descrizione: teamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: teamId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: teamName
    descrizione: teamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: teamName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
