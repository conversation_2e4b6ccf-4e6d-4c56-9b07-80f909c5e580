---
nome_tipo_sistema: NODO
descrizione: NODO
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: branchesCut
    descrizione: branchesCut
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: branchesCut
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cableOver192foConnection
    descrizione: cableOver192foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cableOver192foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cable144foConnection
    descrizione: cable144foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cable144foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cable192foConnection
    descrizione: cable192foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cable192foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cable24foConnection
    descrizione: cable24foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cable24foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cable48foConnection
    descrizione: cable48foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cable48foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cable96foConnection
    descrizione: cable96foConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cable96foConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cadastralCode
    descrizione: cadastralCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cadastralCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cityId
    descrizione: cityId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cityId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: continuousCablesConnection
    descrizione: continuousCablesConnection
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: continuousCablesConnection
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: emptyingCockpit
    descrizione: emptyingCockpit
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: emptyingCockpit
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: estimatedDuration
    descrizione: Durata stimata dell'attività espressa in ore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: estimatedDuration
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: fibersPlacedJunction
    descrizione: fibersPlacedJunction
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: fibersPlacedJunction
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: foJunction
    descrizione: foJunction
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: foJunction
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: infrastructureCheck
    descrizione: infrastructureCheck
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: infrastructureCheck
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: interruptedMinitubes
    descrizione: interruptedMinitubes
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: interruptedMinitubes
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: junctionSite
    descrizione: junctionSite
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: junctionSite
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: junctionType
    descrizione: junctionType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: junctionType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: lackOfMaterial
    descrizione: lackOfMaterial
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: lackOfMaterial
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: latitude
    descrizione: latitude
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: latitude
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: longitude
    descrizione: longitudelongitude
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: longitude
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementFulfilled
    descrizione: networkElementFulfilled
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementFulfilled
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementId
    descrizione: networkElementId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementIsDone
    descrizione: networkElementIsDone
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementIsDone
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementName
    descrizione: networkElementName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkElementType
    descrizione: networkElementType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkElementType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: notAccessibleCockpit
    descrizione: notAccessibleCockpit
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: notAccessibleCockpit
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: onFieldAssistant
    descrizione: onFieldAssistant
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: onFieldAssistant
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pfpMeasure
    descrizione: pfpMeasure
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pfpMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pfpName
    descrizione: pfpName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pfpName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pfsMeasure
    descrizione: pfsMeasure
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pfsMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pfsPosition
    descrizione: pfsPosition
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pfsPosition
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pilingCheck
    descrizione: pilingCheck
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pilingCheck
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedBreakdown
    descrizione: plannedBreakdown
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedBreakdown
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: plannedSplicesAndTermChanges
    descrizione: plannedSplicesAndTermChanges
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: plannedSplicesAndTermChanges
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pop
    descrizione: pop
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pop
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: popId
    descrizione: popId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: popId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: prewiredPFS
    descrizione: prewiredPFS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: prewiredPFS
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ptaMeasure
    descrizione: ptaMeasure
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ptaMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ptaSite
    descrizione: ptaSite
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ptaSite
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pteMeasure
    descrizione: pteMeasure
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pteMeasure
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pteSite
    descrizione: pteSite
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pteSite
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: recoveryCable
    descrizione: recoveryCable
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: recoveryCable
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: requestType
    descrizione: requestType
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: requestType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ring
    descrizione: ring
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ring
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ringId
    descrizione: ringId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ringId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: spentTime
    descrizione: spentTime
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: spentTime
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: splitterPermutations
    descrizione: splitterPermutations
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: splitterPermutations
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: splitter116Placing
    descrizione: splitter116Placing
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: splitter116Placing
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: splitter14Placing
    descrizione: splitter14Placing
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: splitter14Placing
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: terminations
    descrizione: terminations
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: terminations
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: workingGroupCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
