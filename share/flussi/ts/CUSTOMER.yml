---
nome_tipo_sistema: CUSTOMER
descrizione: CUSTOMER
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: backgroundURL
    descrizione: backgroundURL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: backgroundURL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerName
    descrizione: customerName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: logoURL
    descrizione: logoURL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: logoURL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
