set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_cavi_lavori_dettaglio as
select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,cables."cableId"
  ,cables."cableName"
  ,cables."status"
  ,cables."fromNetworkElementName"
  ,cables."fromNetworkElementType"
  ,cables."toNetworkElementName"
  ,cables."toNetworkElementType"
  ,cables."cableType"
  ,cables."potential"
  ,cables."unitOfMeasure"
  ,cables."fromNetworkElementId"
  ,cables."fromNetworkElementGeoLocation"
  ,cables."toNetworkElementId"
  ,cables."toNetworkElementGeoLocation"
  ,cables."plannedUndergroundLength"
  ,cables."plannedAerialLength"
  ,cables."plannedFacadeLength"
  ,cables."doneUndergroundLength" + cables."doneHandmaidLength" "doneUndergroundLength"
  ,cables."doneAerialLength"
  ,cables."doneFacadeLength"
  ,cables."estimatedDuration"
  ,lav."id" "activityId"
  ,lav."status" "activityStatus"
  ,lav."maker" "activityMaker"
  ,lav."teamId" "activityTeamId"
  ,lav."startPlannedDate" "activityStartPlannedDate"
  ,lav."startWorkDate" "activityStartWorkDate"
  ,lav."endPlannedDate" "activityEndPlannedDate" 
  ,lav."estimatedDuration" "activityEstimatedDuration"
  ,lav."endWorkDate" "activityEndWorkDate"
  ,lav."spentTime" "activitySpentTime"
  ,lav."subContractCode" "activitySubContractCode"
  ,lav."techninalAssistantsAssignee" "activityTechAssAssignee"
  ,lav."asBuiltStatus" "activityAsBuiltStatus"
  ,GET_LAVORO_VIRTUAL_STATUS(
     obj_status => cables."status"
    ,activity_status => lav."status"
    ,maker => lav."maker"
    ,team_id => lav."teamId"
    ,start_planned_date => lav."startPlannedDate"
    ,start_work_date => lav."startWorkDate"
  ) "virtualStatus"
from
  v_cavi cables
    join v_progetti pfp on cables."customerId" = pfp."customerId" and cables."contractId" = pfp."contractId" and cables."projectId" = pfp."projectId"
    left join v_cavi_lavori lav on lav."cableId" = cables."cableId" and lav."customerId" = pfp."customerId" and lav."contractId" = pfp."contractId" and lav."projectId" =  pfp."projectId"
;

quit
