set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop table NODI_RIEPILOGO;

CREATE TABLE NODI_RIEPILOGO
(
   "customerId" VARCHAR2(20) NOT NULL
  ,"contractId" VARCHAR2(20) NOT NULL
  ,"pop" VARCHAR2(4000) NOT NULL
  ,"ring" VARCHAR2(4000) NOT NULL
  ,"projectId" NUMBER NOT NULL
  ,"pfpId" VARCHAR2(4000) NOT NULL
  ,"pfpName" VARCHAR2(4000) NOT NULL
  ,"networkElementId" NUMBER NOT NULL
  ,"networkElementType" VARCHAR2(4000) NOT NULL
  ,"networkElementName" VARCHAR2(4000) NOT NULL
  ,"networkElementGeoLocation" VARCHAR2(4000)
  ,"latitude" VARCHAR2(4000)
  ,"longitude" VARCHAR2(4000)
  ,"networkElementStatus" VARCHAR2(20) NOT NULL
  ,"networkElementContainerStatus" VARCHAR2(20) NOT NULL
  ,"total" NUMBER NOT NULL
  ,"notWorkable" NUMBER NOT NULL
  ,"workable" NUMBER NOT NULL
  ,"done" NUMBER NOT NULL
  ,"estimatedDuration" NUMBER
  ,"warnings" VARCHAR2(4000 CHAR)
  ,"runId" NUMBER NOT NULL
  ,"historicizingRunId" NUMBER
)
;
create index NODI_RIEPILOGO_IDX01 on NODI_RIEPILOGO ("customerId", "contractId", "projectId", "historicizingRunId");

COMMENT ON TABLE NODI_RIEPILOGO  IS 'Tabella riepilogo nodi aggregata';
COMMENT ON COLUMN NODI_RIEPILOGO."customerId" IS 'Identificativo cliente';
COMMENT ON COLUMN NODI_RIEPILOGO."contractId" IS 'Identificativo contratto';
COMMENT ON COLUMN NODI_RIEPILOGO."pop" IS 'Pop dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."ring" IS 'Anello dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."projectId" IS 'Id progetto dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."pfpId" IS 'Id pfp dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."pfpName" IS 'Nome del pfp dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementId" IS 'Progressivo univoco del delimitatore di rete in SINFO';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementName" IS 'Identificativo con progressivo numerico dell''elemento di rete da "giuntare" (PFP10, PTE44, PTA13, ecc.)';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementType" IS 'Tipo di delimitatore di rete in SINFO (PFP, PFS, PTE, PTA, PD, GL, ...)';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementGeoLocation" IS 'Coordinate georeferenziali dell''elemento di infrastruttura reale/virtuale che contiene il delimitatore di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."latitude" IS 'Latitudine dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."longitude" IS 'Longitudine dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementStatus" IS 'Stato dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."networkElementContainerStatus" IS 'Stato del delimitatore dell''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."total" IS 'Totale delle giunzioni attestate sull''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."notWorkable" IS 'Totale delle giunzioni non lavorabili attestate sull''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."workable" IS 'Totale delle giunzioni lavorabili attestate sull''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."done" IS 'Totale delle giunzioni eseguite attestate sull''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."estimatedDuration" IS 'Durata stimata dell''esecuzione delle giunzioni attestate sull''elemento di rete';
COMMENT ON COLUMN NODI_RIEPILOGO."warnings" IS 'Eventuali warnings sul calcolo della durata stimata';
COMMENT ON COLUMN NODI_RIEPILOGO."runId" IS 'Id del campionamento';
COMMENT ON COLUMN NODI_RIEPILOGO."historicizingRunId" IS 'Id del campionamento che ha sostituito quello indicato nella riga, se null si tratta del campionamento più recente';

grant select, insert, update on nodi_riepilogo to works_art;

create or replace view v_nodi_lavori_riepilogo as
select
   n."customerId"
  ,n."contractId"
  ,n."pop"
  ,n."ring"
  ,n."projectId"
  ,n."pfpId"
  ,n."pfpName"
  ,n."networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."latitude"
  ,n."longitude"
  ,n."networkElementStatus"
  ,n."networkElementContainerStatus"
  ,n."total"
  ,n."notWorkable"
  ,n."workable"
  ,n."done"
  ,n."estimatedDuration"
  ,n."warnings"
  ,lav."id" "workId"
  ,lav."workStatus"
  ,lav."asBuiltStatus"
from
  works.NODI_RIEPILOGO n
    left join v_nodi_lavori lav on lav."networkElementId" = n."networkElementId" and lav."customerId" = n."customerId" and lav."contractId" = n."contractId" and lav."projectId" = n."projectId"
where 1=1
  and n."historicizingRunId" is null
;

quit
