set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

-- abilitazione property table per il tipo_attivita
DECLARE
 NOME_TIPO_ATTIVITA_P TIPI_ATTIVITA.NOME_TIPO_ATTIVITA%TYPE := 'LAVORO';
 disabilitato_p TDTA_DTAFLAT.DISABILITATO%TYPE := null; -- valorizzato a Y o null

 ID_TIPO_ATTIVITA_P TIPI_ATTIVITA.ID_TIPO_ATTIVITA%TYPE := 0;

 BEGIN
 SELECT id_tipo_attivita INTO id_tipo_attivita_p from tipi_attivita where nome_tipo_attivita = nome_tipo_attivita_p;

 insert into ta_dtaflat values (id_tipo_attivita_p,'PT_LAVORO','Y');

end;
/

-- disabilitazione di tutti i TDTA che non devono essere nella PT associata
declare
 disabilitato_p TDTA_DTAFLAT.DISABILITATO%TYPE := 'Y'; -- valorizzato a Y o null
 check_dta TIPI_DATI_TECNICI_ATTIVITA.DESCRIZIONE%TYPE;
 ID_TIPO_ATTIVITA_P TIPI_ATTIVITA.ID_TIPO_ATTIVITA%TYPE := 0;
 type wyc is varying array(40) of tipi_attivita.nome_tipo_attivita%type;
 nomi_tipi_attivita wyc;
 type varray_varchar is varying array(40) of tipi_dati_tecnici_attivita.descrizione%type;
 var_varray_varchar varray_varchar;
 
begin
  nomi_tipi_attivita := wyc (
    'LAVORO'
  );
  var_varray_varchar  := varray_varchar(
    'asBuiltId'
    ,'eventType'
    ,'note'
    ,'planningServiceCode'
    ,'planningServiceDescription'
    ,'planningServiceId'
    ,'planningServiceResult'
    ,'ttKOreason'
  );

  for att in 1 .. nomi_tipi_attivita.count loop

    DBMS_OUTPUT.PUT_LINE('Tipo Attivita := ' || nomi_tipi_attivita(att));

    SELECT id_tipo_attivita INTO id_tipo_attivita_p from tipi_attivita where nome_tipo_attivita = nomi_tipi_attivita(att);

    for elem in 1 .. var_varray_varchar.count loop

     DBMS_OUTPUT.PUT_LINE(' insert -> ' || var_varray_varchar(elem) || ' - disabilitato -> ' || disabilitato_p);

     select descrizione into check_dta from tipi_dati_Tecnici_Attivita where descrizione=var_varray_varchar(elem);

     INSERT INTO TDTA_DTAFLAT(ID_TIPO_ATTIVITA,COL_SEQ,ID_TIPO_DATO_TECNICO_ATTIVITA,NOME_COLONNA,DISABILITATO,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,CHAR_USED,DATE_FORMAT)
     SELECT id_tipo_attivita_p,nvl((
      SELECT MAX(COL_SEQ)+1 FROM TDTA_DTAFLAT
      WHERE ID_TIPO_ATTIVITA = id_tipo_attivita_p
     ),0),id_tipo_dato_tecnico_attivita,null,disabilitato_p,'VARCHAR2',null,null,null,null,null
     FROM
      TIPI_DATI_TECNICI_ATTIVITA TDTA
     WHERE
      TDTA.DESCRIZIONE=var_varray_varchar(elem);

    end loop;

  end loop;

 end;
/

-- abilitazione dei TDTA in formato TIMESTAMP WITH TIME ZONE (NB: il valore dei DTA deve essere una stringa in formato ISODATE)
declare
 disabilitato_p TDTA_DTAFLAT.DISABILITATO%TYPE := null; -- valorizzato a Y o null
 check_dta TIPI_DATI_TECNICI_ATTIVITA.DESCRIZIONE%TYPE;
 ID_TIPO_ATTIVITA_P TIPI_ATTIVITA.ID_TIPO_ATTIVITA%TYPE := 0;
 type wyc is varying array(40) of tipi_attivita.nome_tipo_attivita%type;
 nomi_tipi_attivita wyc;
 type varray_varchar is varying array(40) of tipi_dati_tecnici_attivita.descrizione%type;
 var_varray_varchar varray_varchar;

 begin
  nomi_tipi_attivita := wyc (
    'LAVORO'
  );
  var_varray_varchar  := varray_varchar(
    'endPlannedDate'
    ,'endWorkDate'
    ,'slaEnd'
    ,'slaStart'
    ,'startPlannedDate'
    ,'startWorkDate'
    ,'subContractStartWorkDate'
    ,'subContractEndWorkDate'
  );

  for att in 1 .. nomi_tipi_attivita.count loop

    DBMS_OUTPUT.PUT_LINE('Tipo Attivita := ' || nomi_tipi_attivita(att));

    SELECT id_tipo_attivita INTO id_tipo_attivita_p from tipi_attivita where nome_tipo_attivita = nomi_tipi_attivita(att);

    for elem in 1 .. var_varray_varchar.count loop

     DBMS_OUTPUT.PUT_LINE(' insert -> ' || var_varray_varchar(elem) || ' - disabilitato -> ' || disabilitato_p);

     select descrizione into check_dta from tipi_dati_Tecnici_Attivita where descrizione=var_varray_varchar(elem);

     INSERT INTO TDTA_DTAFLAT(ID_TIPO_ATTIVITA,COL_SEQ,ID_TIPO_DATO_TECNICO_ATTIVITA,NOME_COLONNA,DISABILITATO,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,CHAR_USED,DATE_FORMAT)
     SELECT id_tipo_attivita_p,nvl((
      SELECT MAX(COL_SEQ)+1 FROM TDTA_DTAFLAT
      WHERE ID_TIPO_ATTIVITA = id_tipo_attivita_p
     ),0),id_tipo_dato_tecnico_attivita,null,disabilitato_p,'TIMESTAMP WITH TIME ZONE',null,null,null,null,'yyyy-mm-dd"T"hh24:mi:ss.fftzh:tzm'
     FROM
      TIPI_DATI_TECNICI_ATTIVITA TDTA
     WHERE
      TDTA.DESCRIZIONE=var_varray_varchar(elem);

    end loop;

  end loop;

 end;
/

-- abilitazione di tutti i TDTA booleani
declare
 disabilitato_p TDTA_DTAFLAT.DISABILITATO%TYPE := null; -- valorizzato a Y o null
 check_dta TIPI_DATI_TECNICI_ATTIVITA.DESCRIZIONE%TYPE;
 ID_TIPO_ATTIVITA_P TIPI_ATTIVITA.ID_TIPO_ATTIVITA%TYPE := 0;
 type wyc is varying array(40) of tipi_attivita.nome_tipo_attivita%type;
 nomi_tipi_attivita wyc;
 type varray_varchar is varying array(40) of tipi_dati_tecnici_attivita.descrizione%type;
 var_varray_varchar varray_varchar;
 
begin
  nomi_tipi_attivita := wyc (
    'LAVORO'
  );
  var_varray_varchar  := varray_varchar(
    'accountingDone',
    'onFieldIntegrationDisabled',
    'survey',
    'cableLaying',
    'junction',
    'civil',
    'updateDatabase',
    'test',
    'flagFIR',
    'updateDatabaseF2',
    'opticalConnectionOLT',
    'opticalConnectionOSU',
    'updateDatabaseF1',
    'restoration',
    'design',
    'patchCord',
    'pathSurvey',
    'installationPlaceSurvey',
    'installationReview',
    'worksManning',
    'measurements',
    'quarterSummary',
    'defectWithDisservice',
    'defectWithoutDisservice',
    'infrastructure'
  );

  for att in 1 .. nomi_tipi_attivita.count loop

    DBMS_OUTPUT.PUT_LINE('Tipo Attivita := ' || nomi_tipi_attivita(att));

    SELECT id_tipo_attivita INTO id_tipo_attivita_p from tipi_attivita where nome_tipo_attivita = nomi_tipi_attivita(att);

    for elem in 1 .. var_varray_varchar.count loop

     DBMS_OUTPUT.PUT_LINE(' insert -> ' || var_varray_varchar(elem) || ' - disabilitato -> ' || disabilitato_p);

     select descrizione into check_dta from tipi_dati_Tecnici_Attivita where descrizione=var_varray_varchar(elem);

     INSERT INTO TDTA_DTAFLAT(ID_TIPO_ATTIVITA,COL_SEQ,ID_TIPO_DATO_TECNICO_ATTIVITA,NOME_COLONNA,DISABILITATO,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,CHAR_USED,DATE_FORMAT)
     SELECT id_tipo_attivita_p,nvl((
      SELECT MAX(COL_SEQ)+1 FROM TDTA_DTAFLAT
      WHERE ID_TIPO_ATTIVITA = id_tipo_attivita_p
     ),0),id_tipo_dato_tecnico_attivita,null,disabilitato_p,'CHAR',1,null,null,null,null
     FROM
      TIPI_DATI_TECNICI_ATTIVITA TDTA
     WHERE
      TDTA.DESCRIZIONE=var_varray_varchar(elem);

    end loop;

  end loop;

 end;
/

-- abilitazione dei TDTA in formato NUMBER (NB: il valore dei DTA deve essere una stringa contenente un NUMBER)
declare
 disabilitato_p TDTA_DTAFLAT.DISABILITATO%TYPE := null; -- valorizzato a Y o null
 check_dta TIPI_DATI_TECNICI_ATTIVITA.DESCRIZIONE%TYPE;
 ID_TIPO_ATTIVITA_P TIPI_ATTIVITA.ID_TIPO_ATTIVITA%TYPE := 0;
 type wyc is varying array(40) of tipi_attivita.nome_tipo_attivita%type;
 nomi_tipi_attivita wyc;
 type varray_varchar is varying array(40) of tipi_dati_tecnici_attivita.descrizione%type;
 var_varray_varchar varray_varchar;
 
begin
  nomi_tipi_attivita := wyc (
    'LAVORO'
  );
  var_varray_varchar  := varray_varchar(
    'spentTime'
    ,'workingGroupCode'
    ,'__DTC_CMI'
    ,'__DTC_DCO'
    ,'__DTC_DOF'
    ,'__DTC_VAR'
    ,'__DTC_WKI'
    ,'__DTC_FIR'
  );

  for att in 1 .. nomi_tipi_attivita.count loop

    DBMS_OUTPUT.PUT_LINE('Tipo Attivita := ' || nomi_tipi_attivita(att));

    SELECT id_tipo_attivita INTO id_tipo_attivita_p from tipi_attivita where nome_tipo_attivita = nomi_tipi_attivita(att);

    for elem in 1 .. var_varray_varchar.count loop

     DBMS_OUTPUT.PUT_LINE(' insert -> ' || var_varray_varchar(elem) || ' - disabilitato -> ' || disabilitato_p);

     select descrizione into check_dta from tipi_dati_Tecnici_Attivita where descrizione=var_varray_varchar(elem);

     INSERT INTO TDTA_DTAFLAT(ID_TIPO_ATTIVITA,COL_SEQ,ID_TIPO_DATO_TECNICO_ATTIVITA,NOME_COLONNA,DISABILITATO,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,CHAR_USED,DATE_FORMAT)
     SELECT id_tipo_attivita_p,nvl((
      SELECT MAX(COL_SEQ)+1 FROM TDTA_DTAFLAT
      WHERE ID_TIPO_ATTIVITA = id_tipo_attivita_p
     ),0),id_tipo_dato_tecnico_attivita,null,disabilitato_p,'NUMBER',null,null,null,null,null
     FROM
      TIPI_DATI_TECNICI_ATTIVITA TDTA
     WHERE
      TDTA.DESCRIZIONE=var_varray_varchar(elem);

    end loop;

  end loop;

 end;
/

-- tutti i TDTA non esplicitamente citati nelle sezioni precedenti saranno abilitati alla PT come VARCHAR2(4000)
/*
  select * from V_TDTA_DTAFLAT v
    join tipi_attivita ta on ta.id_tipo_attivita = v.id_tipo_Attivita
  where ta.nome_tipo_attivita = ?
  and v.abilitata = 'Y'
  and id_tipo_dato_Tecnico_attivita not in (
    select td.id_tipo_dato_Tecnico_attivita from tdta_dtaflat td
    where td.id_tipo_attivita = ta.id_tipo_attivita
  )
*/

show errors

commit;

quit
