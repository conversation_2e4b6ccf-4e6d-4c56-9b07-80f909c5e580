
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'PREEMAINTENANCE_NOTIFY_OPEN',
    'Comunicazioni apertura lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'PREEMAINTENANCE_NOTIFY_OPEN'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'PREEMAINTENANCE_NOTIFY_CLOSE_KO',
    'Comunicazioni chiusura negativa lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'PREEMAINTENANCE_NOTIFY_CLOSE_KO'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'PREEMAINTENANCE_NOTIFY_CLOSE_OK',
    'Comunicazioni chiusura positiva lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'PREEMAINTENANCE_NOTIFY_CLOSE_OK'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'CORRMAINTENANCE_NOTIFY_OPEN',
    'Comunicazioni apertura lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'CORRMAINTENANCE_NOTIFY_OPEN'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'CORRMAINTENANCE_NOTIFY_CLOSE_KO',
    'Comunicazioni chiusura negativa lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'CORRMAINTENANCE_NOTIFY_CLOSE_KO'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'LAVORI',
    'CORRMAINTENANCE_NOTIFY_CLOSE_OK',
    'Comunicazioni chiusura positiva lavoro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'LAVORI'
        and     EVENT = 'CORRMAINTENANCE_NOTIFY_CLOSE_OK'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'LAVORI',
    1
);

commit;

quit
