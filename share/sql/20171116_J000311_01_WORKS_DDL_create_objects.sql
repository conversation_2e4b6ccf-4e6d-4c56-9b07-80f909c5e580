set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace FUNCTION GET_LAVORO_VIRTUAL_STATUS
(
   obj_status IN VARCHAR2
  ,activity_status IN VARCHAR2
  ,maker in VARCHAR2
  ,team_id in VARCHAR2
  ,start_planned_date in TIMESTAMP WITH TIME ZONE
  ,start_work_date in TIMESTAMP WITH TIME ZONE
) RETURN VARCHAR2
IS
  virtual_status VARCHAR2(4000 char);
BEGIN
  -- dbms_output.put_line('start_planned_date: '||start_planned_date);
  select
    case
      when obj_status in ('Planned') and activity_status is null then 'notWorkable'
      when obj_status in ('Workable') and activity_status is null then 'workable'
      when
           (activity_status in ('APERTA','NUOVO','INOLTRATO_SIRTI'))
        or (activity_status = 'IN_LAVORAZIONE' and maker= 'Team' and (team_id is null or start_planned_date is null))
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is null)
      then 'notPlanned'
      when
           (activity_status = 'IN_LAVORAZIONE' and maker= 'Team' and team_id is not null and start_planned_date is not null and start_work_date is null)
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is not null and to_date(to_char(start_planned_date,'YYYYMMDDHH24MISS'),'YYYYMMDDHH24MISS') > sysdate)
      then 'planned'
      when
           (activity_status = 'IN_LAVORAZIONE' and ((maker= 'Team' and team_id is not null and start_planned_date is not null and start_work_date is not null) or (maker= 'Subcontract')))
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is not null and to_date(to_char(start_planned_date,'YYYYMMDDHH24MISS'),'YYYYMMDDHH24MISS') <= sysdate)
      then 'workInProgress'
      when activity_status = 'ATTESA_CARICAMENTO_AS_BUILT' then 'workEnded'
      when activity_status = 'ATTESA_GESTIONE_AS_BUILT' then 'asBuiltNotWorked'
      when activity_status in ('ESPLETATO','QUARANTENA') and obj_status = 'Workable' then 'asBuiltWorked'
      when activity_status in ('ESPLETATO','QUARANTENA') and obj_status = 'Done' then 'sinfoUpdated'
      when activity_status in ('ANOMALIA','ASSURANCE_ANNULLAMENTO') then 'assurance'
      when activity_status in ('ANNULLATO','ATTESA_ANNULLAMENTO') then 'cancelled'
      else null
    end into virtual_status
  from dual;

  RETURN virtual_status;

END GET_LAVORO_VIRTUAL_STATUS;
/

create or replace view v_cavi_lavori as
  select
   dt1.valore "customerId"
  ,dt2.valore "contractId"
  ,to_number(dt3.valore) "projectId"
  ,to_number(dt4.valore) "cableId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,pt."maker" "maker"
  ,pt."teamId" "teamId"
  ,pt."startPlannedDate" "startPlannedDate"
  ,pt."startWorkDate" "startWorkDate"
  ,pt."endPlannedDate"
  ,pt."estimatedDuration"
  ,pt."endWorkDate"
  ,pt."spentTime"
  ,pt."subContractCode"
  ,case
    when pt."maker" = 'Team' then (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SIRTI'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
    else (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SUBAPPALTO'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
   end "techninalAssistantsAssignee"
   ,case
      when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
      when a.stato_corrente in ('ATTESA_CARICAMENTO_AS_BUILT','ATTESA_GESTIONE_AS_BUILT') then 'FASE_AS_BUILT'
      else a.stato_corrente
    end "workStatus"
   ,case
      when a.stato_corrente = 'ATTESA_CARICAMENTO_AS_BUILT' then 'ATTESA_CARICAMENTO'
      when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
      when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
      else null
    end "asBuiltStatus"
from
  v_sistemi s
    join v_dt_sistemi dt1 on dt1.id_sistema = s.id and dt1.nome = 'customerId'
    join v_dt_sistemi dt2 on dt2.id_sistema = s.id and dt2.nome = 'contractId'
    join v_dt_sistemi dt3 on dt3.id_sistema = s.id and dt3.nome = 'projectId'
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'cableId'
    join v_attivita a on a.id_sistema = s.id and a.nome_tipo_attivita = 'LAVORO' and a.id = (select max(a2.id) from v_attivita a2 where a2.id_sistema = s.id)
    join pt_lavoro pt on pt.id_attivita = a.id
where 1=1
  and s.tipo = 'CAVO'
  and s.data_dismissione is null
  and s.data_sospensione is null
;

create or replace view v_cavi as
select
   s."customerId"
  ,s."contractId"
  ,s."projectId"
  ,c."cableId"
  ,c."cableName"
  ,c."cableType"
  ,c."potential"
  ,c."unitOfMeasure"
  ,c."fromNetworkElementId"
  ,c."fromNetworkElementName"
  ,c."fromNetworkElementType"
  ,c."fromNetworkElementGeoLocation"
  ,c."toNetworkElementId"
  ,c."toNetworkElementName"
  ,c."toNetworkElementType"
  ,c."toNetworkElementGeoLocation"
  ,c."plannedUndergroundLength"
  ,c."plannedAerialLength"
  ,c."plannedFacadeLength"
  ,c."status"
  ,c."doneUndergroundLength"
  ,c."doneAerialLength"
  ,c."doneFacadeLength"
  ,c."doneHandmaidLength"
  ,c."estimatedDuration"
from CAVI c
  join sync_runs s on s."runId" = c."runId" and c."historicizingRunId" is null
;

create or replace view v_cavi_lavori_dettaglio as
select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,cables."cableId"
  ,cables."cableName"
  ,cables."status"
  ,cables."fromNetworkElementName"
  ,cables."fromNetworkElementType"
  ,cables."toNetworkElementName"
  ,cables."toNetworkElementType"
  ,cables."cableType"
  ,cables."potential"
  ,cables."unitOfMeasure"
  ,cables."fromNetworkElementId"
  ,cables."fromNetworkElementGeoLocation"
  ,cables."toNetworkElementId"
  ,cables."toNetworkElementGeoLocation"
  ,cables."plannedUndergroundLength"
  ,cables."plannedAerialLength"
  ,cables."plannedFacadeLength"
  ,cables."doneUndergroundLength"
  ,cables."doneAerialLength"
  ,cables."doneFacadeLength"
  ,cables."doneHandmaidLength"
  ,cables."estimatedDuration"
  ,lav."id" "activityId"
  ,lav."status" "activityStatus"
  ,lav."maker" "activityMaker"
  ,lav."teamId" "activityTeamId"
  ,lav."startPlannedDate" "activityStartPlannedDate"
  ,lav."startWorkDate" "activityStartWorkDate"
  ,lav."endPlannedDate" "activityEndPlannedDate" 
  ,lav."estimatedDuration" "activityEstimatedDuration"
  ,lav."endWorkDate" "activityEndWorkDate"
  ,lav."spentTime" "activitySpentTime"
  ,lav."subContractCode" "activitySubContractCode"
  ,lav."techninalAssistantsAssignee" "activityTechAssAssignee"
  ,lav."asBuiltStatus" "activityAsBuiltStatus"
  ,GET_LAVORO_VIRTUAL_STATUS(
     obj_status => cables."status"
    ,activity_status => lav."status"
    ,maker => lav."maker"
    ,team_id => lav."teamId"
    ,start_planned_date => lav."startPlannedDate"
    ,start_work_date => lav."startWorkDate"
  ) "virtualStatus"
from
  v_cavi cables
    join v_progetti pfp on cables."customerId" = pfp."customerId" and cables."contractId" = pfp."contractId" and cables."projectId" = pfp."projectId"
    left join v_cavi_lavori lav on lav."cableId" = cables."cableId" and lav."customerId" = pfp."customerId" and lav."contractId" = pfp."contractId" and lav."projectId" =  pfp."projectId"
;

create or replace view v_cavi_lavori_riepilogo as
select 
   "projectId"
  ,"pfpId"
  ,"pfpName"
  ,"fromNetworkElementId"
  ,"fromNetworkElementName"
  ,"fromNetworkElementType"
  ,"fromNetworkElementGeoLocation"
  ,count(1) "total"
  ,decode(sum(case when "virtualStatus" = 'notWorkable' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'notWorkable' then 1 else 0 end)) "notWorkable"
  ,decode(sum(case when "virtualStatus" = 'workable' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workable' then 1 else 0 end)) "workable"
  ,decode(sum(case when "virtualStatus" = 'notPlanned' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'notPlanned' then 1 else 0 end)) "notPlanned"
  ,decode(sum(case when "virtualStatus" = 'planned' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'planned' then 1 else 0 end)) "planned"
  ,decode(sum(case when "virtualStatus" = 'workInProgress' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workInProgress' then 1 else 0 end)) "workInProgress"
  ,decode(sum(case when "virtualStatus" = 'workEnded' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workEnded' then 1 else 0 end)) "workEnded"
  ,decode(sum(case when "virtualStatus" = 'asBuiltNotWorked' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'asBuiltNotWorked' then 1 else 0 end)) "asBuiltNotWorked"
  ,decode(sum(case when "virtualStatus" = 'asBuiltWorked' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'asBuiltWorked' then 1 else 0 end)) "asBuiltWorked"
  ,decode(sum(case when "virtualStatus" = 'sinfoUpdated' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'sinfoUpdated' then 1 else 0 end)) "sinfoUpdated"
  ,decode(sum(case when "virtualStatus" = 'assurance' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'assurance' then 1 else 0 end)) "assurance"
  ,decode(sum(case when "virtualStatus" = 'cancelled' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'cancelled' then 1 else 0 end)) "cancelled"
from
  v_cavi_lavori_dettaglio
group by
   "projectId"
  ,"pfpId"
  ,"pfpName"
  ,"fromNetworkElementId"
  ,"fromNetworkElementName"
  ,"fromNetworkElementType"
  ,"fromNetworkElementGeoLocation"
;

quit
