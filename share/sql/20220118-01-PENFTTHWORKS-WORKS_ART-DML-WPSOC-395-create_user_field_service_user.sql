set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

insert into operatori values(
    seq_operatori.nextval,
    'Field',
    'f7b2993185f755d2212840328001bb2f',
    (Select id_gruppo from gruppi where nome = 'REMOTE_AUTOMATION'),
    'FIELD_SERVICE_USER',
    null,
    null,
    'S',
    null,
    'Service',
    null,
    null,
    '-1',
    null,
    null,
    null,
    100,
    null,
    null,
    'Y',
    null
);

insert into operatori_Gruppi values(
    (Select id_operatore from operatori where login_operatore = 'FIELD_SERVICE_USER'),
    (Select id_Gruppo from gruppi where nome = 'REMOTE_AUTOMATION')
);

show errors

commit;

quit
