set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE FORCE VIEW V_CAVI_LAVORI AS
with p as (
select dt1.valore "cableId"
  , dt2.valore "customerId"
  , dt3.valore "contractId"
  , max(a.id) id
from
  v_sistemi s
    join v_attivita a on a.id_sistema = s.id
    join v_dt_sistemi dt1 on dt1.id_sistema = s.id and dt1.nome = 'cableId'
    join v_dt_sistemi dt2 on dt2.id_sistema = s.id and dt2.nome = 'customerId'
    join v_dt_sistemi dt3 on dt3.id_sistema = s.id and dt3.nome = 'contractId'
where 1=1
  and s.tipo = 'CAVO'
  and a.nome_tipo_attivita = 'LAVORO'
group by dt1.valore
  , dt2.valore
  , dt3.valore
)
select p."customerId"
  ,p."contractId"
  ,pt."projectId"
  ,to_number(p."cableId") "cableId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,pt."maker" "maker"
  ,pt."teamId" "teamId"
  ,pt."startPlannedDate" "startPlannedDate"
  ,pt."startWorkDate" "startWorkDate"
  ,pt."endPlannedDate"
  ,pt."estimatedDuration"
  ,pt."endWorkDate"
  ,pt."spentTime"
  ,pt."subContractCode"
  ,(select dt5.valore from v_dt_sistemi dt5 where dt5.id_sistema = s.id and dt5.nome = 'recoveryCable') "recoveryCable"
  ,(select dt6.valore from v_dt_sistemi dt6 where dt6.id_sistema = s.id and dt6.nome = 'infrastructureCheck') "infrastructureCheck"
  ,(select dt7.valore from v_dt_sistemi dt7 where dt7.id_sistema = s.id and dt7.nome = 'plannedBreakdown') "plannedBreakdown"
  ,(select dt8.valore from v_dt_sistemi dt8 where dt8.id_sistema = s.id and dt8.nome = 'interruptedMinitubes') "interruptedMinitubes"
  ,(select dt9.valore from v_dt_sistemi dt9 where dt9.id_sistema = s.id and dt9.nome = 'branchesCut') "branchesCut"
  ,(select dt10.valore from v_dt_sistemi dt10 where dt10.id_sistema = s.id and dt10.nome = 'pilingCheck') "pilingCheck"
  ,(select dt11.valore from v_dt_sistemi dt11 where dt11.id_sistema = s.id and dt11.nome = 'teamNote') "teamNote"
  ,(select dt12.valore from v_dt_sistemi dt12 where dt12.id_sistema = s.id and dt12.nome = 'assistantNote') "assistantNote"
  ,case
    when pt."maker" = 'Team' then (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SIRTI'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
    else (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SUBAPPALTO'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
   end "techninalAssistantsAssignee"
   ,case
      when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
      else a.stato_corrente
    end "workStatus"
   ,case
      when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
      when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
      else null
    end "asBuiltStatus"
From p
  join pt_lavoro pt on pt.id_attivita = p.id
  join v_attivita a on a.id = p.id
  join v_sistemi s on s.id = a.id_sistema
;

quit

