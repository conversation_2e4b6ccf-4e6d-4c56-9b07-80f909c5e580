set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

accept user default 'works.' prompt 'dammi nome utente a cui concedere i permessi  (default works.) '

create or replace synonym CONF_NODI_TMP_STD_MAX for &user.CONF_NODI_TMP_STD_MAX;
create or replace synonym CONF_NODI_TMP_STD_GIUNZ for &user.CONF_NODI_TMP_STD_GIUNZ;
create or replace synonym CONF_CAVI_TMP_STD_MAX for &user.CONF_CAVI_TMP_STD_MAX;

quit
