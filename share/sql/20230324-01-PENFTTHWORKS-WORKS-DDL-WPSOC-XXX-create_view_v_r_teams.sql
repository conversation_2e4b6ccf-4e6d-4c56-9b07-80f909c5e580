set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE VIEW V_R_TEAMS AS 
  select b.userid USERNAME, b.opid opid, b.email, c."SOC",c."C<PERSON>",c."CIDSA<PERSON>",c."DATA_I_VALID",c."DATA_F_VALID",c."COGNOME",c."NOME",c."GENERE",c."DATA_NASCITA",c."COMUNE_NASCITA",c."CODICE_CATASTALE",c."PV_NASCITA",c."CODICE_FISCALE",c."TITOLO_STUDIO",c."DESCR_TITOLO_STUDIO",c."RAGG_TITOLO_STUDIO",c."QUALIFICA",c."DESCR_QUALIFICA",c."LIVELLO",c."DESCR_LIVELLO",c."RU<PERSON><PERSON>_PROF",c."DESCR_RUOLO_PROF",c."CCOSTO",c."DATA_ASSUNZIONE",c."COMUNE_ASSUNZIONE",c."DESCR_COMUNE_ASSUNZIONE",c."DATA_CESSAZIONE",c."UNITA_ORGANIZ",c."SIGLA_UNITA_ORGANIZ",c."CLASSE_UNITA_ORGANIZ",c."DESCR_CLASSE_UNITA_ORGANIZ",c."RAGG_CLASSE_UNITA_ORGANIZ",c."FLAG_ASS_TEC",c."FLAG_SQUADRA",c."TIPO_RILEVAZ_PRESTAZ",c."SOC_RESP_UNITA_ORGANIZ",c."CID_RESP_UNITA_ORGANIZ",c."CIDSAP_RESP_UNITA_ORGANIZ",c."SOC_DATORE_LAVORO",c."CID_DATORE_LAVORO",c."CIDSAP_DATORE_LAVORO",c."DATA_ORA_VAR",c."PARTNER",c."ID_TALENTHIA",c."REGIME_TRASFERTA",c."COMUNE_RESIDENZA",c."CAP_RESIDENZA",c."INDIRIZZO_RESIDENZA",c."PV_RESIDENZA",c."DESCR_COMUNE_RESIDENZA",c."FRAZIONE_RESIDENZA",c."CIVICO_RESIDENZA",c."FLAG_GALLERIA",c."FLAG_TORRE",c."FLAG_FORFETARIO",c."FLAG_COLLABORATORE",c."CCOSTO_LOGISTICO",c."PROFILO_SAP",c."CONTRATTO",c."DESCR_CONTRATTO",c."FLAG_OPERATIVO",c."DATA_MITRIC",c."DECOR_GRUPPO_OPERATIVO",c."SOCRESP_GRUPPO_OPERATIVO",c."CIDRESP_GRUPPO_OPERATIVO",c."GRUPPO_OPERATIVO",c."CODICE_SOGGETTO",c."TIPO_CONTRATTO",c."DESCR_TIPO_CONTRATTO",c."TIPO_PARTTIME",c."DESCR_TIPO_PARTTIME",c."CODICE_ORGANICO",c."DESCR_CODICE_ORGANICO"
from anag.persone@service_anag C
    join anag.UTENTI@service_anag b on  B.SOC=C.SOC AND B.CID=C.CID
WHERE c.DATA_I_VALID <= SYSDATE
    AND c.DATA_F_VALID > SYSDATE
    AND c.FLAG_SQUADRA <> ' '
    and c.PARTNER IS NULL
    AND C.DATA_F_VALID>SYSDATE
    AND (C.DATA_CESSAZIONE is null or C.DATA_CESSAZIONE > sysdate)
;

GRANT SELECT ON V_R_TEAMS TO WORKS_ART;

accept obj_teams default 'V_R_TEAMS' prompt 'dammi nome oggetto per i circuiti (default V_R_TEAMS) '

create or replace view V_TEAMS as
select *
from &obj_teams;

grant select on v_teams to works_art;

--drop materialized view mv_anag_persone;
create materialized view mv_anag_persone refresh complete as
select *
from v_teams;

alter materialized view mv_anag_persone refresh start with sysdate+1/48 next sysdate+1/24;

grant select on mv_anag_persone to works_art;

show errors

quit
