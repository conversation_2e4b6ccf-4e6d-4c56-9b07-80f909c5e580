set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop table CAVI_TSL;

CREATE TABLE CAVI_TSL 
(
    "cableType" VARCHAR2(4000) NOT NULL 
  , "aerialMtsByManHour" FLOAT NOT NULL 
  , "facadeMtsByManHour" FLOAT NOT NULL 
  , "undergroundMtsByManHour" FLOAT NOT NULL
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CAVI_TSL ADD CONSTRAINT CAVI_TSL_UK1 UNIQUE ("cableType","disableDate") ENABLE;

COMMENT ON TABLE CAVI_TSL IS 'Tempi standard lavori stimati per posa cavi';

COMMENT ON COLUMN CAVI_TSL."cableType" IS 'Tipo di cavo';
COMMENT ON COLUMN CAVI_TSL."aerialMtsByManHour" IS 'Metri posa cavi aerei per ora uomo';
COMMENT ON COLUMN CAVI_TSL."facadeMtsByManHour" IS 'Metri posa cavi in facciata per ora uomo';
COMMENT ON COLUMN CAVI_TSL."undergroundMtsByManHour" IS 'Metri posa cavi sotterranei per ora uomo';
COMMENT ON COLUMN CAVI_TSL."insertDate" IS 'Data inserimento dell''informazione';
COMMENT ON COLUMN CAVI_TSL."disableDate" IS 'Data storicizzazione dell''informazione, se null indica che è il valore attivo';

quit
