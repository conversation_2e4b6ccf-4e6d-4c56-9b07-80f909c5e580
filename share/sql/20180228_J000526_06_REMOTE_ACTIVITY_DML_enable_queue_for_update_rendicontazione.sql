
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'LAVORO',
    'Gestione Lavori',
    1
  );

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'RENDICONTAZIONE',
    'Gestione Rendicontazione',
    1
  );


insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    '___LOCALHOST___',
    'LAVORO',
    'UPDATE_ACCOUNTING',
    'Gestione aggiornamento rendicontazione dal attività di LAVORO',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = '___LOCALHOST___'
	and 	SOURCE_CONTEXT = 'LAVORO'
        and     EVENT = 'UPDATE_ACCOUNTING'
        and     ENABLED = 1
    ),
    '___LOCALHOST___',
    'RENDICONTAZIONE',
    1
);

commit;

quit
