set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_macrotasks_lavori as
select 
			  "customerId"
			  ,"contractId"
			  ,"projectId"
			  ,"runDate"
			  ,"sinfoLastModified"
			  ,"permitsAreaId"
			  ,"type"
			  ,"category"
			  ,"subCategory"
			  ,"unitOfMeasure"
			  ,"totQuantity"
			  ,"toDoQuantity" - "inProgressQuantity" "toDoQuantity"
			  ,"inProgressQuantity"
			  ,"doneQuantity"
			  ,"accountableQuantity"
			  ,"inProgConstructionSiteCount"
			  ,"doneConstructionSiteCount"
			  ,"cancelConstructionSiteCount"
			  ,"estimatedDuration"
		from (
			select
				  mtr."customerId"
				  ,mtr."contractId"
				  ,mtr."projectId"
				  ,mtr."runDate"
				  ,mtr."sinfoLastModified"
				  ,mt."permitsAreaId"
				  ,mt."type"
				  ,mt."category"
				  ,mt."subCategory"
				  ,mt."unitOfMeasure"
				  ,mt."doneQuantity" + mt."toDoQuantity" "totQuantity"
				  ,mt."toDoQuantity"
				  , nvl((
				    select sum(to_number_if_valid(replace(dts4.valore,'.',',')))
				    From v_sistemi s
				      join v_dt_sistemi dts on dts.id_sistema = s.id
				      join v_dt_sistemi dts2 on dts2.id_sistema = s.id
				      join v_dt_sistemi dts3 on dts3.id_sistema = s.id
				      join v_dt_sistemi dts3bis on dts3bis.id_sistema = s.id
				      join v_dt_sistemi dts4 on dts4.id_sistema = s.id
				      join sistemi_relazione sr on sr.b = s.id and sr.tipo = '0'
				      join v_attivita vatt on vatt.id_sistema = sr.a
				    where s.tipo = 'MACROLAVORO'
				    and dts.nome = 'projectId'
				    and dts2.nome = 'category'
				    and dts3.nome = 'subCategory'
				    and dts3bis.nome = 'permitsAreaId'
				    and dts4.nome = 'toAssignQuantity'
				    and vatt.nome_tipo_attivita = 'LAVORO'
				    and vatt.stato_corrente not in ('ANNULLATO','ESPLETATO','ESPLETATO_SENZA_AS_BUILT')
				    and dts.valore = mtr."projectId"
				    and dts2.valore = mt."category"
				    and dts3.valore = mt."subCategory"
				    and dts3bis.valore = mt."permitsAreaId"
				  ),0) "inProgressQuantity"
				  ,mt."doneQuantity" - mt."accountableQuantity" "doneQuantity"
				  ,mt."accountableQuantity"
				  ,nvl((
				    select count(vatt.id)
				    From v_sistemi s
				      join v_dt_sistemi dts on dts.id_sistema = s.id
				      join v_dt_sistemi dts2 on dts2.id_sistema = s.id
				      join v_dt_sistemi dts3 on dts3.id_sistema = s.id
				      join v_dt_sistemi dts3bis on dts3bis.id_sistema = s.id
				      join sistemi_relazione sr on sr.b = s.id and sr.tipo = '0'
				      join v_attivita vatt on vatt.id_sistema = sr.a
				    where s.tipo = 'MACROLAVORO'
				    and dts.nome = 'projectId'
				    and dts2.nome = 'category'
				    and dts3.nome = 'subCategory'
				    and dts3bis.nome = 'permitsAreaId'
				    and vatt.nome_tipo_attivita = 'LAVORO'
				    and vatt.stato_corrente not in ('ANNULLATO','ESPLETATO','ESPLETATO_SENZA_AS_BUILT')
				    and dts.valore = mtr."projectId"
				    and dts2.valore = mt."category"
				    and dts3.valore = mt."subCategory"
				    and dts3bis.valore = mt."permitsAreaId"
				  ),0) "inProgConstructionSiteCount"
				  ,nvl((
				    select count(vatt.id)
				    From v_sistemi s
				      join v_dt_sistemi dts on dts.id_sistema = s.id
				      join v_dt_sistemi dts2 on dts2.id_sistema = s.id
				      join v_dt_sistemi dts3 on dts3.id_sistema = s.id
				      join v_dt_sistemi dts3bis on dts3bis.id_sistema = s.id
				      join sistemi_relazione sr on sr.b = s.id and sr.tipo = '0'
				      join v_attivita vatt on vatt.id_sistema = sr.a
				    where s.tipo = 'MACROLAVORO'
				    and dts.nome = 'projectId'
				    and dts2.nome = 'category'
				    and dts3.nome = 'subCategory'
				    and dts3bis.nome = 'permitsAreaId'
				    and vatt.nome_tipo_attivita = 'LAVORO'
				    and vatt.stato_corrente in ('ESPLETATO','ESPLETATO_SENZA_AS_BUILT')
				    and dts.valore = mtr."projectId"
				    and dts2.valore = mt."category"
				    and dts3.valore = mt."subCategory"
				    and dts3bis.valore = mt."permitsAreaId"
				  ),0) "doneConstructionSiteCount"
				  ,nvl((
				    select count(vatt.id)
				    From v_sistemi s
				      join v_dt_sistemi dts on dts.id_sistema = s.id
				      join v_dt_sistemi dts2 on dts2.id_sistema = s.id
				      join v_dt_sistemi dts3 on dts3.id_sistema = s.id
				      join v_dt_sistemi dts3bis on dts3bis.id_sistema = s.id
				      join sistemi_relazione sr on sr.b = s.id and sr.tipo = '0'
				      join v_attivita vatt on vatt.id_sistema = sr.a
				    where s.tipo = 'MACROLAVORO'
				    and dts.nome = 'projectId'
				    and dts2.nome = 'category'
				    and dts3.nome = 'subCategory'
				    and dts3bis.nome = 'permitsAreaId'
				    and vatt.nome_tipo_attivita = 'LAVORO'
				    and vatt.stato_corrente in ('ANNULLATO')
				    and dts.valore = mtr."projectId"
				    and dts2.valore = mt."category"
				    and dts3.valore = mt."subCategory"
				    and dts3bis.valore = mt."permitsAreaId"
				  ),0) "cancelConstructionSiteCount"
				  ,mt."estimatedDuration"
			from macrotasks mt
			  join SYNC_RUNS mtr on mtr."runId" = mt."runId" and mtr."syncType" = 'MACROTASKS'
			where 1=1
			  and mt."historicizingRunId" is null
		);

grant select on v_macrotasks_lavori to works_art;

quit
