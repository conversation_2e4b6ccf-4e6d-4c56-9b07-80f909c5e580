set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

rename SEQ_MACROTASKS_RUN to SEQ_SYNC_RUNS;

CREATE TABLE TMP_SYNC_RUNS
(
    "runId" NUMBER not null
  , "customerId" varchar(20) not null
  , "contractId" varchar(20) not null
  , "projectId" NUMBER not null
  , "runDate" DATE not null
  , "sinfoLastModified" DATE not null
  , "syncType" varchar2(256)
);

CREATE TABLE TMP_MACROTASKS
(
    "permitsAreaId" NUMBER
  , "workZoneid" NUMBER
  , "buildingId" NUMBER
  , "type" VARCHAR(20) not null
  , "category" VARCHAR(256) not null
  , "subCategory" VARCHAR(256)
  , "unitOfMeasure" VARCHAR(20) not null
  , "toDoQuantity" FLOAT not null
  , "doneQuantity" FLOAT not null
  , "duration" FLOAT not null
  , "runId" NUMBER not null
  , "historicizingRunId" NUMBER
);

quit
