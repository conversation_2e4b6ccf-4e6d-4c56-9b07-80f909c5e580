set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

create or replace view v_tim_works_assetId as
select pt.id_attivita id, dts.valore "assetId"
from works_art.pt_lavoro pt
    join works_art.v_Attivita vatt on vatt.id = pt.id_attivita
    join works_art.v_dt_sistemi dts on dts.id_sistema = vatt.id_sistema and dts.nome = 'assetId'
where pt."customerId" = 'TIM'
order by 1 desc,2;

grant select on v_tim_works_assetId to client_core_rpt;

show errors

quit
