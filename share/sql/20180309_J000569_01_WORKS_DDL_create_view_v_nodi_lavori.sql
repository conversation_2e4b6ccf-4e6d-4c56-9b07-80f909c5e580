set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE FORCE VIEW V_NODI_LAVORI AS 
  select
   pt."customerId"
  ,pt."contractId"
  ,pt."projectId"
  ,dt4.valore "networkElementId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,pt."maker" "maker"
  ,pt."teamId" "teamId"
  ,pt."startPlannedDate" "startPlannedDate"
  ,case when pt."maker" = 'Team' then pt."startWorkDate" else pt."subContractStartWorkDate" end "startWorkDate"
  ,pt."endPlannedDate"
  ,pt."estimatedDuration"
  ,case when pt."maker" = 'Team' then pt."endWorkDate" else pt."subContractEndWorkDate" end "endWorkDate"
  ,pt."spentTime"
  ,pt."subContractCode"
  ,case
    when pt."maker" = 'Team' then (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SIRTI'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
    else (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SUBAPPALTO'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
   end "techninalAssistantsAssignee"
  ,case
    when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
    else a.stato_corrente
  end "workStatus"
  ,case
    when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
    when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
    else null
  end "asBuiltStatus"
from
  v_sistemi s
    join v_attivita a on a.id_sistema = s.id
    join pt_lavoro pt on pt.id_attivita = a.id
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'networkElementId'
where 1=1
  and s.tipo = 'NODO'
  and a.nome_tipo_attivita = 'LAVORO'
  and  a.id = (
    select max(vc.id)
    from v_attivita vc
      join v_sistemi vs on vs.id = vc.id_sistema
      join v_dt_sistemi vdts on vdts.id_sistema = vs.id
      join v_dt_sistemi vdts2 on vdts2.id_sistema = vs.id
      join v_dt_sistemi vdts3 on vdts3.id_sistema = vs.id
    where vs.tipo = s.tipo
    and vdts.nome = dt4.nome
    and vdts2.nome = 'customerId'
    and vdts3.nome = 'contractId'
    and vdts.valore = dt4.valore
    and vdts2.valore = pt."customerId"
    and vdts3.valore = pt."contractId"
  )
;


quit

