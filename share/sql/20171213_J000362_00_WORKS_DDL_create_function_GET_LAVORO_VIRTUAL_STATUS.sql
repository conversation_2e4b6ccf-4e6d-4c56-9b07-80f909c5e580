set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace FUNCTION GET_LAVORO_VIRTUAL_STATUS
(
   obj_status IN VARCHAR2
  ,activity_status IN VARCHAR2
  ,maker in VARCHAR2
  ,team_id in VARCHAR2
  ,start_planned_date in TIMESTAMP WITH TIME ZONE
  ,start_work_date in TIMESTAMP WITH TIME ZONE
) RETURN VARCHAR2
IS
  virtual_status VARCHAR2(4000 char);
BEGIN
  select
    case
      when obj_status in ('Planned') and activity_status is null then 'notWorkable'
      when obj_status in ('Workable') and activity_status is null then 'workable'
      when obj_status in ('Done') then 'sinfoUpdated' -- NB: se l'attivita non esiste sarebbe un warning...
      when
           (activity_status in ('APERTA','NUOVO','INOLTRATO_SIRTI'))
        or (activity_status = 'IN_LAVORAZIONE' and maker= 'Team' and (team_id is null or start_planned_date is null))
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is null)
      then 'notPlanned'
      when
           (activity_status = 'IN_LAVORAZIONE' and maker= 'Team' and team_id is not null and start_planned_date is not null and start_work_date is null)
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is not null and to_date(to_char(start_planned_date,'YYYYMMDDHH24MISS'),'YYYYMMDDHH24MISS') > sysdate)
      then 'planned'
      when
           (activity_status = 'IN_LAVORAZIONE' and ((maker= 'Team' and team_id is not null and start_planned_date is not null and start_work_date is not null) or (maker= 'Subcontract')))
        or (activity_status = 'INOLTRATO_SUBAPPALTO' and start_planned_date is not null and to_date(to_char(start_planned_date,'YYYYMMDDHH24MISS'),'YYYYMMDDHH24MISS') <= sysdate)
      then 'workInProgress'
      when activity_status = 'ATTESA_CARICAMENTO_AS_BUILT' then 'workEnded'
      when activity_status = 'ATTESA_GESTIONE_AS_BUILT' then 'asBuiltNotWorked'
      when activity_status in ('ESPLETATO','QUARANTENA') and obj_status = 'Workable' then 'asBuiltWorked'
      when activity_status in ('ANOMALIA','ASSURANCE_ANNULLAMENTO') then 'assurance'
      when activity_status in ('ANNULLATO','ATTESA_ANNULLAMENTO') then 'cancelled'
      else null
    end into virtual_status
  from dual;

  RETURN virtual_status;

END GET_LAVORO_VIRTUAL_STATUS;
/

quit
