set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

ALTER TABLE NODI_RIEPILOGO
ADD CONSTRAINT NODI_RIEPILOGO_FK1 FOREIGN KEY
(
  "runId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

ALTER TABLE NODI_RIEPILOGO
ADD CONSTRAINT NODI_RIEPILOGO_FK2 FOREIGN KEY
(
  "historicizingRunId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

CREATE INDEX NODI_RIEPILOGO_IDX02 ON NODI_RIEPILOGO ("runId" DESC);
CREATE INDEX NODI_RIEPILOGO_IDX03 ON NODI_RIEPILOGO ("historicizingRunId");
CREATE INDEX NODI_RIEPILOGO_IDX04 ON NODI_RIEPILOGO ("networkElementId");

CREATE INDEX NODI_IDX03 ON NODI ("networkElementId");

quit
