set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 192 f.o. dielettrico per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo anticaccia 192fo dielettrico autop.(ADSS) tubetti da 12','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 4 fo','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 12fo dielettrico per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo light 12fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo 24fo dielettrico per posa in tubazione','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo light 24fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 24fo dielettrico per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo anticaccia 24fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo 48fo dielettrico per posa in tubazione','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo light 48fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo anticaccia 48fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 48fo dielettrico per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo 72fo dielettrico per posa in tubazione','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo anticaccia 72fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 96fo dielettrico per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo light 96fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo 96fo dielettrico per posa in tubazione','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo light 144fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo aereo anticaccia 144fo dielettrico autop.(ADSS)','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Cavo 144fo dielettrico per posa in tubazione','9,01','10,57','7,14',sysdate,null);
Insert into CAVI_TSL ("cableType","aerialMtsByManHour","facadeMtsByManHour","undergroundMtsByManHour","insertDate","disableDate") values ('Minicavo 144fo dielettrico (tubetto 12) per posa in tubazione ed in facciata','9,01','10,57','7,14',sysdate,null);

commit;

quit
