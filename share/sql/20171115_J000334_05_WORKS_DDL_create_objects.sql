set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_<PERSON><PERSON><PERSON> as
select
   dt1.valore "customerId"
  ,dt2.valore "contractId"
  ,dt3.valore "pop"
  ,dt4.valore "ring"
  ,dt5.valore "projectId"
  ,dt6.valore "pfpId"
  ,dt7.valore "pfpName"
from
  v_sistemi s
    join v_dt_sistemi dt1 on dt1.id_sistema = s.id and dt1.nome = 'customerId'
    join v_dt_sistemi dt2 on dt2.id_sistema = s.id and dt2.nome = 'contractId'
    join v_dt_sistemi dt3 on dt3.id_sistema = s.id and dt3.nome = 'pop'
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'ring'
    join v_dt_sistemi dt5 on dt5.id_sistema = s.id and dt5.nome = 'projectId'
    join v_dt_sistemi dt6 on dt6.id_sistema = s.id and dt6.nome = 'pfpId'
    join v_dt_sistemi dt7 on dt7.id_sistema = s.id and dt7.nome = 'pfpName'
where 1=1
  and s.tipo = 'PROJECT'
  and s.data_dismissione is null
  and s.data_sospensione is null
;

create or replace view v_nodi_lavori as
select 
   dt1.valore "customerId"
  ,dt2.valore "contractId"
  ,dt3.valore "projectId"
  ,dt4.valore "networkElementId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,case
    when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
    when a.stato_corrente in ('ATTESA_CARICAMENTO_AS_BUILT','ATTESA_GESTIONE_AS_BUILT') then 'FASE_AS_BUILT'
    else a.stato_corrente
  end "workStatus"
  ,case
    when a.stato_corrente = 'ATTESA_CARICAMENTO_AS_BUILT' then 'ATTESA_CARICAMENTO'
    when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
    when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
    else null
  end "asBuiltStatus"
from
  v_sistemi s
    join v_dt_sistemi dt1 on dt1.id_sistema = s.id and dt1.nome = 'customerId'
    join v_dt_sistemi dt2 on dt2.id_sistema = s.id and dt2.nome = 'contractId'
    join v_dt_sistemi dt3 on dt3.id_sistema = s.id and dt3.nome = 'projectId'
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'networkElementId'
    join v_attivita a on a.id_sistema = s.id and a.nome_tipo_attivita = 'LAVORO' and a.id = (select max(a2.id) from v_attivita a2 where a2.id_sistema = s.id)
    -- join pt_lavoro pt on pt.id_attivita = a.id
where 1=1
  and s.tipo = 'NODO'
  and s.data_dismissione is null
  and s.data_sospensione is null
;

create or replace view v_nodi as
select
   s."customerId"
  ,s."contractId"
  ,to_char(s."projectId") "projectId"
  ,to_char(n."networkElementId") "networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."cablingType"
  ,n."networkElementIsDone"
  ,n."infrastructureExists"
  ,n."infrastructureIsDone"
  ,n."inputCableId"
  ,n."inputCableName"
  ,n."inputCablePotential"
  ,n."inputCablePotentialPlanned"
  ,n."outputCableId"
  ,n."outputCableName"
  ,n."outputCablePotential"
  ,n."outputCablePotentialPlanned"
  ,n."status"
  ,n."splicedFibers"
  ,n."terminatedFibers"
  ,n."estimatedDuration"
from NODI n
  join sync_runs s on s."runId" = n."runId" and n."historicizingRunId" is null
;

create or replace view v_nodi_lavori_dettaglio as
select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,n."networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."cablingType"
  ,n."networkElementIsDone"
  ,n."infrastructureExists"
  ,n."infrastructureIsDone"
  ,n."inputCableId"
  ,n."inputCableName"
  ,n."inputCablePotential"
  ,n."inputCablePotentialPlanned"
  ,n."outputCableId"
  ,n."outputCableName"
  ,n."outputCablePotential"
  ,n."outputCablePotentialPlanned"
  ,n."status"
  ,n."splicedFibers"
  ,n."terminatedFibers"
  ,n."estimatedDuration"
from
  v_nodi n
    join v_progetti pfp on n."customerId" = pfp."customerId" and n."contractId" = pfp."contractId" and n."projectId" = pfp."projectId"
;

create or replace view v_nodi_lavori_riepilogo as
select 
   d."customerId"
  ,d."contractId"
  ,d."pop"
  ,d."ring"
  ,d."projectId"
  ,d."pfpId"
  ,d."pfpName"
  ,d."networkElementId"
  ,d."networkElementName"
  ,d."networkElementType"
  ,case
    when d."networkElementIsDone" = 1 then 'Done'
    else 'Not done'
  end "networkElementStatus"
  ,case
    when d."infrastructureExists" = 0 then 'Not exists'
    when d."infrastructureIsDone" = 1 then 'Done'
    else 'Not done'
  end "networkElementContainerStatus"
  ,count(1) "total"
  ,sum(case when d."status" = 'Planned' then 1 else 0 end) "notWorkable"
  ,sum(case when d."status" = 'Workable' then 1 else 0 end) "workable"
  ,sum(case when d."status" = 'Done' then 1 else 0 end) "done"
  ,lav."workStatus"
  ,lav."asBuiltStatus"
from
  v_nodi_lavori_dettaglio d
    left join v_nodi_lavori lav on lav."networkElementId" = d."networkElementId" and lav."customerId" = d."customerId" and lav."contractId" = d."contractId" and lav."projectId" =  d."projectId"
group by
   d."customerId"
  ,d."contractId"
  ,d."pop"
  ,d."ring"
  ,d."projectId"
  ,d."pfpId"
  ,d."pfpName"
  ,d."networkElementId"
  ,d."networkElementName"
  ,d."networkElementType"
  ,case
    when d."networkElementIsDone" = 1 then 'Done'
    else 'Not done'
  end -- "networkElementStatus"
  ,case
    when d."infrastructureExists" = 0 then 'Not exists'
    when d."infrastructureIsDone" = 1 then 'Done'
    else 'Not done'
  end -- "networkElementContainerStatus"
  ,lav."workStatus"
  ,lav."asBuiltStatus"
;


quit
