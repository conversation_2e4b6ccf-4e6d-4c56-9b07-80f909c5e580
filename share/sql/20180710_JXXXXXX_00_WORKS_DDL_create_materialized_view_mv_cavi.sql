s
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE MATERIALIZED VIEW MV_CAVI  
  AS SELECT
    sr."customerId",
    sr."contractId",
    sr."projectId",
    dt4.valore "pfpName",
    n."fromNetworkElementId",
    n."fromNetworkElementName",
    n."fromNetworkElementType",
    n."fromNetworkElementGeoLocation",
    CASE
            WHEN n."status" IN (
                'Planned'
            )
                 AND lav."status" IS NULL THEN 'notWorkable'
            WHEN n."status" IN (
                'Workable'
            )
                 AND lav."status" IS NULL THEN 'workable'
            WHEN n."status" IN (
                'Done'
            ) THEN 'sinfoUpdated' -- NB: se l'attivita non esiste sarebbe un warning...
            WHEN n."status" IN (
                'Accountable'
            ) THEN 'accountable' -- NB: se l'attivita non esiste sarebbe un warning...
            WHEN ( lav."status" IN (
                'APERTA',
                'NUOVO',
                'INOLTRATO_SIRTI'
            ) )
                 OR (
                lav."status" = 'IN_LAVORAZIONE_SIRTI'
                AND (
                    lav."teamId" IS NULL
                    OR lav."startPlannedDate" IS NULL
                )
            )
                 OR (
                lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO'
                AND lav."subContractCode" IS NULL
            ) -- NB: solo per garantire la fase di transizione
             THEN 'notPlanned'
            WHEN (
                lav."status" = 'IN_LAVORAZIONE_SIRTI'
                AND lav."teamId" IS NOT NULL
                AND lav."startPlannedDate" IS NOT NULL
                AND lav."startWorkDate" IS NULL
            ) THEN 'planned'
            WHEN (
                lav."status" = 'IN_LAVORAZIONE_SIRTI'
                AND (
                    (
                        lav."teamId" IS NOT NULL
                        AND lav."startPlannedDate" IS NOT NULL
                        AND lav."startWorkDate" IS NOT NULL
                    )
                )
            )
                 OR ( lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO' ) -- NB: volutamente viene ignorato il valore della lav."startPlannedDate"
                  THEN 'workInProgress'
            WHEN lav."status" = 'ATTESA_RENDICONTAZIONE'   THEN 'workEnded'
            WHEN lav."status" = 'ATTESA_GESTIONE_AS_BUILT' THEN 'asBuiltNotWorked'
            WHEN lav."status" IN (
                'ESPLETATO',
                'QUARANTENA'
            )
                 AND n."status" = 'Workable' THEN 'asBuiltWorked'
            WHEN lav."status" IN (
                'ANOMALIA',
                'ASSURANCE_ANNULLAMENTO'
            ) THEN 'assurance'
            WHEN lav."status" IN (
                'ANNULLATO',
                'ATTESA_ANNULLAMENTO'
            ) THEN 'cancelled'
            ELSE NULL
        END
    "virtualStatus"
FROM
    cavi n
    JOIN sync_runs sr ON sr."runId" = n."runId"
    JOIN v_dt_sistemi dt1 ON dt1.nome = 'projectId'
                             AND dt1.valore = TO_CHAR(sr."projectId")
    JOIN v_dt_sistemi dt2 ON dt2.id_sistema = dt1.id_sistema
                             AND dt2.nome = 'customerId'
                             AND sr."customerId" = dt2.valore
    JOIN v_dt_sistemi dt3 ON dt3.id_sistema = dt1.id_sistema
                             AND dt3.nome = 'contractId'
                             AND sr."contractId" = dt3.valore
    JOIN v_dt_sistemi dt4 ON dt4.id_sistema = dt1.id_sistema
                             AND dt4.nome = 'pfpName'
    JOIN v_sistemi s ON s.id = dt1.id_sistema
    LEFT JOIN v_cavi_lavori lav ON lav."cableId" = n."cableId"
                                   AND lav."customerId" = sr."customerId"
                                   AND lav."contractId" = sr."contractId"
                                   AND lav."projectId" = sr."projectId"
WHERE
    1 = 1
    AND sr."syncType" = 'CAVI'
    AND n."historicizingRunId" IS NULL
    AND s.tipo = 'PROJECT'
    AND s.data_dismissione IS NULL
    AND s.data_sospensione IS NULL;

COMMENT ON MATERIALIZED VIEW "WORKS"."MV_CAVI"  IS 'snapshot table for snapshot WORKS.MV_CAVI';

quit
