set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_nodi_lavori as
select
   pt."customerId"
  ,pt."contractId"
  ,pt."projectId"
  ,dt4.valore "networkElementId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,case
    when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
    when a.stato_corrente in ('ATTESA_CARICAMENTO_AS_BUILT','ATTESA_GESTIONE_AS_BUILT') then 'FASE_AS_BUILT'
    else a.stato_corrente
  end "workStatus"
  ,case
    when a.stato_corrente = 'ATTESA_CARICAMENTO_AS_BUILT' then 'ATTESA_CARICAMENTO'
    when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
    when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
    else null
  end "asBuiltStatus"
from
  v_sistemi s
    join v_attivita a on a.id_sistema = s.id
    join pt_lavoro pt on pt.id_attivita = a.id
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'networkElementId'
where 1=1
  and a.nome_tipo_attivita = 'LAVORO'
  and a.id = (select max(a2.id) from v_attivita a2 where a2.id_sistema = s.id)
  and s.tipo = 'NODO'
  and s.data_dismissione is null
  and s.data_sospensione is null
;

quit
