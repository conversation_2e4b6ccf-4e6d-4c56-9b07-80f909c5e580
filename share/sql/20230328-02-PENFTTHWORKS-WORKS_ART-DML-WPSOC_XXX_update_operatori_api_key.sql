set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

SET DEFINE OFF;
delete operatori_api_key where id_operatore in (select id_operatore from operatori where login_operatore in ('FIELD_SERVICE_USER','FIELD_SERVICE_EVENT_USER'));
insert into operatori_api_key (id_operatore, api_key, data_creazione, data_revoca) values((select id_operatore from operatori where login_operatore = 'FIELD_SERVICE_USER'), 'eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IkZJRUxEX1NFUlZJQ0VfVVNFUiJ9.DRNh1_9x3S8dbuaMWUwZCUEn8K0UmhTl7aMfLdH1o_s',sysdate, null);
insert into operatori_api_key (id_operatore, api_key, data_creazione, data_revoca) values((select id_operatore from operatori where login_operatore = 'FIELD_SERVICE_EVENT_USER'), 'eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IkZJRUxEX1NFUlZJQ0VfRVZFTlRfVVNFUiJ9.vS9WKJGzOYOkBf0M19cTgsXD3wQhEStr3HxcwvlRy-0',sysdate, null);

show errors

commit;

quit
