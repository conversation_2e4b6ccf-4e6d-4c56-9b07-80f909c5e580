set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqler<PERSON>r exit 1 rollback;
whenever oser<PERSON>r  exit 2 rollback;

update CONF_NODI_ATTESTAZIONE
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTA'
and "minCablesNumbers" = 0
and "maxCablesNumbers" = 48
;

update CONF_NODI_ATTESTAZIONE
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTA'
and "minCablesNumbers" = 49
and "maxCablesNumbers" = 9999999999
;

update CONF_NODI_ATTESTAZIONE
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTE'
and "minCablesNumbers" = 0
and "maxCablesNumbers" = 9999999999
;

update CONF_NODI_TIPO_CABLAGGIO
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTA'
and "cablingType" = 'Splicing'
;

update CONF_NODI_TIPO_CABLAGGIO
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTA'
and "cablingType" = 'Termination'
;

update CONF_NODI_TIPO_CABLAGGIO
set "disableDate" = sysdate
where 1=1
and "networkElementType" = 'PTE'
and "cablingType" = 'Termination'
;

show errors

commit;

quit
