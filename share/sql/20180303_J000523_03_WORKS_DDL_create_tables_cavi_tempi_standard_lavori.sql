set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE TABLE CONF_CAVI_TMP_STD_MAX (
  "manMinutes" FLOAT(126) NOT NULL ENABLE, 
  "insertDate" DATE NOT NULL ENABLE, 
  CONSTRAINT CONF_CAVI_TMP_STD_MAX_UK1 UNIQUE ("manMinutes")
)
;

COMMENT ON COLUMN CONF_CAVI_TMP_STD_MAX."manMinutes" IS 'Minuti/uomo massimi stimati per l''intervento';
COMMENT ON COLUMN CONF_CAVI_TMP_STD_MAX."insertDate" IS 'Data di inserimento della voce di configurazione: verrà utilizzata la entry più recente';
COMMENT ON TABLE CONF_CAVI_TMP_STD_MAX IS 'Tempo massimo standard lavori stimati: se il calcolo supera la soglia, il tempo si imposta uguale alla soglia';

quit
