set line 1000
set autocommit off
set echo on
set SER<PERSON>ROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

accept user default 'works_art' prompt 'dammi nome utente a cui concedere i permessi  (default works_art) '

grant select, insert, update, delete on MACROTASKS to &user;
grant select, insert, update, delete on MACROTASKS_RUN to &user;
grant select on SEQ_MACROTASKS_RUN to &user;

quit
