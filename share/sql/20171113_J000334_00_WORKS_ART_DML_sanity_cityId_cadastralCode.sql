set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

update dati_Tecnici_Attivita
set id_tipo_Dato_Tecnico_Attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_dati_Tecnici_attivita where descrizione = 'cadastralCode')
where id_tipo_Dato_Tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_dati_Tecnici_attivita where descrizione = 'cityId');

update dati_Tecnici dt2
set dt2.id_tipo_Dato_tecnico = (select dt3.id_tipo_Dato_Tecnico from tipi_dati_tecnici dt3 where dt3.nome = 'cadastralCode')
where (dt2.id_sistema, dt2.id_tipo_dato_Tecnico) in (
  select dt.id_sistema, dt.id_tipo_Dato_Tecnico
  from dati_Tecnici dt
    join tipi_Dati_Tecnici tdt on tdt.id_tipo_dato_tecnico = dt.id_tipo_dato_Tecnico
    join v_sistemi s on s.id = dt.id_sistema
  where tdt.nome = 'cityId'
  and s.tipo = 'CAVO'
);

insert into dati_Tecnici
select id_sistema
  , id_sottosistema
  , ID_TIPO_DATO_TECNICO
  , DESCRIZIONE
  , seq_dati_Tecnici.nextval ID_DATO_TECNICO
  , OPER_ULTIMA_VAR
  , DATA_ULTIMA_VAR
  , id_storico_import
from (
select distinct
 s2.id id_sistema
, null id_sottosistema
, (select dts4.id_tipo_Dato_Tecnico from tipi_Dati_Tecnici dts4 where dts4.nome = 'cityId') ID_TIPO_DATO_TECNICO
, dts.valore DESCRIZIONE
, (select op.id_operatore from operatori op where op.login_operatore = 'ROOT') OPER_ULTIMA_VAR
, sysdate DATA_ULTIMA_VAR
, null id_storico_import
from v_sistemi s
  join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'cityId'
  join v_dt_sistemi dts2 on dts2.id_sistema = s.id and dts2.nome = 'cadastralCode'
  join v_dt_sistemi dts3 on dts3.valore = dts2.valore and dts3.nome = 'cadastralCode'
  join v_sistemi s2 on s2.id = dts3.id_sistema
where s.tipo = 'PROJECT'
and s2.tipo = 'CAVO'
);

commit;

quit
