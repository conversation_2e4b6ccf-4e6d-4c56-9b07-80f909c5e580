set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_NODI_LAVORI_RIEPILOGO AS 
  select
   n."customerId"
  ,n."contractId"
  ,n."pop"
  ,n."ring"
  ,n."projectId"
  ,n."pfpId"
  ,n."pfpName"
  ,n."networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."latitude"
  ,n."longitude"
  ,n."networkElementStatus"
  ,n."networkElementContainerStatus"
  ,n."total"
  ,n."notWorkable"
  ,n."workable"
  ,n."done"
  ,n."accounted"
  ,n."estimatedDuration"
  ,n."warnings"
  ,lav."id" "workId"
  ,lav."workStatus"
  ,lav."maker" "workMaker"
  ,lav."teamId" "workTeamId"
  ,lav."startPlannedDate" "workStartPlannedDate"
  ,lav."startWorkDate" "workStartWorkDate"
  ,lav."endPlannedDate" "workEndPlannedDate"
  ,lav."estimatedDuration" "workEstimatedDuration"
  ,lav."endWorkDate" "workEndWorkDate"
  ,lav."spentTime" "workSpentTime"
  ,lav."subContractCode" "workSubContractCode"
  ,lav."techninalAssistantsAssignee" "workTechAssAssignee"
  ,lav."asBuiltStatus"
from
  works.NODI_RIEPILOGO n
    left join v_nodi_lavori lav on lav."networkElementId" = n."networkElementId" and lav."customerId" = n."customerId" and lav."contractId" = n."contractId" and lav."projectId" = n."projectId"
where 1=1
  and n."historicizingRunId" is null
;

quit
