set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop table CAVI;

CREATE TABLE CAVI
(
    "cableId" NUMBER NOT NULL
  , "cableName" VARCHAR2(4000) NOT NULL
  , "cableType" VARCHAR2(4000) NOT NULL
  , "potential" NUMBER
  , "unitOfMeasure" VARCHAR2(4000) NOT NULL
  , "fromNetworkElementId" NUMBER NOT NULL
  , "fromNetworkElementName" VARCHAR2(4000) NOT NULL
  , "fromNetworkElementType" VARCHAR2(4000) NOT NULL
  , "fromNetworkElementGeoLocation" VARCHAR2(4000) NOT NULL
  , "toNetworkElementId" NUMBER NOT NULL
  , "toNetworkElementName" VARCHAR2(4000) NOT NULL
  , "toNetworkElementType" VARCHAR2(4000) NOT NULL
  , "toNetworkElementGeoLocation" VARCHAR2(4000) NOT NULL
  , "plannedUndergroundLength" FLOAT
  , "plannedAerialLength" FLOAT
  , "plannedFacadeLength" FLOAT
  , "status" VARCHAR2(4000) NOT NULL
  , "doneUndergroundLength" FLOAT
  , "doneAerialLength" FLOAT
  , "doneFacadeLength" FLOAT
  , "doneHandmaidLength" FLOAT
  , "duration" FLOAT not null
  , "runId" NUMBER not null
  , "historicizingRunId" NUMBER
);

ALTER TABLE CAVI
ADD CONSTRAINT CAVI_FK1 FOREIGN KEY
(
  "runId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

ALTER TABLE CAVI
ADD CONSTRAINT CAVI_FK2 FOREIGN KEY
(
  "historicizingRunId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

COMMENT ON TABLE CAVI IS 'Elenco dei cavi';

COMMENT ON COLUMN CAVI."cableId" IS 'Identificativo SiNFO del cavo';
COMMENT ON COLUMN CAVI."cableName" IS 'Identificativo mnemonico (nome) del cavo';
COMMENT ON COLUMN CAVI."cableType" IS 'Tipologia cavo';
COMMENT ON COLUMN CAVI."potential" IS 'Potenzialità del cavo';
COMMENT ON COLUMN CAVI."unitOfMeasure" IS 'Unità di misura (valori possibili ''mt'')';
COMMENT ON COLUMN CAVI."fromNetworkElementId" IS 'Identificativo SiNFO dell''elemento di rete di partenza';
COMMENT ON COLUMN CAVI."fromNetworkElementName" IS 'Identificativo mnemonico (nome) dell''elemento di rete di partenza';
COMMENT ON COLUMN CAVI."fromNetworkElementType" IS 'Tipo dell''elemento di rete di partenza';
COMMENT ON COLUMN CAVI."fromNetworkElementGeoLocation" IS 'Coordinate georeferenziali dell''elemento di rete di partenza';
COMMENT ON COLUMN CAVI."toNetworkElementId" IS 'Identificativo SiNFO dell''elemento di rete d''arrivo';
COMMENT ON COLUMN CAVI."toNetworkElementName" IS 'Identificativo mnemonico (nome) dell''elemento di rete d''arrivo';
COMMENT ON COLUMN CAVI."toNetworkElementType" IS 'Tipo dell''elemento di rete d''arrivo';
COMMENT ON COLUMN CAVI."toNetworkElementGeoLocation" IS 'Coordinate georeferenziali dell''elemento di rete d''arrivo';
COMMENT ON COLUMN CAVI."plannedUndergroundLength" IS 'Lunghezza tratta sotterranea progettata: valorizzato solo se ''status'' NON vale ''Done''';
COMMENT ON COLUMN CAVI."plannedAerialLength" IS 'Lunghezza tratta aerea progettata: valorizzato solo se ''status'' NON vale ''Done''';
COMMENT ON COLUMN CAVI."plannedFacadeLength" IS 'Lunghezza tratta su facciata progettata: valorizzato solo se ''status'' NON vale ''Done''';
COMMENT ON COLUMN CAVI."status" IS 'Indica lo stato SiNFO del cavo (valori possibili ''Planned'', ''Workable'', ''Done'')';
COMMENT ON COLUMN CAVI."doneUndergroundLength" IS 'Lunghezza tratta sotterranea realizzata: valorizzato solo se ''status'' vale ''Done''';
COMMENT ON COLUMN CAVI."doneAerialLength" IS 'Lunghezza tratta aerea realizzata: valorizzato solo se ''status'' vale ''Done''';
COMMENT ON COLUMN CAVI."doneFacadeLength" IS 'Lunghezza tratta su facciata realizzata: valorizzato solo se ''status'' vale ''Done''';
COMMENT ON COLUMN CAVI."doneHandmaidLength" IS 'Lunghezza tratta manuale realizzata: valorizzato solo se ''status'' vale ''Done''';
COMMENT ON COLUMN CAVI."duration" IS 'Durata stimata della posa cavi';
COMMENT ON COLUMN CAVI."runId" IS 'Id del campionamento';
COMMENT ON COLUMN CAVI."historicizingRunId" IS 'Id del campionamento che ha sostituito quello indicato nella riga, se null si tratta del campionamento più recente';

quit
