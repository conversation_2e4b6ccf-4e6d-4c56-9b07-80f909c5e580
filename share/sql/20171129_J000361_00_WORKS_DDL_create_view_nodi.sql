set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

drop view v_nodi_lavori_dettaglio;

CREATE OR REPLACE FORCE VIEW V_NODI AS 
  select
   s."customerId"
  ,s."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,to_char(s."projectId") "projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,to_char(n."networkElementId") "networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."cablingType"
  ,n."networkElementIsDone"
  ,n."infrastructureExists"
  ,n."infrastructureIsDone"
  ,n."inputCableId"
  ,n."inputCableName"
  ,n."inputCablePotential"
  ,n."inputCablePotentialPlanned"
  ,n."outputCableId"
  ,n."outputCableName"
  ,n."outputCablePotential"
  ,n."outputCablePotentialPlanned"
  ,n."status"
  ,n."splicedFibers"
  ,n."terminatedFibers"
from NODI n
  join sync_runs s on s."runId" = n."runId" and n."historicizingRunId" is null
  join v_progetti pfp on s."customerId" = pfp."customerId" and s."contractId" = pfp."contractId" and s."projectId" = pfp."projectId"
;

quit
