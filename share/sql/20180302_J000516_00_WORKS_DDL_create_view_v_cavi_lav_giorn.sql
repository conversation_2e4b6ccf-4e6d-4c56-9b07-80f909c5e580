set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_CAVI_LAV_GIORN AS 
  select final."teamId"
  , final."executionDate"
  , final."cableId"
  , final."fromNetworkElementId"
  , final."spentTime"
  , final."eventType"
  , case when final."eventType" = 'COMPLETE' then (select vcl."doneUndergroundLength" From v_cavi vcl where vcl."cableId" = final."cableId")
    else null
    end "sinfoDoneUndergroundLength"
  , case when final."eventType" = 'COMPLETE' then (select vcl."doneAerialLength" From v_cavi vcl where vcl."cableId" = final."cableId")
    else null
    end "sinfoDoneAerialLength"
  , case when final."eventType" = 'COMPLETE' then (select vcl."doneFacadeLength" From v_cavi vcl where vcl."cableId" = final."cableId")
    else null
    end "sinfoDoneFacadeLength"
  , case when final."eventType" = 'COMPLETE' then (select vcl."doneHandmaidLength" From v_cavi vcl where vcl."cableId" = final."cableId")
    else null
    end "sinfoDoneHandmaidLength"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneUndergroundLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "doneUndergroundLength"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneAerialLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "doneAerialLength"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneFacadeLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "doneFacadeLength"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneHandmaidLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "doneHandmaidLength"
from (
  select es.id, (
    select dta.valore
    from v_dta dta
    where dta.id_attivita = es.id
    and dta.data_Esecuzione = es.ultimo_Step_giorno
    and dta.nome = 'teamId'
  ) "teamId"
  , es.data_esecuzione "executionDate"
  , es."cableId"
  , es."fromNetworkElementId"
  , (
        select dtats.valore
        from v_dta dtats
        where 1=1
        and dtats.id_attivita = es.id
        and dtats.nome = 'spentTime'
        and dtats.data_esecuzione = es.ultimo_Step_giorno
      )-nvl((
        select dtats.valore
        from v_dta dtats
        where 1=1
        and dtats.id_attivita = es.id
        and dtats.nome = 'spentTime'
        and dtats.data_esecuzione = (
          select max(dtats2.data_esecuzione)
          from v_dta dtats2
          where dtats2.id_attivita = dtats.id_attivita
            and dtats2.nome = dtats.nome
            and dtats2.data_esecuzione < es.data_esecuzione
        )
      ),0)"spentTime"
    , (
        select dtats.valore
        from v_dta dtats
        where 1=1
        and dtats.id_attivita = es.id
        and dtats.nome = 'eventType'
        and dtats.data_esecuzione = es.ultimo_Step_giorno
      ) "eventType"
  From (
    with steps as (
    select distinct v.id
      , trunc(dta.data_esecuzione) data_esecuzione
      , dts.valore "cableId"
      , dtsNE.valore "fromNetworkElementId"
    from v_attivita v
      join v_sistemi s on s.id = v.id_Sistema
      join v_dta dta on dta.id_attivita = v.id and dta.nome = 'eventType'
      join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'cableId'
      join v_dt_sistemi dtsNE on dtsNE.id_sistema = s.id and dtsNE.nome = 'fromNetworkElementId'
    where 1=1
      and v.nome_tipo_Attivita = 'LAVORO'
      and s.tipo = 'CAVO'
      and dta.valore in ('COMPLETE','PARTIAL_COMPLETE','NOT_DONE')
    )
    select steps.*, (
      select max(dta.data_esecuzione) data_esecuzione
      from v_dta dta
      where dta.id_attivita = steps.id
      and dta.nome = 'eventType'
      and dta.valore in ('COMPLETE','PARTIAL_COMPLETE','NOT_DONE')
      and dta.data_Esecuzione < steps.data_esecuzione+1
    ) ULTIMO_STEP_GIORNO
    from steps
  ) es
) final;

quit
