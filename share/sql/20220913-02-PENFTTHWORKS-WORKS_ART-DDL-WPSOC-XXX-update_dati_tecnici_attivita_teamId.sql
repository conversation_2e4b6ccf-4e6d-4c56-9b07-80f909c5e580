set line 1000
set autocommit off
set echo on
set SERVEROUT off

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

-- Migrazione non condizionale (PTE e Manutenzioni)
update dati_Tecnici_attivita dta
set dta.descrizione = nvl((select m.new_cid from works.mappa_cid m where m.old_cid = dta.descrizione), dta.descrizione)
where dta.id_attivita in (
    select  a.id
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'PTE', 'CorrMaintenance','ExtrMaintenance','PreeMaintenance' )
) and dta.id_tipo_Dato_Tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_Dati_Tecnici_attivita where descrizione = 'teamId')
;

-- Migrazione condizionale (Network)
update dati_Tecnici_attivita dta
set dta.descrizione = nvl((select m.new_cid from works.mappa_cid m where m.old_cid = dta.descrizione), dta.descrizione)
where dta.id_attivita in (
    select  a.id
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    join    v_dt_sistemi nid
    on      nid.id_sistema = a.id_sistema
    and     nid.nome = 'networkId'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'Network', 'NetworkFibercop' )
    and     exists ( select 1 from works.network_da_migrare where "networkId" = nid.valore )
) and dta.id_tipo_Dato_Tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_Dati_Tecnici_attivita where descrizione = 'teamId')
;

show errors

commit;

quit
