set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oser<PERSON>r  exit 2 rollback;

delete from CONF_NODI_ATTESTAZIONE;
delete from CONF_NODI_CAVO_SPILLATO;
delete from CONF_NODI_POSA_MECCANICA;
delete from CONF_NODI_TIPO_CABLAGGIO;
delete from CONF_NODI_TEMPO_TOTALE;

insert into CONF_NODI_POSA_MECCANICA values (
  'PFP',
  55/60,
  sysdate,
  null
);

insert into CONF_NODI_POSA_MECCANICA values (
  'PD',
  45/60,
  sysdate,
  null
);

insert into CONF_NODI_POSA_MECCANICA values (
  'GL',
  45/60,
  sysdate,
  null
);

insert into CONF_NODI_POSA_MECCANICA values (
  'PTE',
  15/60,
  sysdate,
  null
);

insert into CONF_NODI_POSA_MECCANICA values (
  'PTA',
  30/60,
  sysdate,
  null
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
)
values(
  'PFP',
  0,
  48,
  30/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFP',
  49,
  144,
  40/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFP',
  145,
  192,
  50/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFP',
  193,
  60/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFS',
  0,
  48,
  60/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFS',
  49,
  96,
  90/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PFS',
  97,
  120/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PD',
  0,
  48,
  30/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PD',
  49,
  144,
  40/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PD',
  145,
  50/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'GL',
  0,
  48,
  30/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'GL',
  49,
  144,
  40/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'GL',
  145,
  50/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "manHours"
  , "insertDate"
) values(
  'PTE',
  30/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "maxCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PTA',
  0,
  48,
  30/60,
  sysdate
);

insert into CONF_NODI_ATTESTAZIONE (
  "networkElementType"
  , "minCablesNumbers"
  , "manHours"
  , "insertDate"
) values(
  'PTA',
  49,
  40/60,
  sysdate
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PFP',
  'Splicing',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PFS',
  'Splicing',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PFS',
  'Termination',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PD',
  'Splicing',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'GL',
  'Splicing',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PTE',
  'Termination',
  2.5/60,
  sysdate,
  null
);

insert into CONF_NODI_TIPO_CABLAGGIO values (
  'PTA',
  'Termination',
  1.5/60,
  sysdate,
  null
);

insert into CONF_NODI_CAVO_SPILLATO values (
  'PD',
  90/60,
  sysdate,
  null
);

insert into CONF_NODI_CAVO_SPILLATO values (
  'GL',
  90/60,
  sysdate,
  null
);

insert into CONF_NODI_TEMPO_TOTALE values (
  'CabinaEnel',
  60/60,
  sysdate,
  null
);

insert into CONF_NODI_TEMPO_TOTALE values (
  'QuadroIP',
  60/60,
  sysdate,
  null
);

show errors

commit;

quit