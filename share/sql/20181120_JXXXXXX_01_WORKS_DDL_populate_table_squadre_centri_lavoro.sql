set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

REM INSERTING into WORKS.SQUADRE_CENTRI_LAVORO
SET DEFINE OFF;
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ABAMA<PERSON>AN<PERSON>','RID<PERSON>','10200889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','ABATANGEL<PERSON>','G<PERSON><PERSON>IE<PERSON>','10085039');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','ABATI','STEFANO','10061049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ACCAR<PERSON>','PIERA','10062001');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','ACCETTURA','DOMENICO','10081466');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','ACETO','GIUSEPPE','10084787');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ACHERI','MARTINO','10083088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','ACIERNO','GIANFRANCO','10090624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','ACQUARONE','ANDREA','10080697');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ADAMO','ANTONIO','10046143');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','AFFATATO','DOMENICO','10089665');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','AFFORTUNATO','CARMINE','10091937');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','AGATI','NUNZIO','10086871');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','AGATIELLO','ANTONIO','10090631');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','AGGIO','MATTEO','10092557');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','AGLIOCCHI','SERGIO','10085235');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','AGNELLO','FRANCESCO','10085833');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','AGNOLIN','RENATO','10044752');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','AGOSTINO','GIUSEPPE','10085976');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','AGRELLO','PROSPERO','10092777');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','AGRELLO','GIANLUIGI','10092792');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','AGRESTA','GIANFRANCO','10084907');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','AGUS','FRANCESCO','10085309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','AGUZZI','PIERGIACOMO','10062002');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','AIELLO','SALVATORE GIOVANNI','10091261');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','ALAGIA','LUIGI','10080858');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ALBANO','DOMENICO','10092616');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','ALBAROSATA','BASILIO','10078950');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','ALBERI','ROBERTO','10086305');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','ALBINI','FLAVIO','10091369');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','ALBINI','FAUSTO','10083300');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ALESSANDRINI','FLAVIO','10044694');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','ALESSANDRINO','VITO','10084044');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','ALESSANDRO','ALESSIO','10092148');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ALESSI','EMILIO','10062004');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','ALFANO','AMEDEO','10090937');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ALFIERI','DOMENICO','10090943');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ALFIERI','GIOVANNI','10082569');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ALFIERO','MARCO','10046102');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ALFISI','CAMILLO','10080730');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','ALIOTTA','MIRKO','10092421');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ALIOTTA','CARMELO','10045651');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ALLEGORICO','DOMENICO','10062005');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','ALLOCCA','GIOVANNI','10092283');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ALLOCCA','FELICE','10086080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ALMA','FRANCESCO','10080650');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ALONZO','PAOLO','10081139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','ALPARONE','ALESSANDRO','10083428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','ALTAMURA','LEONARDO','10091834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','ALTEMURA','ATTILIO','10090286');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','ALTIERI','SILVANO GIOVANNI','10092483');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ALUNNI','LORELLA','10090876');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','AMALFI','BIAGIO','10079554');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','AMATO','GIUSEPPE','10092168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','AMATO','LUCA','10086675');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','AMATO','ANTONIO','10045267');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','AMATO','SALVATORE','10086677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','AMATO','ANGELO','10051936');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','AMELIA','GIUSEPPE','10086560');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','AMENDOLA','SALVATORE','10085057');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','AMENDOLA','GIOVANNI','10079749');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','AMENDOLA','CARLO','10091163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','AMENDOLA','LUIGI','10091164');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','AMIOTTI','GABRIELE','10044785');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','AMIRATO','GIAMPIERO','10084874');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','AMOROSO','ANDREA','10082852');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','ANDALO','ALFREDO','10045447');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','ANDREOLI','ANGELO','10083735');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','ANDREONI','MARCO','10062006');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ANDREOZZI','CIRO','10088343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','ANDRIANI','GIACOMO','10081468');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ANDRONICO','ROSARIO','10081286');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ANGELETTI','MARCELLO','10062007');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','ANGELINI','MIRCO','10083332');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ANGELOCOLA','TIZIANO','10062009');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ANGIERO','ANGELO','10078710');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ANGIOI','MASEN','10200912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','ANGIUS','MARIANO','10084782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ANGIUS','DAVIDE','10200858');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','ANNESE','NICOLO''','10079871');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','ANNUNZIATA','ALDO','10085992');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ANSALONE','VINCENZO','10078716');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ANTELMI','VINCENZO','10061164');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','ANTIFORA','PIETRO','10083760');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ANTOLINI','LORIS','10044419');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','ANTONELLI','CARLO','10045836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ANTONELLI','LUCA','10091894');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','ANTONETTI','LUCIANO','10086456');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','APADULA','FRANCESCO','10090219');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','AQUILI','MASSIMILIANO','10092484');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','AQUINO','FRANCESCO','10083774');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','ARATO','MASSIMO','10085098');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ARCELLA','ANDREA','10084056');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ARCELLA','CORRADO','10092877');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ARCONE','LUIGI','10061018');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ARDENGHI','ALESSANDRO','10091618');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','ARDOVINI','ANDREA','10086198');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ARDOVINI','GIANLUCA','10086670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','ARENA','SEBASTIANO','10090922');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','ARENA','LUIGI ANTONIO','10083781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','ARENA','ANTONINO','10045136');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','ARGENTO','ROBERTO','10091316');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ARGIOLAS','ANTONIO','10045486');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','ARIZIA','NUNZIO','10092574');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','ARMENISE','SABINO','10091022');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ARMENTANO','VITTORIO','10087601');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ARMENTO','ALESSANDRO','10091060');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','ARONADIO','ETTORE','10080150');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','ARONICA','FABRIZIO','10087861');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ARRICHIELLO','RINO','10082794');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','ARRIGONI','PIETRO','10045070');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ARTINI','STEFANO','10092252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','ASARO','VINCENZO','10090384');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ASCHETTINO','GIOVANNI','10079648');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','ASTOLFI','VALTER','10080613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','ATILLAH','MOHAMED REDA','10200817');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','ATLANTE','NICOLA','10089036');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ATRIGNA','DOMENICO','10092581');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ATZEI','MAURO','10045785');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ATZENI','ROBERTO','10045582');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ATZORI','DARIO','10200770');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','AUDINO','GIROLAMO','10082572');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','AUGELLI','ELEONORA','10090890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','AURILIA','MICHELE','10092250');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','AUTORINO','UMBERTO','10086678');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','AUTUORI','FRANCESCO','10092646');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','AVALLONE','GIUSEPPE','10085898');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','AVERSA','FABIO','10084528');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','AVERSANO','PASQUALE','10083206');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','AVERSANO','GENNARO','10079962');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BACCHIS','GIUSEPPE','10045392');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BACCI','UMBERTO','10046228');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','BACCICHETTI','STEFANO','10085165');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','BACCO','GABRIELE','10091410');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BACCOLINI','VALERIO','10085337');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','BACHIS','VITO','10051533');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','BADAMI','PIERLUCA','10200757');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','BAGGIANI','MASSIMO','10083016');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BAGNO','ALFONSO','10091519');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','BAILONE','ENZO','10061162');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','BALBI','FEDERICO','10091953');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BALBONI','FRANCO','10044846');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','BALDASSARRE','LUCIO','10045034');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','BALDASSARRE','LUCIANO','10044657');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BALDINI','MAURIZIO','10051857');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','BALISTRERI','MASSIMILIANO','10088416');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','BALLIN','LINO','10091000');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BALLISAI','SIMONE','10200771');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','BALLOCCI','CLAUDIO','10081792');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','BALLOCCI','PIERGUIDO','10086135');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BALZI','DARIO','10092936');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BANGONI','AGOSTINO','10090478');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BANINI','EMILIO','10092139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','BARALDI','GIANCARLO','10078023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BARBARO','ROBERTO','10092277');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','BARBARO','ANTONINO','10045083');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BARBERIO','FRANCESCO','10080387');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','BARBINI','FABRIZIO','10080368');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BARBOLLA','GIOVANNI','10080984');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BARDUCCO','CINZIA','10086150');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','BARISIONE','GIANCARLO','10091530');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','BARLETTA','DANIELE','10091191');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BARNA','BIANCA PARASCHIVA','10092027');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BARNABA''','LUIGI','10081430');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BAROCCETTI','ALESSANDRO','10092409');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','BARONE','SALVATORE','10085133');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','BARONE','MICHELE','10078896');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','BARONE','VINCENZO','10091740');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','BARRA','CLAUDIO GAETANO','10082564');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BARRERA','GIOVANNI','10092806');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BARRESI','VALTER','10078685');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','BARRICELLI','CARMINE','10081213');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','BARRICIELLO','ROBERTO','10084531');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BARTOLI','ROBERTO','10083343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','BARTOLOTTA','MARCO FRANCESCO','10091154');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','BARTUCCI','ATTILIO','10085002');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BASILE','NUNZIO','10091836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','BASILE','CIRO','10082848');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BASILICO','CARLO EMANUELE','10062008');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','BASSANI','GIULIANO','10090439');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','BASSI','GABRIELE','10090733');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','BASSO','ROBERTO','10081027');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BASSOTTI','FLAVIO','10091312');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BASTIOLI','ENRICO','10078908');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BATTAGLINO','FRANCESCO','10083579');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','BATTIATO','SALVATORE','10092627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','BATTISTEL','STEFANO','10082499');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','BATTISTI','ALESSIO','10092145');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','BATTISTINI','ANDREA','10082897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','BAUDINO','LUCA','10092162');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BAVARESCO','LIVIO','10044770');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BECCARIA','GIANLUCA','10080856');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BEGA','ANDREA','10091046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','BELARDI','MICHELE','10091809');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BELFIGLIO','ROBERTO','10044982');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','BELLA','MARIO','10091440');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','BELLA','ANGELO','10044465');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','BELLAN','LUCIANO','10080201');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BELLARDO','MARCO','10041560');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','BELLIA','ANTONIO','10091923');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','BELLISARIO','DANIELE','10091080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','BELLO''','MASSIMO','10078247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BELLOBUONO','CARMINE','10091215');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BELLOTTO','ALBERTO','10089155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BELTRAME','MAURO','10085278');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','BELTRAMI','MASSIMO','10082519');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','BELVISO','NICOLA','10092125');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','BENATO','MICHELE','10045443');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BENEDETTI','THOMAS','10091179');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BENETTON','MATTIA','10091943');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BENVENUTI','LUCIANO','10084929');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','BENVENUTO','VINCENZO','10045038');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','BERARDI','VITO','10080342');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BERETTA','FABIO','10061157');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','BERGHI','MASSIMILIANO','10091672');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','BERNARDINI','GIULIO','10086113');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BERNARDINI','ROBERTO','10090472');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BERNOCCHI','ALESSIO','10090023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BERTOLA','GIORGIO','10089661');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BERTOLAZZI','ANDREA','10091252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','BERTOLI','ROBERTO','10090144');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','BERTONE','DARIO','10091433');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','BERTOZZI','ANTONIO','10045718');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BERTUCCIO','FRANCESCO','10091801');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BETTINI','GIOVANNI','10092231');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','BEVILACQUA','ROSARIO','10092813');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BIANCHI','MASSIMILIANO','10092080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','BIANCHI','CLAUDIO','10092114');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','BIANCHINA','CARMINE','10091908');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','BIANCO','GIOVANNI','10082959');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BIGETTI','PAOLO','10080296');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','BIGLIERI','LORENZO','10084771');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BIGNULIN','RAFFAELE','10092558');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','BIONDI','UMBERTO','10081054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','BISAGNI','GIOVANNI','10078100');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','BIVACQUA','FRANCESCO','10091389');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','BIVACQUA','MASSIMILIANO','10092526');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','BIZ','ALESSANDRO','10046316');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','BIZZARRI','ANGELO','10081608');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BLANDOLINO','ANTONIO','10091447');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BLASI','LUCA','10084024');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BLASI','FRANCESCO','10091269');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BLEVE','VINCENZO','10091631');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','BLOISE','DANIELE','10092182');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','BOCCA','PASQUALE','10080976');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BOGNOLO','FEDERICO','10092811');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BOI','ANTONELLO','10082892');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BOI','LUCIANO','10045509');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BOLDRINI','MASSIMO','10083369');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BOLOGNATO','EROS','10091892');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','BOLOGNINI','GIOVANNI','10080187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','BOMBINO','FRANCESCO','10084322');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BOMBONATI','ROBERTO','10081137');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BOMPASSO','NICOLA','10092835');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','BONACCI','SAVERIO','10080836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','BONANNI','FRANCESCO','10086561');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','BONANNI','ALESSIO','10091508');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BONANNO','PIERLUIGI','10091463');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','BONANNO','GIUSEPPE','10091571');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BONDANINI','MASSIMO','10090842');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BONERBA','ANGELO','10091317');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','BONGIOVANNI','GERARDO','10044869');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','BONGIOVANNI','LUIGI','10085895');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','BONI','CLAUDIO','10085733');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','BONIFACIO','DEMETRIO','10086340');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BONZANO','SADRY LUIGI','10084583');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BORDONARO','SANTINO','10061037');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','BORELLI','PATRIZIO','10092744');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','BORGATO','MARCO','10078303');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BORGHI','DAVIDE','10090026');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BORGOGNI','PIER GIOVANNI','10082723');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','BORRELLI','PASQUALE','10082589');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','BORRELLI','GIOVANNI','10092004');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BORRIELLO','GERARDINA','10091184');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','BORTOLI','SERGIO','10078706');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BORTOLIN','RENATO','10046239');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','BORZACCHIELLO','RAFFAELE','10082683');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','BOSCAINO','LUIGI','10046214');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','BOSCHETTO','LINDO','10044630');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BOSCHI','MICHELE','10091870');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','BOSCHIERO','MATTEO','10091188');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','BOSIO','ANDREA','10200900');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','BOTINDARI','PIETRO','10061206');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BOTRUGNO','PASQUALE','10046240');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','BOTTARO','GIORGIO','10089544');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BOTTURA','MAURO ORAZIO','10086487');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','BOVIENZO','GIANFRANCO','10084064');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','BOZZANO','EROS','10090347');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BOZZOLAN','LORENZO','10043675');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BRAIDO','RUDY','10091606');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BRAMANTE','COSIMO','10091781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BRAMANTE','LUIGI','10091775');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','BRANCA','GIOVANNI','10092464');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','BRANCALEONE','PASQUALE','10080815');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','BRANDOLINI','VINCENZO','10090267');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','BRATTA','NICOLA ANDREA','10085388');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BRAVI','ALESSANDRO','10091358');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','BRAY','SERGIO QUINTINO','10091535');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BREMBILLA','ALESSANDRO','10081019');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BREZ','MARCO','10062017');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','BRIGANTE','ELIO','10052033');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','BRIGNOLO','ALFREDO','10083426');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BRIGUGLIO','DANIELE GIUSEPPE','10092046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BROCCIA','WALTER','10084795');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BROCH','GINO','10091332');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','BROCH','THOMAS','10092501');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','BROGGI','ANTONIO','10090671');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','BROZZU','ALESSANDRO','10061237');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','BRUNELLI','MASSIMO','10083427');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BRUNETTO','CRISTIAN','10091061');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','BRUNO','FEDERICO','10091912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BRUNO','ROBERTO','10062020');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','BRUNO','MARIO','10090998');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','BRUNO','GIUSEPPE','10041776');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','BRUNO','CARMINE','10082777');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','BRUNO','GIOVANNI','10092779');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','BRUNO','ANTONINO','10092731');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','BRUSCHI','MAURO','10061234');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','BRUSCINO','ANGELO','10086480');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BRUTTI','LUCA','10091626');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','BUCARELLI','ROBERTO','10062022');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','BUCCERI','ALESSANDRO GIOVANN','10091920');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','BUCCI','ANTONIO','10044857');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','BUCOLO','VITTORIO','10092580');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','BUDAI','LUCA','10046371');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','BULFONI','GIULIO','10091608');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','BULFONI','ENZO','10082931');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','BUNO','RICHARD IAN','10200881');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','BUONINCONTRO','GIUSEPPE','10081451');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','BUONOCORE','EMILIO','10078883');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','BUONOCORE','SABATO','10091952');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BURATTO','ALFIERO ALBERTO','10062023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','BURTI','MATTIA','10091670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','BUSATTO','GIOVANNI','10045820');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','BUSELLU','ANTONIO MICHELE','10084332');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','BUSI','FEDERICO','10086615');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','BUSSOTTI','LUCIA','10062256');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','BUZZO','MAURIZIO','10085768');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CABIDDU','MARIO SEBASTIAN','10084302');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CABRAS','DANIELE','10091564');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CACCAVALLO','LUIGI PAOLO','10091207');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CADDEO','MASSIMO','10045563');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAFARELLI','GUIDO','10082778');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAFARELLI','MICHELE','10086680');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','CAFISSE','MARCO','10091099');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CAGNAZZO','PAOLO','10091114');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CAIVANO','ANTONIO','10080398');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','CALABRESE','CARMINE','10090608');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CALAMARI','STEFANO','10045133');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CALANDRELLI','CARMINE','10041741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CALASCIBETTA','GIUSEPPE','10091006');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CALCATERRA','LUIGI','10045168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','CALDARESI','FABRIZIO','10085735');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CALDARESI','NICOLA','10051944');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CALDERARO','GIUSEPPE','10083789');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CALEGARI','ANGELO','10092704');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CALI''','GIUSEPPE VINCEN','10086247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CALIFANO','SALVATORE','10081421');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','CALIO''','SIMONE','10091936');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CALISTI','PAOLO','10061003');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CALLAI','PIETRO','10084126');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','CALVANO','DINO','10085080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','CALVO','EMANUELE','10051009');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CALZA','ANDREA','10091762');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CALZAVARA','FRANCESCO','10091360');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','CAMAGGI','ANDREA','10092251');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CAMARDELLA','ANIELLO','10084050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','CAMEROTTO','ANDREA','10046243');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','CAMILLONI','SIMONE','10083923');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CAMITI','GIANCARLO','10081316');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CAMMARATA','DOMENICO','10080651');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CAMOLETTO','MARCO','10088517');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','CAMPAGNA','ROBERTO','10200796');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','CAMPAGNOL','ALBERTO','10041506');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CAMPI','ELIO','10061193');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CAMPOLMI','FABIO','10079269');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CAMPOLMI','GINO','10078547');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CANAPINI','STEFANO','10045327');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CANARI','MASSIMO','10044799');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','CANEO','VITTORIO','10044844');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','CANFORA','NICOLA','10078714');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CANFORA','GIUSEPPE','10084047');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','CANGEMI','ANTONINO','10084584');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CANGEMI','VITO','10061259');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CANGEMI','GIUSEPPE','10090260');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CANNATA','ANTONIO','10092527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CANNEVA','EMILIANO','10092393');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','CANNILLO','MARCO','10083948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CANNILLO','GIORGIO','10092647');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CANNIZZO','MAURIZIO','10061095');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','CANONICO','ANTONIO','10091513');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CANTARELLA','FEDERICO','10091392');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','CANTATORE','MASSIMILIANO','10083310');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CANTISANI','EGIDIO','10091202');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CANTONE','GIUSEPPE','10081606');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CANTONI','MASSIMILIANO','10083311');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CANTONI','GIOVANNI CARLO','10046382');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CANZIAN','STEFANO','10062030');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CANZIAN','RUDI','10091103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CAPASSO','MICHELE','10085342');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAPASSO','MICHELE','10082807');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','CAPATTI','LUCA','10084112');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAPONE','TIZIANA','10090462');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CAPONIO','ONOFRIO','10091782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CAPOZZA','FABIO','10062247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CAPPAI','ANDREA','10200805');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CAPPARELLI','DORIANO','10080216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','CAPPELLARO','STEFANO','10086046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAPPELLUCCIO','PIETRO','10085712');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAPPETTA','SILVIO RAFFAELE','10079869');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CAPPUCCINO','VITALE PAOLO','10084158');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CAPRINO','ALESSANDRO','10090746');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CAPRIOLI','MARCO','10092559');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CAPRIOLI','VITTORIO','10045273');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CAPUA','ROCCO','10082207');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','CAPUANO','ANTONIO','10082886');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CAPUOZZO','GIANCARLO','10084867');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CARA','MARCO','10200806');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CARAFA','ALDO','10046052');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CARAMATTA','GIUSEPPE','10082765');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CARAMIA','ATTILIO','10091723');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CARAPELLOTTI','MAURIZIO','10084200');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CARBONI','ALESSIO','10091609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CARBONI','MATTIA','10092617');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CARBONI','MARCO','10200915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','CARDI','GIORGIO','10082810');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CARDONE','UMBERTO','10091776');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CARECCI','MARCO','10084724');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CARELLA','GIOVANNI','10091981');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CARFORA','DOMENICO','10083079');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CARIA','FRANCESCO','10092618');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CARIA','VIRGINIO ANTONI','10084311');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','CARILLO','CIRO','10092195');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','CARINGELLA','MICHELE ANTONIO','10090939');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','CARIO','GINO','10045787');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','CARIOSCIA','FRANCESCO','10091116');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','CARLETTO','ISAAC','10092649');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CARLI','FRANCO','10080772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CARLI','VITTORIO','10081832');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','CARLINI','FABIO','10045453');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','CARLONE','LUCIO','10092609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CARLUCCI','GINO','10078552');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','CARMASSI','LUCIANO','10080942');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','CAROZZI','YARI','10091088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CARRAFIELLO','MARIO','10091939');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CARRARO','ANDREA','10200864');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','CARREGA','SIMONE','10091667');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CARTA','GIAN FRANCO','10082235');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CARTANI''','FRANCESCO','10089313');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','CARTASSO','CLAUDIO','10052216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CARUSO','GIOVANNI','10084007');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CARUSO','RAFFAELE','10089954');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','CARUSO','VINCENZO SIMONE','10092683');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CARUSO','FRANCESCO ANTONIO','10091777');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CARUSO','NATALE','10091165');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CASACASTI','MASSIMILIANO','10084263');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CASANOVA','NICOLAS','10092120');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CASANOVA','PIERLUIGI','10092119');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CASATI','CARLO','10080937');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CASCELLA','GIOVANNI','10080669');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CASCELLA','GIUSEPPE','10092279');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','CASCINA','CALOGERO','10085163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CASCIOTTI','RAFFAELE','10044834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CASCONE','CIRO','10045433');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CASELLA','CARMINE','10086667');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CASIMIRRI','FAUSTO','10005207');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CASIRATI','MASSIMO','10091842');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','CASOLARI','ANDREA','10086433');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','CASOLE','LIBERATO','10080171');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','CASSANO','ROBERTO','10084772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','CASSANO','GIOVANNI','10091155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CASSESE','SEBASTIANO','10085068');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CASTAGNA','LORENZO','10082452');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CASTALDO','SEBASTIANO','10080844');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CASTELLANO','VINCENZO','10083773');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CASTIELLO','VINCENZO','10082506');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','CASTIELLO','SAVERIO','10092485');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','CASTIGLIONE','ANTONELLO','10092540');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CASTIONI','ALBERTO GRAZIANO','10090438');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CASTRICONE','MASSIMO','10091361');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CASTRIGNANO''','SERGIO','10091242');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CASTROGIOVANNI','GIUSEPPE SALVAT','10089327');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','CASTRONOVO','FRANCESCO','10080481');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CASTRONOVO','MICHELE','10091527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','CASU','ANTONELLO','10085040');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','CASU','MAURO','10089642');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CASU','GIANBATTISTA','10085498');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CASU','ROBERTO','10045448');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','CASULA','MAURO','10086400');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CASULA','GIANNI BRUNO','10090816');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CASULA','ALESSANDRO','10092475');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','CASULLI','GIANCARLO','10090633');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CATALANO','EMANUELE','10091434');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CATALANO','MARIO','10083764');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CATALDO','VITO','10045967');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CATAROZZI','ROSARIO','10091978');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CATENA','FABIO','10062036');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','CATTANO','ROBERTO','10085732');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','CATTO','FLAVIO','10084646');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CATUCCI','IGNAZIO','10091121');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','CAUSIN','MARINO','10044095');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CAUZO','ANTONIO','10045952');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','CAVALLARO','NUNZIO','10083980');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CAVALLARO','ANGELO','10092528');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CAVALLARO','FILADELFO ENRICO','10091495');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CAVALLINI','ORFEO','10085064');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','CAVALLINI','FRANCESCO','10092828');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','CAVANNA','ROBERTO','10041678');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CAVATAIO','FRANCESCO','10090322');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CAVERZAN','DINO','10083432');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CAZZATO','LUCA','10091356');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CECCHIN','ROBERTO','10091160');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CECCONET','DANIELE','10092502');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CECI','LUIGI','10091142');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CECI','FRANCESCO','10091143');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CECUTTO','PAOLO','10046389');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','CEGLIA','SANTE','10081952');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CELENTANO','THEO','10091704');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','CELLI','SIMONE','10091081');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','CENA','MAURO','10084936');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CENCI','LUIGI','10091126');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CENCIARELLI','STEFANO','10085357');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','CENGARLE','MARCO','10041934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CENGHIALTA','FABIO','10091374');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CENI','CARLO','10061142');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','CENJA','DOMENIKO','10200820');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CENTOMO','ANDREA','10091944');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CENTRONE','PANTALEO','10086563');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CERBONE','NICOLA','10091370');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','CERBONE','DANIELE','10092582');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CERIONI','LUCIANO','10062038');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CERQUOZZI','MAURO','10082523');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CERRI','MARIO','10081381');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CERVELLI','CLAUDIO','10084505');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','CERVO','DAVIDE','10092560');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','CESARO''','ANDREA','10083957');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','CESTELLI','PASQUALE','10061138');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CEVENINI','FABIO','10090469');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CEVENINI','ALBERTO','10078703');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','CHIACCHIERONI','PAOLO','10046145');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CHIACCHIO','CIRO','10091721');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CHIANELLO','PAOLO','10090983');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CHIANESE','EMID','10079964');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','CHIANURA','COSIMO','10083049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CHIAPPETTA','GIOVANNI','10086353');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CHIAPPINI','MAURO','10084376');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','CHIARIELLO','ANTONIO','10092794');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CHIAROLANZA','FRANCESCO','10081558');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CHILLEMI','DANIELE','10090732');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','CHINES','GIUSEPPE','10044615');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','CHINNI','MAURIZIO','10092103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CHIUSANO','ENRICO','10083094');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CIAMBRONE','DANIELE','10090999');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CIAN','PAOLO','10061264');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CIANFARANI','ANTONIO','10062039');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','CIARDULLO','VALERIO','10082653');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','CIARDULLO','FRANCESCO','10091676');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CICALA','MICHELE','10091778');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','CICALESE','CATELLO','10045319');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CICCARDI','MAURIZIO','10082896');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CICCHETTI','GIUSEPPE','10083421');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CICCHETTI','GIUSEPPE','10083422');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CICCIMARRA','GIUSEPPE','10091475');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CICCIMARRA','MATTIA','10091117');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CICCONE','CARMINE','10084038');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','CICERO','MAURO','10092126');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CIFARELLI','FILIPPO','10078897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CILENTI','STEFANO','10091744');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CILIESA','SIMONE','10200882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','CILLARI','VITTORIO','10079479');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CILURZO','GIUSEPPE','10090734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CILURZO','MICHELE','10092685');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','CINA''','ANDREA','10045835');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CINERELLI','MAURO','10061093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','CINO','NICCOLO''','10092226');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','CINQUEPALMI','GIUSEPPE','10085758');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','CINUS','ADRIANO','10091334');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CIOLFI','GIANNI','10084589');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CIONGOLI','NICOLA','10082805');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CIOTOLA','SALVATORE','10086682');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CIPOLLA','LEONARDO','10091454');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','CIPRI','VINCENZO','10088002');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','CIRAOLO','DARIO','10091093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CIRILLO','GIUSEPPE','10086361');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CIRILLO','MAXIM LUCA','10200931');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CITTI','CLAUDIO','10044539');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CIULLO','ANTONIO','10091397');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','CIUMMO','CESARE GIOVANNI','10084471');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CLAPIS','STEFANO','10092079');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CLARIZIA','GIANCARLO','10086627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','CLEMENTE','PAOLO','10081679');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','CLEMENTE','ANGELO','10092650');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CLEMENTINI','LUCIANO','10062041');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','COBUZZI','MARCO','10091654');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','COCCA','UMBERTO','10082994');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COCCIA','NICOLA','10052165');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','COCO','ADRIANO','10091493');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','COCOZZA','ANTONIO','10091177');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','COCOZZA','ANTONIO','10085528');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','CODARIN','MAURIZIO','10046391');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','COIS','VINCENZO','10079242');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','COLA','CLAUDIO','10082677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','COLAFRANCESCHI','ALESSANDRO','10084270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','COLAGRANDE','RAIMONDO','10062207');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','COLAIANNI','LEONARDO','10084818');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','COLANGELO','GIUSEPPE','10045722');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','COLASANTE','MIRKO','10090589');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','COLASANTI','LEONARDO','10090729');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','COLOGNESI','FABIO','10085844');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','COLOMBA','GABRIELLA','10062043');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','COLOMBO','ADELIO','10091199');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','COLOMBO','MARIO','10091840');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','COLOMBO','FABIO','10045071');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','COLONNA','SILVIO','10086419');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','COLORIO','MIRCO','10081414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','COLORU','GIANLUCA','10080539');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','COLOTTA','FRANCO','10200742');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','COLUCCI','BIAGIO','10044131');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','COLUCCI','ALESSIO','10200883');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COLUCCI','ANTONIO','10086653');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','COMISI','ANDREA','10092536');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','COMISI','NICOLA','10043489');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','CONCA','DANIELE','10091505');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CONCAS','LINO','10091585');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','CONCILIO','LUIGI','10092755');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','CONCINA','PAOLO','10092276');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','CONFENTE','REMIGIO','10045014');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CONFORTO','MASSIMO','10044614');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','CONGIU','MARCELLO','10092476');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CONIDI','FRANCESCO','10090101');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CONIGLIARO','FRANCESCO','10081658');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CONNIZZOLI','MASSIMO','10061035');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','CONTE','SALVATORE','10091439');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','CONTE','PASQUALE','10092486');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','CONTE','ANTONIO','10082031');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CONTI','VALTER','10062045');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','CONTI','SALVATORE','10080764');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CONTI','ALESSANDRO','10084803');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','CONTI','GIAMPAOLO','10083728');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CONTICELLI','ROBERTO','10086669');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CONTINI','ROBERTO','10092619');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','CONTU','MATTEO','10086554');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CONTU','GINO','10045459');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','CONVERTINI','MICHELE','10061216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','COONE','SEBASTIANO','10086312');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','COPPOLA','LUCIO','10081259');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','COPPOLA','TOMMASO','10061169');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COPPOLA','RAFFAELE','10083772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','COPPOLA','ALESSIO','10045103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','COPPOLARO','PELLEGRINO','10045724');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','CORAGLIOTTO','LUCA','10083099');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','CORAPI','GIANMARCO','10092782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CORBELLI','SIMON PIETRO','10091507');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','CORBI','GIOVANNI','10083156');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CORBINATI','NICOLO''','10200884');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','CORCIONE','SEBASTIANO','10061044');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','CORDELLA','MARCO','10200782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','CORDONE','GIACOMO','10200821');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CORNAGHI','MATTEO RENATO','10092061');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CORNERO','TOMMASO','10092392');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','CORONA','MIRKO','10092477');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','COROTTO','FABRIZIO','10041536');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CORRADI','STEFANO','10046101');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CORRADO','GIUSEPPE','10091279');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CORRIAS','MICHELE','10092620');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CORSARO','SALVATORE','10092541');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CORSARO','GIUSEPPE','10045654');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','CORSETTI','ANTONIO','10044837');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','CORTESE','RENATO','10090881');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','CORTIS','STEFANO','10200913');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','COSENTINO','GAETANO','10200783');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','COSENTINO','ORAZIO','10092651');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','COSIMI','GIUSEPPE','10062049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','COSSU','PIETRO','10083122');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','COSSU','SANDRO','10084527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','COSTA','ALESSANDRO','10092260');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COSTAGLIOLA','GENNARO','10091394');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','COSTANTINI','ANTONIO','10200890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','COSTANTINI','LUIGI','10085430');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','COSTANTINI','SALVATORE','10084292');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','COSTANTINI','CLAUDIO','10046397');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','COSTANTINO','GIUSEPPE','10085947');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','COSTANZA','PIETRO','10089328');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','COSTANZO','CIRO','10092807');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','COTTONE','SALVATORE','10092784');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','COTTONE','SALVATORE','10046212');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','COTZA','SERGIO','10084035');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','COVELLA','VITO','10085277');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','COVELLA','FRANCESCO','10084995');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','COZZI','DAVIDE','10092542');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','COZZI','NICOLA','10083102');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','COZZI','GIUSEPPE','10092416');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COZZOLINO','FRANCESCO','10089205');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','COZZOLINO','RAFFAELE','10090877');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','CRACCHIOLO','ROBERTO','10092705');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CREMANTE','LUCA','10091633');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CRESCENTI','FABRIZIO','10092163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','CRISAFULLI','GIUSEPPE','10092529');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','CRISCUOLO','CIRO','10082887');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CRISTALLO','GIUSEPPE','10091958');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','CRISTANTE','MARCO','10084420');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CRISTIANO','DOMENICO','10092185');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','CRISTIANO','VINCENZO','10078175');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','CRISTIANO','GIOVANNI','10092066');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','CRIVELLI','LUCA','10061032');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','CROCIANI','MARCO','10091628');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','CROSA MASSARIS','MICHELE','10091738');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','CUBEDDU','GIUSEPPE','10061171');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','CUCCARO','GIORGIO','10091419');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CUCCU','CESARE','10085496');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','CUCCU','GIOVANNI','10045622');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CUENCA CABRERA','WALTER','10092047');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','CULOS','SERGIO','10046250');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','CUNIGLIO','TOMMASO','10086579');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CUNSOLO','SALVATORE','10092028');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','CUNSOLO','EUGENIO CONCETTO','10092732');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','CUPO','GIUSEPPE','10091940');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','CURRO''','MARCO','10089974');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','CUSANNO','GAETANO','10084814');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','CUSIMANO','FRANCESCO','10092891');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','CUTILLO','CARMINE','10079043');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','CUZZUPE''','LORENZO','10062053');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','DA COL','MAURO','10091335');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DA COL','NATALINO','10091336');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DA RE','ALFEO','10083436');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DA RE','LIVIO','10085168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','D''AGOSTINO','IVANO','10045158');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','D''AGOSTINO','ROCCO','10080845');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','DAIDONE','SALVATORE','10092036');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','DAIDONE','DOMENICO','10086378');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DAL BORGO','FABIO','10084282');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','DAL CASON','MAURIZIO','10046072');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','DAL PONT','WALTER','10091337');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','DAL POZZO','ALBERTO','10082647');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','D''ALBA','ADRIANO','10200798');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','D''ALEO','FABIO GIUSEPPE','10045442');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','D''ALESSANDRO','PIETRO','10045621');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','D''ALESSANDRO','AUGUSTO','10062054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DALLA LIBERA','FABIO','10085385');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','DALLA POZZA','GAETANO','10085128');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','DALLA PRIA','ENRICO','10082436');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','DALL''OLIO','STEFANO','10086246');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','D''ALOJA','GIUSEPPE','10091378');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','D''AMARIO','ROBERTO','10085243');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','D''AMATO','PIERGIORGIO','10084277');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','D''AMATO','FRANCESCO','10080455');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','D''AMBROSIO','ALBERTO','10046401');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DAMIANI','CIRO','10085620');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DAMIANI','ALESSANDRO','10092022');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DAMIANO','MICHELE','10045725');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','D''AMICO','ALESSIO','10200871');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','D''AMICO','GIOVANNI','10091956');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','D''AMICO','ROBERTO','10091051');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','D''AMICO','MARCO','10091313');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','DAMMACCO','VITO','10089038');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','D''AMONE','ANDREA','10091449');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','D''AMORE','VALTER','10043998');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','D''ANDREA','CARMINE','10082508');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','D''ANDREA','ROBERTO','10045649');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','D''ANGELO','GIUSEPPE','10092146');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','D''ANGELO','ROSOLINO','10045325');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DANI','DAVIDE GIUSEPPE ETTORE','10092471');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DANIELE','PASQUALE','10045764');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DANIELE','VINCENZO','10082444');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','DANIELE','DANILO','10092878');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','DANILE','DANIELE','10091549');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','D''ANTONIO','SILVANO','10062055');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','DANUSO','FEDERICO','10092100');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','DANZA','PAOLO','10045446');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','D''APICE','FRANCESCO','10091193');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','D''AQUINO','FABIO','10091602');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','D''AQUINO','MASSIMO','10091509');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DARACHILLE','MASSIMILIANO','10091561');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','D''ARASMO','MICHELE','10086350');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','D''ARGENIO','COSIMO','10041743');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DARIO','ROSARIO','10092462');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','D''ARRIGO','DOMENICO','10091917');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','D''ARRIGO','SALVATORE','10045183');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','D''AURIA','GIUSEPPE','10084454');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','DAVI''','SALVATORE','10090285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','DE ANGELIS','MATTEO','10091078');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DE ANGELIS','BERNARDINO','10062056');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DE ANGELIS','SERGIO','10044561');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE ANGIOLETTI','MASSIMO','10083898');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE BELLIS','LUIGI','10045726');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','DE BENI','MASSIMO','10041544');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','DE BENI','GIAMPAOLO','10081760');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DE CARO','ALDO','10085093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','DE CEGLIA','DOMENICO','10091802');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DE CESARIS','MASSIMILIANO','10092177');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','DE CET','MASSIMO','10081343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE CHIARA','GIOVANNI','10082889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE CHIARA','GENNARO','10087598');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DE CHIRICO','GIOACCHINO','10080356');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE CICCO','GENNARO','10045765');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DE COL','EROS','10092613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DE FALCO','MASSIMILIANO','10090959');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','DE FALCO','MARIO ANGELO','10091195');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','DE FEO','GIUSEPPE','10007690');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE FILPO','FRANCESCO','10091999');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE FILPO','ANTONIO','10091998');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE FILPO','FERNANDO','10092257');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DE GENNARO','MAURO','10083762');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','DE GIOIA','VINCENZO','10084868');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE GREGORIO','ANIELLO','10045037');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','DE ICCO','ANDREA','10091450');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE INNOCENTIS','GIOVANNI','10082779');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','DE LIBERALI','GABRIELE','10086734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','DE LUCA','FAUSTO','10084950');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DE LUCA','GABRIELE','10091063');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DE LUCA','BENIAMINO','10085227');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DE LUCA','ANTONIO','10081599');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE LUCA','LUIGI','10082888');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE LUCA','ARMANDO','10045727');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DE LUCA','ALBERICO','10091986');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','DE LUCA','FRANCO','10085450');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','DE MARCHI','RENATO','10083472');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DE MARCO','DAVIDE','10200830');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DE MARTIN','SIMONE','10092503');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','DE NICOLO','STEFANO','10084805');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DE NIGRIS','LEONARDO','10084801');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','DE PALMA','GIACINTO','10085394');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DE PIAZZA','MAURO','10091186');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DE PIERI','SIMONE','10091681');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DE RISO','FABIO','10090364');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE RISO','CIRO','10086085');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE RISO','MATTIA','10085475');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE ROSA','MARIO','10092487');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','DE ROSA','ANTONIO','10092561');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE ROSA','VINCENZO','10046103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','DE SALVE','CLAUDIO','10045345');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DE SANCTIS','FABRIZIO','10061099');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DE SANTIS','GIOVANNA','10092415');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DE SANTIS','VALERIO','10092263');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','DE SARIO','FRANCESCO','10041686');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE SIMONE','DOMENICO','10090992');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DE SIMONE','FABIO','10092652');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE SIMONE','GENNARO','10045766');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE SIMONE','VINCENZO','10086683');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','DE VITO','MAURIZIO','10079236');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DE VIVO','MICHELE','10045767');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DE VIVO','BARTOLOMEO','10045730');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DECARLO','FRANCESCO','10045716');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','DECORTE','DAVIDE','10091393');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DEI TOS','KEOMA','10091625');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','DEIAS','LUIGI OTTAVIO','10085981');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DEIDDA','SALVATORE','10092522');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DEIDDA','GIANCARLO','10045391');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','DEL GAUDIO','ALESSIO','10090912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DEL GRANDE','GIUSEPPE','10062221');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','DEL MONDO','GIANLUCA','10091931');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DEL ROSSO','SERGIO','10092023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DELFINO','PATRIZIA','10062243');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DELGADO FONSECA','ARIEL NECTOR','10092058');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','D''ELIA','MASSIMO','10084532');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DELIGIO','VITO','10084823');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DELLA BELLA','ALESSANDRO','10200872');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','DELLA BERNARDINA','GIANCARLO','10090645');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DELLA ROCCA','ROSARIO','10085069');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','DELL''AGNELLO','ENRICO','10045115');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','DELL''ANNA','SERGIO','10091236');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DELL''ANNO','MICHELE','10091900');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','DELLAVALLE','LUCA','10092275');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DELLE FRATTE','STEFANO','10079371');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','DELLI PAOLI','CLEMENTE','10061010');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DELLI PAOLI','ANTONIO','10092003');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','DEMARINIS','MARIA','10085925');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','DEMICHELI','MARCO','10090527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','DEMICHELIS','CINZIA','10041504');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DEMONTIS','GRAZIANO','10084500');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DEMURTAS','GIULIO','10084499');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','DENTI','DANIELE','10085906');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','DEPLANO','GIAMPAOLO','10084843');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','DEPLANO','CRISTIAN','10092532');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','D''ERRICO','ANGELO SALVATOR','10085087');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DESOGUS','NICOLA','10200849');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','D''ESPOSITO','DOMENICO','10044596');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','DESTRO','PIERANTONIO','10045251');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','DEVASINI','ALBERTO','10090627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','DI BARBORA','YARI','10092287');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DI BARI','CARLO','10062061');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI BARI','ERCOLE','10083830');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DI BENEDETTO','MASSIMO','10081431');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DI BIAGI','ALESSANDRO','10086452');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DI BIASE','ROBERTO ROCCO','10090175');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DI BLASIO','ANTONIO','10080820');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DI CAPRIO','ANTONIO','10092745');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','DI CEGLIE','MICHELE','10089039');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DI CEGLIE','FRANCESCO','10079873');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','DI CROCE','ROBERTO','10061060');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','DI DIO','ANTONINO','10092512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI DOMENICO','MARCO','10090735');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DI DONATO','FRANCESCO','10092048');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DI FALCO','CHRISTIAN','10062063');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','DI FAZIO','SALVATORE','10092917');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI FLUMERI','VINCENZO','10061100');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','DI FRATTA','GILIBERTO','10045613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DI GAUDIO','ANTONIO','10092562');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','DI GIACOMO','DAVIDE','10085656');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DI GIOIA','MARIA RAFFAELA','10062064');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','DI GIULIO','SERGIO','10078477');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DI GREGORIO','GIUSEPPE','10091844');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','DI GREGORIO','LINO EMANUELE','10091249');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','DI GUARDO','GUIDO GIOVANNI','10061154');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI LANDRO','RAFFAELE','10085995');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','DI LASCIO','RAFFAELE','10087401');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI LAURO','MARIO','10045769');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','DI LEMMA','LUDOVICO','10091652');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','DI LEVA','MAURO','10091559');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DI LORETO','ALFREDO','10062065');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DI LUCA','FILIPPO','10086701');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','DI LUCCA','DIEGO','10046411');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','DI MAGGIO','PIETRO','10084632');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DI MAGGIO','CALOGERO','10092701');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI MAIO','PASQUALE','10091144');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','DI MAIO','GIOVANNI','10079525');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI MAMBRO','FRANCESCO','10061101');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DI MARCO','FRANCESCO','10062066');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DI MARCO','VINCENZO NUNZIO','10092008');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','DI MARCO','SERGIO','10084848');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','DI MARIA','ANTONIO M','10078906');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI MARINO','ANTONIO','10081888');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DI MARIO','PATRIZIO','10200743');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI MARZIO','VALTER','10044728');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','DI NARDO','MANUELE','10082463');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DI NATALE','ALESSANDRO','10091340');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','DI PAOLO','MASSIMILIANO','10083250');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','DI PASQUALE','CLAUDIO','10090511');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','DI PIAZZA','ALDO GIUSEPPE','10083025');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','DI PIETRO','ANTONIO','10045657');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI RADO','MARIO','10078720');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','DI RUOCCO','ANTONIO','10091581');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','DI SALVO','IGNAZIO','10061173');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DI SALVO','CRISTIANO','10085520');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DI SARNO','DOMENICO','10046563');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','DI SCIPIO','VINCENZO','10079037');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','DI SECLI''','GIOSUE','10091682');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','DI STEFANO','MICHELE','10045492');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','DI STEFANO','FABRIZIO','10091603');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','DI TELLA','MARCO','10046126');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DI VETTA','FRANCESCO','10092785');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DI VIETRO','SABINO','10080983');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','DI VINCENZO','ALBERICO','10091582');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DI VIZIO','LUIGI','10084770');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','DIAFERIA','CESARE','10089342');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','DIANA','ANTONELLO','10084644');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','DIANA','CLAUDIO','10079015');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','DIANA','ROBERTO','10045472');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','DIBIASE','BARTOLOMEO','10084026');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','D''IGNAZI','LUCIANO','10084422');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','DILENA','MATTEO','10091895');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','DILILLO','NICOLA','10083057');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','D''IMPERIO','GIACOMO','10051807');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','D''IMPERIO','ROBERTO','10061005');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','D''IMPERIO','RITO','10081625');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','D''IMPERIO','GIACOMO','10081623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','DINI','GIANCARLO','10081298');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','DIOGUARDI','TOMMASO','10082767');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DISARMATO','SALVATORE','10086695');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','DISTANTE','COSIMO','10091909');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','DISTRATIS','NICO','10092706');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','DITOMMASO','VITTORIO','10082422');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DOMENICALE','LUCA','10092751');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DONA''','ANDREA','10090741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DONADONI','GERARDO','10088779');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DONATI','MAURO','10062253');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','DONATO','TOMMASO','10089999');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','DONATO','GIOVANNI','10061082');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','DONNADIO','GIOVANNI','10083375');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','DONNO','FRANCESCO','10090867');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','D''ONOFRIO','CARMINE','10089374');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','D''ONOFRIO','PAOLO','10092040');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','DONZETTI','SALVATORE','10083875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','DORI','CESARE','10045138');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','D''ORIANO','TOMMASO','10061009');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DORIGO','ANDREA','10090652');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','D''OSPINA','GIORGIO','10003372');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','DOSSO','ERMANNO','10082671');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','DOVICO','STEFANO','10090677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','DRAGO','ALESSANDRO','10090953');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DROGHINI','SIMONE','10092773');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','DUCA','CESARE','10085521');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','DUCCESCHI','ANDREA','10083176');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','DURANTE','MORENO','10082387');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','DUTTO','LEONARDO','10092772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ECCA','MICHELE','10092563');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','ELISEI','GAETANO','10091511');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','EMILIANI','LUIGI','10082837');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','EMMANUELE','DAMIANO','10081541');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','ENEA','LEONARDO','10092788');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ENZO','LORENZO','10090695');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ERARIO','ALBERTO','10083058');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ERBA','CLAUDIA PATRIZIA','10061063');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ERBA','MASSIMILIANO','10091464');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ERCOLI','ROBERTO','10082433');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ERMACORA','PIERO','10046414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','ERMINI','GIUSEPPE','10045803');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ERRICHIELLO','FRANCESCO','10083770');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','ERRICO','ROBERTO','10087351');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','ERRICO','GIUSEPPE','10092488');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ESPOSITO','ALFONSO','10083997');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','ESPOSITO','NICOLA','10092489');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ESPOSITO','CIRO','10086078');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ESPOSITO','GIUSEPPE','10081593');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ESPOSITO','RICCARDO','10086239');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ESPOSITO','AGOSTINO','10080945');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ESPOSITO','ANTONIO','10085604');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ESSOULIMANI','OUSSAMA','10200868');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','FABBRI','GIANLUCA','10083243');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','FABBRO','ALBERTO','10046415');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','FACHERIS','MASSIMO','10080082');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','FADDA','ALESSIO','10092513');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FADDA','ANTONELLO','10045504');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FAEDDA','NICOLA','10083084');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','FAGOTTI','CLAUDIO','10091227');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','FAITANINI','MARIO','10086418');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','FALABELLA','GIOVANNI','10078230');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','FALABELLA','GIOVANNI','10084622');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FALANGA','NICOLA','10091768');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','FALASCA','UMBERTO','10083221');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FALCO','FRANCESCO FILIP','10083119');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','FALCONE','ANIELLO','10081297');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','FANALI','LUCIANO','10081499');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FANNI','PIER FRANCO','10085210');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','FANTAUZZI','JAMES','10062072');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','FANTIN','ELIA','10091591');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','FARA','ANDREA','10092721');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','FARELLA','FRANCESCO','10079038');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','FARELLA','ROCCO','10045274');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','FARELLA','ANTONIO','10044592');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','FARINA','GIUSEPPE','10080908');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','FARIOLI','ALESSANDRO','10079017');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FARNESI','FABRIZIO','10092253');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FARRIS','RINALDO','10084125');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','FASCINA','GIUSEPPE','10045202');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','FASO','ALFREDO','10080900');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','FASO','DAVIDE','10092575');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FASULO','FRANCESCO','10086684');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','FAVICCHIA','PASQUALE','10045190');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','FAVILLI','LEONARDO','10079897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','FAZZESE','VINCENZO','10092834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','FE''','LUCIANO','10091982');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FEDELE','ALEX','10092790');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','FELICE','GIUSEPPE','10080286');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','FELICE','OTTAVIO','10081820');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','FELICE','FRANCESCO','10084185');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','FELICI','MARCO','10083050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FELLONI','FABRIZIO','10052317');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','FENU','ROBERTO','10084079');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','FERAZZA','FABRIZIO','10045309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','FERILLI','CLAUDIO ANTONIO','10045969');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','FERLAINO','ANTONIO','10090996');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FERONE','GIOVANNI','10086482');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','FERRAIUOLO','PIETRO MARIO','10091996');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','FERRANDINO','ANTONIO','10092686');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','FERRARI','CLAUDIO','10081072');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','FERRARI','PASQUALINO','10090613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','FERRARI','DAVIDE','10044660');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','FERRARI','FABRIZIO','10090346');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FERRARO','ROBERTO','10044901');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','FERRARO','GIANCARLO','10044355');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','FERRARO','MATTEO','10091472');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','FERRAZZI','MARCO FAUSTO','10062069');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FERRELI','GIOVANNI','10082244');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','FERRERO','MASSIMO','10044956');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','FERRETTI','GIACOMO','10061004');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','FERRIGNO','GIANCARLO','10061104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','FERRO','EUGENIO','10200859');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','FERRO','GIUSEPPE','10045957');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','FERRO''','GIUSEPPE','10041637');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','FICHERA','GAETANO  MARCO ROBERTO','10092537');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FICOCIELLO','FRANCO','10078878');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','FIDELIO','PAOLO GIOVANNI','10092687');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','FIDENZI','VINCENZO','10080229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FIECCHI','ANDREA','10092417');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','FIECCHI','UBALDO','10077195');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FILIPPI','ANTONIO','10089330');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','FILIPPIG','VALTER','10046418');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','FILIZZOLA','GENNARO','10079412');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','FILIZZOLO','VINCENZO','10083902');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','FINARDI','DARIO','10090593');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','FINOTELLI','GIANLUCA','10089676');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','FINOTTO','LUCIANO','10044776');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','FIOCCHI','WALTER','10086676');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','FIORE','CONCETTO MARIO','10086916');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','FIORELLI','MATTEO','10091248');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FIORENTINI','ANGELO','10090382');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','FIORETTO','EMANUELE','10090172');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','FIORI','FULVIO','10086485');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FIORIO PLA','ROBERTO','10084784');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','FIORUCCI','CHIARA','10091991');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FIOTTI','LUIGI','10085714');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','FLORA','ANGELO','10079237');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','FLORENZANO','GIOVANNI','10084623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','FLORESTA','SALVATORE','10084492');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','FLORIO','SALVATORE','10044726');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FLORIS','ANDREA','10200916');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FOIS','MICHELE MATTIA','10200852');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','FONSIDITURI','PIETRO','10085874');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','FONTANA','FLAVIO','10062079');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','FONTANA','MICHELE','10081202');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','FONTANA','EUSTACHIO','10090882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FONTANA','CARMINE','10089331');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FONTANELLA','GIUSEPPE','10043515');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','FONTE','MICHELE','10078414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','FORMAGGIO','GIANFRANCO','10082482');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','FORMAGGIO','TONINO','10079425');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','FORMOSA','GENNARO','10084594');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','FORNARELLI','GAETANO','10092830');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','FORNASA','PAOLO','10081791');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','FORNELLI','SERGIO','10085989');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','FORNI','GIULIANO','10078527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','FORTE','MARCO','10085763');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','FORTE','SEBASTIANO','10045076');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','FORTE','LORENZO','10085745');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','FORTE','VINCENZO','10092000');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FORTUNA','GIANCARLO','10083954');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','FORTUNATO','ANTONIO','10090585');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','FORTUNATO','GIUSEPPE','10092653');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','FOSSATI','FABIO','10200932');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','FOSSATI','PAOLO','10061027');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','FRANCESCATO','FRANCESCO','10090342');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','FRANCESCHINI','PIERGIORGIO','10078413');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','FRANCESCONI','MARCO','10081441');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','FRANCESE','MARIO','10091291');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','FRANCOMANO','VINCENZO','10085419');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','FRANGONE','CATALDO','10085359');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','FRAPPAMPINA','ANTONIO','10079740');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','FRASCATI','GIOVANNI','10090618');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','FRASCHILLA','VITO GIUSEPPE','10092747');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','FRATONI','CLAUDIO','10052308');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FRAU','MARCO','10045623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','FRICANO','GIOVANNI','10090987');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','FRIGERI','GIUSEPPE','10061163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','FRIGHI','DANILO','10085991');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','FRIGNANI','DAVIDE','10091819');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','FRISO','MAURO','10090100');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','FROSININI','WILLIAM','10080167');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','FRUNCILLO','SAVERIO','10082867');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','FULGHERI','MARIANO','10092465');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','FULGHERI','MAURIZIO','10045565');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','FULGIERI','FRANCESCO','10092612');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','FUMAGALLI','MARIO','10085907');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','FUMAI','GIOVANNI','10092880');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','FUNARO','FRANCESCO','10090367');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','FURIATI','ANTONIO','10090936');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','FURIOSO','MASSIMILIANO','10092766');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','FURMIGLIERI','ANTONIO','10087032');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','FUSARI','VITO','10089364');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','FUSCO','GIUSEPPE','10088567');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','FUSCO','SALVATORE','10090648');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','FUSCO','SANDRO','10046168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','FUSCO','GIUSEPPE','10044396');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','FUSCO','NICOLA','10092654');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GABBI','FEDERICO','10091054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','GABELLI','CLAUDIO','10079245');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','GADLER','GIANCARLO','10033049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','GAGLIANO','NICOLO''','10079002');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','GAGLIARDI','MAURIZIO','10061041');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','GAGLIARDI','ROSARIO','10091901');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','GAION','MASSIMO','10061051');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GALANTINI','GIANPIETRO','10090366');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GALASSO','VINCENZO','10045099');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','GALDI','ANTONIO','10080198');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','GALEAZZO','PIETRO','10092707');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GALEOTA','GIUSEPPE','10086990');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GALERI','ENRICO','10084260');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','GALETTA','GIUSEPPANGELO','10081948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','GALISE','ARTURO','10003506');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','GALLERANI','GUGLIELMO','10052313');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','GALLO','FRANCESCO GRAZIANO','10090073');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GALLO','GIUSEPPE','10079233');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','GALLO','GIOVANNI','10088090');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','GALLO','ORTENSIO','10079431');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','GALLO','MARIO','10086125');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','GALLO','VITTORIO','10045049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','GALLONZELLI','MAURIZIO','10062083');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GAMBADORO','ALESSANDRO','10084705');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GAMBAIANI','JEAN MARC','10078116');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','GAMBARETTO','LORIS','10091545');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','GAMBINO','FRANCESCO PAOLO','10091044');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','GANGONE','GABRIELE','10091578');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GARAU','SANDRO','10081662');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','GARAU','RENZO','10045852');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GARAU','ANTONELLO','10082742');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','GARAVINI','VINCENZO','10091131');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GARGANO','ALESSANDRO SALVATO','10091536');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','GARGIULO','DANIELE','10092151');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GARGIULO','GAETANO','10086345');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','GAROFALO','CIRO','10082682');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GAROFALO','PAOLO','10091446');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GAROFALO','FABIO','10090736');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','GAROFALO','LUIGI','10090874');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GARRAPA','LUIGI','10092249');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GATTA','MICHELE','10078605');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GAUDINO','VINCENZO PIO','10089630');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GAUDINO','BRUNO','10086686');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GAUDINO','NICOLA','10086339');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GAZZOLI','CLAUDIO ANGELO','10080119');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GAZZOLI','CARLO MARIO','10086351');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GAZZOLI','PIERANGELO','10079505');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','GEDA','FRANCO','10043137');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','GELAO','MAURIZIO','10085216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GELATI','MARINA','10062086');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GELATI','MARCO','10090078');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GEMINIANI','MAURIZIO','10005320');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GENCARELLI','GIUSEPPE','10080452');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GENETIEMPO','ANGELO','10079571');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GENOVESE','GIUSEPPE','10085988');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','GENTILE','MICHELE','10092498');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GENTILE','MASSIMILIANO','10045013');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GENTILE','ANTONIO','10083901');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GENTILE','GUIDO','10092876');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','GENTILI','AURELIO','10080270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GENTILINI','FRANCO','10091281');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','GERARDI','LORENZO','10044624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','GERLI','MARCO','10078271');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GERMANI','GIAMPAOLO GIUSE','10062088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','GERMANO','MAURO','10051569');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','GESMUNDO','GIUSEPPE','10084028');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','GESSA','GIANFRANCO','10082893');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','GESSA','ROBERTO','10084985');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GHEZZI','ALBERTO','10078664');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','GHIANI','ROBERTO','10081905');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','GHIN','CHRISTIAN','10091887');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','GHIOSSO','MARCO','10084635');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','GHIRONI','MICHELE','10200808');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','GHIRONI','FRANCESCO','10086195');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','GHISELLINI','ALESSANDRO','10090338');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GHISLOTTI','ALAN ANDREA','10090235');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIACCHI','LORETO','10082410');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','GIACOMOZZI','ERMANNO','10044564');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GIACOMOZZI','DANIELE','10078968');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','GIACONA','DAVIDE GIUSEPPE','10092774');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIACONA','ALESSANDRO GIOACCHINO','10092530');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','GIACONIA','ANTONINO','10044849');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GIAMMARIA','EMILIO','10084923');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIANFORMAGGIO','DAVIDE','10092136');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIANFRIDDO','SALVATORE','10045261');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GIANGRECO','ALESSANDRO','10062090');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GIANNETTI','GIANCARLO','10080219');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GIANNETTI','DOMENICO','10084096');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','GIANNINI','FRANCESCO','10080439');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','GIANNOTTA','ANDREA','10061114');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','GIANNOTTI','MORENO','10081152');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','GIANONI','ALESSANDRO','10092583');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GIANSANTE','ARMANDO','10086700');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GIARDINI','LUCIO','10081155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','GIATTINO','MAURIZIO','10079819');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GIGI','GIUSEPPE FABIO','10090904');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GIGLI','MARIO','10083083');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GILETTI','PARIDE','10062091');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','GILLI','ROBERTO','10080016');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GINALDI','FERNANDO ANTONI','10041559');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GINOCCHIO','JACOPO','10091537');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GIOIA','FELICE','10044662');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','GIOLA','MARIO','10028247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GIORDANI','CLAUDIO','10090903');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','GIORDANO','DANIELE','10081692');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','GIORDANO','MARIO','10044848');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GIORDANO','FRANCESCO','10091703');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GIORDANO','GIOVANNI','10091677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','GIORDANO','ALFONSO','10078670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GIORGINO','PIETRO','10045917');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GIORGIONE','CLAUDIO','10045731');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','GIOVANETTI','RUGGERO','10005229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GIOVANNETTI','MASSIMO','10079362');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','GIRARDELLO','STEFANO','10092282');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','GIROLAMI','LORENZO','10092688');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','GISONNA','FABIO','10092564');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','GIUDICI','GILBERTO GIUSEPPE','10091314');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIUFFRIDA','SIMONE EUGENIO','10091284');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GIUGNO','GABRIELE','10091673');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','GIULIANI','MATTEO','10090187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GIULIANI','IVANO','10091318');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','GIULIANI','MARCO','10084378');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GIULIANI','GRAZIANO','10086168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GIULIANO','FRANCESCO','10091569');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','GIULIANO','MARCO','10090940');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','GIULIANO','LUCIANO','10062095');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','GIUNTA','ALDO','10061045');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIUNTA','LUIGI','10045458');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GIURATO','ANDREA SILVIO','10092916');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','GIUSTINIANI','SALVATORE','10089182');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','GIZZONIO','MARIO','10079213');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GNAZZO','LUIGI','10091577');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','GNOATO','MASSIMO','10062096');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','GRACCO','TIZIANO IVANO','10091371');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','GRAGNANI','VITTORIO','10045240');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','GRANATO','RICCARDO','10090244');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','GRANATO','FABIO','10091767');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','GRANDINETTI','PINO','10046208');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GRAPPA','FABIO ANTONIO','10046021');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','GRASSANO','PIERO','10052302');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','GRASSO','MARIO','10091872');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GRASSO','CARMELO','10045659');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GRASSO','ANTONINO','10200787');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','GRASSO','MICHELE','10092422');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GRAVINA','DOMENICO','10045320');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','GRAVINA','MICHELE','10091769');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GRAZIANI','STEFANO','10079364');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GRAZIANO GIUNTA','ANTONIO','10045258');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','GRECO','CIRO','10081682');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','GRECO','FRANCESCO','10061284');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','GRECO','GIUSEPPE','10050343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','GRECO','GIUSEPPE','10080670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GRECO','FRANCESCO','10091266');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GRECO','SALVATORE','10084882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','GRECO','PIETRO GIOVANNI','10085390');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','GRECO','DAVIDE','10085132');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GRECO','ANTONIO','10082679');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','GREGORI','ALDO','10081188');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','GREGORI','AUGUSTO','10083676');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','GREGORIO','GIOVANNI','10089525');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','GREWING','STEFANO','10084769');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','GRIECO','DOMENICO','10081834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','GRIFFO','MASSIMILIANO','10061088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','GRIGIONI','MAURO','10082487');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','GRILLO','GIUSEPPE','10091107');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','GRIMALDI','PASQUALE','10091408');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106352','GRIMALDI','CARLA','10092358');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','GRISENDI','GIOVANNI','10082687');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GRISOLIA','DOMENICO','10090126');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','GRITTINI','DAVIDE','10044911');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','GROSSI','LORENZO','10078223');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','GUAGLIANONE','GIUSEPPE','10082962');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','GUAJANA','LUIGI','10200760');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GUALANDI','GIANLUCA','10045082');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','GUALCO','MAURIZIO','10080789');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','GUALDANI','VINCENZO','10062100');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','GUANTARIO','GIUSEPPE','10085816');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','GUARDO','ANTONIO','10092514');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','GUARINO','ANTONIO','10092708');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','GUARRERA','SALVATORE','10092914');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','GUCCIARDI','RENATO','10041682');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','GUCCIONE','ROBERT LUKAS','10061086');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','GUGLIOTTA','CONCETTA','10085230');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','GUIDETTI','IVAN','10062101');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','GUIDO','TOMMASO','10045918');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','HARRI','SKERDI','10091706');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','HAYARI','OMAR','10087852');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','IACHETTA','FABIO','10092538');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','IACONETA','FRANCESCO','10062102');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','IACOPONI','MASSIMILIANO','10086414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','IACOVONE','NICOLA','10086540');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','IACOVOZZI','LIVIO','10081892');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','IACUANIELLO','SALVATORE','10087033');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','IACUELLI','MARCO','10092879');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','IACUITTI','SIMONE','10092435');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','IADANZA','ANGELO','10061028');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','IANNACCONE','MODESTINO','10086225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','IANNACCONE','RAFFAELE','10089286');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','IANNELLI','GIUSEPPE','10085558');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','IANNELLI','MARIO','10091714');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','IANNICIELLO','MICHELE','10084051');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','IANNIELLO','NICOLA','10082781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','IANNINI','PASQUALE','10084876');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','IAQUINTA','FRANCESCO','10086370');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','IAQUINTA','GIUSEPPE','10083364');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','IBBA','SAMUEL','10200809');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','IENNA','VALERIO','10200761');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','ILLUZZI','EMANUELE','10082604');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','IMMERNANO','MASSIMILIANO','10091528');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','IMPALA''','GIUSEPPE','10085974');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','IMPELLIZZERI','DANILO MARIO','10092832');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','IMPERIALE','SAVERIO','10045346');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','IMPICCINI','GABRIELE','10083112');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','INCLAN NOLASCO','JOSE JUAN','10092171');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','INDRIGO','MICHELE','10089997');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','INGIANNI','MASSIMO OSCAR','10084593');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','INGROSSO','ANDREA','10062213');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','INNOCENTE','GIUSEPPE','10091245');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','INNOCENZI','LUCIO','10062104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','INSINGA','ANTONINO','10083215');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','INTELISANO','ROCCO','10087875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','INVERNIZZI','LUCA','10091635');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','INVIDIATO','FRANCO','10083979');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','INZINZOLA','SERGIO','10090886');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','IODICE','ANTONIO','10091415');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','IORFINO','MICHELE','10090357');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','IORFINO','DOMENICO','10045441');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','IORFINO','BRUNO','10092288');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','IORIO','CARMINE','10084055');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','IPPOLITO','ERCOLE','10078030');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','IRDE','GIOVANNI','10088373');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','ISABELLI','DOMENICO','10080172');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','JIRITANO','VINCENZO','10052318');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','JOVIC','JUGOSLAV','10091898');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','KEZIRE','ALI ESSOFA','10090621');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','KHOURY','ELIE','10062105');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','KOCK','ROBERT JOZEF','10062106');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','KUMAR','KULWINDER','10092247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LA BARBERA','GIUSEPPE','10080653');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LA BARBERA','SALVATORE','10086430');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','LA BARCA','FRANCESCO','10091885');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','LA BARCA','ANTONINO','10079948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LA CAMERA','MASSIMILIANO','10092758');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','LA DELFA','DAMIANO','10091094');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','LA GONA','CLAUDIO','10200799');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','LA GRECA','ENZO','10045321');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LA MANNA PATANE''','GIOVANNI','10092539');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','LA MANTIA','GIOACCHINO','10092262');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LA MATTINA','GABRIELE','10091004');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','LA MOTTA','GIOVANNI','10090861');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LA REZZA','ANTONELLO','10062232');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LA ROSA','CARMELO','10092628');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LA SPINA','ANGELO','10200788');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','LABADIA','ROBERTO','10084194');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','LABARI','GERMANO','10083930');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LABATE','VINCENZO','10092246');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','LACALENDOLA','ALESSANDRO','10091388');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','LADISA','FEDELE','10083758');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LAFRONZA','ANTONIO','10085566');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LAFRONZA','FRANCESCO SAVERIO','10091483');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','LAINO','GIOVANNI ANTONI','10085832');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','LALLI','TIZIANO','10089488');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LALOVICH','PIETRO','10091824');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','LAMANNA','GIUSEPPE','10083763');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','LAMANNA','FRANCESCO','10083323');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','LAMANNA','LUCA GIUSEPPE','10092584');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','LAMBIASE','ALFONSO','10083768');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LAMBOGLIA','BIAGIO','10080215');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','LAMINI','BRUNO','10086248');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','LAMONACA','ALESSANDRO','10090730');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','LAMONARCA','GIANROCCO','10092640');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','LAMPIS','MICHELE','10092515');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LAMPIS','SILVIO','10085060');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','LANDI','DAMIANO','10091566');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LANDOLINA','GIOSAFAT','10083249');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','LANDONIO','GABRIELE MARIA','10080380');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LANETTA','GABRIELE','10044836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','LANGELLOTTI','FRANCESCO','10091829');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','LANTERI','SEBASTIANO','10045662');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','LANZA','ELIO CESARE','10092817');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LANZAFAME','ORAZIO','10092918');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','LANZO','MASSIMO','10062107');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LAPEDOTA','MICHELE','10084029');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LARMINI','GERARDO','10084156');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','LAROCCA','MARIO','10200905');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LAUDANI','FRANCESCO','10085664');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','LAURETTA','MARCELLO','10044882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','LAURIA','VICENTE','10082531');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','LAURIA','ANTONIO','10092054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LAZZARO','PIETRO','10092638');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LAZZARO','ROBERTO','10092550');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','LEANZA','ROSARIO','10080766');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LECIS','ALBERTO','10092523');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LEDDA','NICOLA','10092216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','LEMBO','FRANCESCO','10092576');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LEMME','MAURIZIO','10085522');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','LEO','GIOVANNI','10084785');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','LEO','ANTONIO','10041694');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','LEO','ANDREA','10092504');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','LEO','PIERINO','10079715');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','LEO','TOMMASO','10041665');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','LEONE','ROCCO','10086666');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LEONE','ROBERTO','10080683');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','LEONE','ANTONIO','10091964');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','LEONE','GIUSEPPE','10090380');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LEONI','ENRICO','10085085');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','LEOPARDI','FELICE','10092733');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','LEOTTA','CONCETTO','10052123');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','L''EPISCOPO','SANTO','10092543');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','LEPPA','ROSARIO LEONARDO','10046084');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','LEVORATI','FABIO','10091772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','LI VIGNI','ALBERTO','10092156');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LIANZA','RENZO','10051530');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LIBANO','ANTONIO','10091821');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LIBERATI','FRANCESCO','10090465');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','LIBERATORE','PAOLO','10045041');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LIBERTI','VINCENZO','10092925');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','LICARI','DANIELE','10061048');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','LICARI','SIMONE','10200841');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','LICATA','LUCIO GERMANO','10088590');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','LICATA RICOTTONE','CALOGERO GES├Ö','10092748');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','LICAUSI','MARCO FABIO','10090210');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LICCIARDI','SERGIO','10092152');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','LIFONSO','GIUSEPPE','10091233');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LILLI','MAURIZIO','10092204');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','LINCIANO','ANTONELLO','10091234');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','LIOTTA','EPIFANIO','10092837');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','LIRI','GIACOMO','10076994');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LISCIANDRELLI','MASSIMILIANO','10079360');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LISCIANDRELLO','VINCENZO','10091963');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','LIUZZA','SALVATORE','10092261');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LIUZZI','SALVATORE','10081937');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','LO BASSO','FRANCESCO','10085778');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LO BIUNDO','NICOLA  LEONARDO','10092467');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LO COCO','ANTONINO','10085143');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','LO GATTO','MARIO','10090997');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LO GIOCO','ANTONINO','10092007');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','LO GIUDICE','GIUSEPPE','10085512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LO MONTE','CRISTIAN','10092280');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LO MONTE','SALVATORE','10081913');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','LO PICCOLO','SANDRO','10092734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','LO SICCO','ANTONIO','10090259');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LO STORTO','MICHELE','10081720');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','LOBALSAMO','DOMENICO','10078940');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','LOCANTO','ARIANO','10078249');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','LODI','MAURO','10090974');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LOGIAS','CARMELO','10084304');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LOI','MARCELLO','10045428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LOI','NICOLO''','10085575');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LOIACONO','FRANCESCO','10084257');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','LOIZZI','NICOLA','10061272');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LOLLI','LEONARDO','10085343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','LOLLINO','VITO NICOLA','10078899');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','LOMASTO','ANTONIO','10092490');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','LOMASTO','STEFANO','10091718');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','LOMBARDI','DAVIDE','10089895');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LOMBARDI','ADELIO','10092608');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','LOMBARDINI','STEFANO','10044430');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','LOMBARDO','SALVATORE','10092065');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','LOMBARDO','CARMELO','10045457');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','LOMBARDO','ALESSANDRO','10091832');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','LOMBARDO','FRANCESCO','10061286');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LOMBRONI','MASSIMO','10062109');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','LOMUSCIO','NICOLA','10045104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LONGHI','ANDREA','10091760');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','LONGHIN','FEDERICO','10091649');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','LONGHITANO','FRANCESCO','10092544');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LONGO','ROBERTO','10084476');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','LONGO','GENNARO','10092831');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','LONGO','FABRIZIO','10091538');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','LONGO','EMILIO','10046043');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','LONGOBARDI','SALVATORE','10091086');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','LOPEZ','GIOVANNI','10092124');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','LOPEZ','FRANCESCO','10061197');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LOPORCHIO','LUIGI','10083947');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','LOPRESTI','ANTONINO','10090778');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LORE''','DOMENICO','10078900');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LORRAI','FRANCESCO','10082251');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','LORRAI','ROBERTO','10092516');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','LORUSSO','NICOLA','10081429');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LOSA','GABRIELE','10061070');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LOSACCO','DOMENICO','10084811');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','LOSITO','NICOLA','10091674');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','LOVALLO','DONATO','10084497');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LUBERTI','LUCA','10062216');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','LUBRANO','GIOACCHINO','10082782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LUCARELLI','GIUSEPPE','10084963');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','LUCARINI','FABIO','10045206');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','LUCARINI','STEFANO','10091491');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','LUCCHINI','GIANFRANCO','10061156');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LUCCIARINI','FABRIZIO','10091992');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LUCIANI','PAOLO','10091915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','LUCIANI','GIUSEPPE','10084077');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','LUCIANO','ANNUNZIATA','10062110');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','LUCINI','DANIELE','10062252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','LUMINI','ANDREA','10085760');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','LUNGHEU','PAOLO','10079290');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','LUONGO','LUCIO','10076378');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','LUPARELLI','ALESSIO','10090868');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','LUPETTO','ANTONIO','10089373');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','LUPO','CHRISTIAN','10091119');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','LUPPINO','VINCENZO','10091627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','LUSICINI','DANTE','10090512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','LUSITO','VITANTONIO','10084806');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MACCARRONE','SALVATORE','10092524');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','MACCARRONE','FEDERICO','10092172');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','MACCARRONE','SANDRO','10091962');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MACCHIONE','PAOLO','10092497');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MACCHIONE','CARLO','10092444');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','MACCIONI','ANDREA','10083225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','MACERI','GINO','10083285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','MACI','STEFANO','10087885');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','MACRI''','FRANCESCO','10079282');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MACRI''','VINCENZO','10090104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MADONNA','VINCENZO','10082103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MADONNA','BIAGIO','10092819');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MADRIGRANO','MASSIMO FRANCESCO','10091386');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MAESTRI','ROBERTO','10085679');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','MAFFE''','DANIELE','10091807');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','MAFFEI','LUIGI','10082495');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MAGAGNINI','GIUSEPPE','10045124');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MAGAGNINO','ROBERTO ROCCO','10045919');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MAGGIORE','MARIO','10044616');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','MAGGIORE','MAURIZIO','10092889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MAGGIORE','COSIMO','10091817');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MAGGIOTTI','PAOLO','10090949');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MAGISTRI','BENIAMINO','10092656');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MAGLIE','VINCENZO','10091282');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','MAGLIOCCO','GIUSEPPE PIO','10092818');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MAGNANINI','ALBERTO','10089056');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','MAGNOLATO','MASSIMO','10045833');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MAIO','MARCO','10091567');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MAIONE','ANDREA MARCO','10092517');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MAIONE','CIRO','10092285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MAIONE','SALVATORE','10091050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MAIONE','VINCENZO','10091052');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MAIONE','VINCENZO','10078863');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MAIORANO','NICOLA','10045566');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','MALAFARINA','FRANCESCO','10092105');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MALAPONTI','CALOGERO','10092545');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','MALIZIA','CRISTIAN','10091945');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','MALIZIA','EMANUEL','10091946');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MALIZIA','LAMBERTO','10083909');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MANCA','DAVIDE','10200812');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MANCIN','EGIDIO','10090110');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MANCINI','STEFANO','10062115');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MANCINI','CIRO','10062114');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MANCINI','RICCARDO','10080271');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MANCO','VALERIA','10088545');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MANCO','GIANPIERO','10081475');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','MANCUSO','TERESA','10090428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','MANCUSO','ANGELO','10091595');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MANDIA','LUIGI','10081700');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','MANDOLINO','NICOLA','10080979');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','MANFREDELLI','GIUSEPPE','10083786');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','MANFREDI','LIDIO','10045343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MANGANO','GIUSEPPE','10044789');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MANGANO','ALFIO','10092735');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MANGINI','LUCIANO','10091839');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MANGO','LUCIANO','10086710');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MANNAI','ANDREA','10085497');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MANNARINO','SETTIMIO','10084986');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MANOSPERTI','MARCO','10090072');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MANSUETO','MARIO','10080513');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MANTARRO','ANDREA','10092709');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MANTI','CLAUDIO','10091540');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','MANTOAN','RENZO','10090674');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MANTOVANI','ROCCO','10091208');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','MANTOVANI','MIRCO','10045068');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MANUELE','ANTONINO','10078920');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MANZO','GIOVANNI','10092710');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MANZO','GIUSEPPE','10087485');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MARAIO','ANTONIO','10078599');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MARANO','FABIO','10092775');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MARANO','BRUNO','10091225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','MARASCO','FRANCESCO','10090982');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MARASCO','ANGELO','10091597');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MARCELLI','ORLANDO','10085028');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MARCELLI','PAOLO ROSARIO','10083218');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MARCELLO','DARIO RICCARDO','10091539');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MARCHESANI','ANDREA','10045859');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MARCHESANI','DIEGO','10045222');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MARCHESE','GIANFRANCO','10061124');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MARCHESE','ANDREA','10045974');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','MARCHESE','LEONARDO','10086298');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MARCHESIN','MAURO CIRILLO','10044629');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MARCHESOTTI','MARIO','10044368');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MARCHETTA','FABRIZIO','10092410');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MARCHETTO','GIUSVA','10088828');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','MARCOLINI','DANILO','10092778');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MARCOLONGO','STEFANO','10085292');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MARCOMENI','PAOLO','10090901');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MARGILIO','VITO','10091428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','MARI','DOMENICO','10088889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','MARIANACCI','NICOLA','10091864');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MARIANI','ARISTIDE','10084773');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MARIGHELLA','DARIO','10061046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MARIGLIANO','ANIELLO','10088526');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','MARINELLI','GIOVANNI ENRICO','10045074');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MARINI','FEDERICO','10062117');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MARINI','GIUSEPPE','10045734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','MARINO','GIUSEPPE','10084627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MARINO','LUCIANO','10086083');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MARINO','GIUSEPPE','10085062');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MARINO','SALVATORE','10078250');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','MARINO','LUIGI DOMENICO','10088221');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MARIOTTI','GIUSEPPE','10091068');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MARITATO','CARMINE','10078459');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','MARMO','CORRADO','10084912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MARONGIU','ROBERTO','10090817');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MARONGIU','ALESSANDRO','10085270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MARONGIU','MAURIZIO','10045611');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MAROTTA','FRANCESCO','10092719');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MAROTTA','DOMENICO','10082030');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MAROTTA','NICOLO''','10092781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MAROTTA','FILIPPO','10084246');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MAROZZI','MARCO','10084925');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MARRA','MAURIZIO ALBERTO','10092730');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MARRO','VINCENZO','10045735');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MARROCCO','DAMIANO','10062205');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MARROCU','SALVATORE','10092518');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','MARSANO','IVANO','10085582');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MARSELLA','ROBERTO','10084948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','MARSILIO','FRANCO','10045281');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MARTELLO','PATRIZIO','10044669');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MARTINELLI','SEVERINO','10044347');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MARTINELLI','FRANCESCO','10085934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MARTINI','ANDREA GIUSEPPE','10084334');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','MARTINO','ANTONIO','10091017');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MARTINO','ORAZIO','10046116');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','MARTIRE','GIOVANNI','10091018');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MARTIRE BEDOSTRI','ALESSANDRO','10091212');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MARTIS','SANDRO','10080358');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MARTORANA','ANGELO','10045329');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MARULLO','EMANUELE','10092577');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MARULLO','GIOVANNI','10045590');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MARZANO','MASSIMO','10044897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MASCIA','ALBERTO','10045844');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MASINI','ROBERTO','10052330');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MASO','FABIO','10092614');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','MASONI','ROBERTO','10080905');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','MASSA','SALVATORE','10092165');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','MASSA','ANDREA','10200824');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MASSA','PIER LUIGI','10045920');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','MASSARO','GIUSEPPE','10080332');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MASSARO','GIUSEPPE','10092565');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MASSIMILIANO','TONINO','10085919');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MASSIMINO','SALVATORE','10091262');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MASTRANTUONO','MAURO','10045139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MASTROCOLA','RINO','10062120');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MASTROCOLA','NICOLINO','10045736');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','MASTROIANNI','MARIO','10082035');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MASTROLIA','ANGELO','10086758');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MASTRONARDO','VITO','10083192');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','MASTROROCCO','MICHELE','10078939');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','MATRANGA','GAETANO','10045437');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','MATTA','GIOVANNI FABIO','10085908');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','MATTEOTTI','CRISTIAN','10092463');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MATTERA','CRISTIAN','10090634');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MATTOGNO','ALDO','10078882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MAULUCCI','FRANCESCO','10044812');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MAURIZI','LIVIO','10062229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MAVILIA','FRANCESCO','10090865');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MAZZA','EMILIANO','10092645');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MAZZA','DINO ROBERTO','10090222');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MAZZA','VINCENZO','10200833');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MAZZEI','ANTONIO','10092491');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106381','MAZZEI','GRAZIANO','10092881');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','MAZZITELLI','MASSIMO','10091914');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MAZZOCCHI','MARCO','10061181');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','MAZZOLA','DAVID','10061199');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MAZZOLA','VINCENZO','10092711');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MAZZONE','DANIELE','10091367');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','ME','LUCIANO','10084751');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MEDEA','STEFANO','10044353');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MEI','FABIO','10061106');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MELAS','MARCO','10085169');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','MELCHIONDA','LEONARDO','10043946');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','MELILLO','MICHELE','10091140');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','MELIS','ALESSANDRO','10200919');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MELIS','ANGELO GIUSEPPE','10061107');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MELLINO','LUIGI','10079973');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MELONI','GIULIANO','10200775');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MEMOLI','RAFFAELE','10083988');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','MENAZZI','IVAN','10080453');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','MENCHISE','ROCCO','10045664');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MENCUCCI','MASSIMO','10084063');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MENDICINO','GINO','10090980');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MENEGATTI','ALESSANDRO','10089230');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MENGARELLI','MARCO','10090905');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','MENGHIN','SILVANO','10044520');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MENGONI','ALESSANDRO','10092411');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MENICHELLI','ALESSANDRO','10084924');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MENNITTI','RAFFAELE','10092795');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MEO','GENNARO','10082840');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MEOLI','ANTONIO FRANCES','10045737');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MERAFINA','MICHELE','10085029');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MERATI','PAOLO','10005317');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MERCURI','RICCARDO','10200873');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MERENDI','MARCO','10086179');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MEREU','ROBERTO','10092468');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MERLUZZI','MARIO','10045288');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MESSERE','DANIELE','10091265');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','MESSERI','ANDREA','10086325');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MESSINA','GIUSEPPE','10062235');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MESSINEO','ANDREA','10092281');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','MESSUTI','NICOLA','10084619');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MEVOLA','ROBERTO','10086717');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MEZZATESTA','DOMENICO','10045569');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','MIAN','LUCA','10090412');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','MICALIZZI','FABIO ANTONIO','10091288');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','MICARELLI','RINO','10081459');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','MICCOLI','ORONZO','10085653');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MICELI','FRANCESCO','10052172');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MICELI','GIUSEPPE','10083277');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MICELLI','LUIGI','10045953');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','MICHELAZZO','DAVIDE','10090678');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MICHELI','ROBERTO','10084300');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MICOTTI','MARCO VITTORIO','10092060');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','MIGLIORE','VINCENZO','10045665');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MIGLIORINO','NICOLA','10200869');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','MIGNANO','SALVATORE','10079627');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MIGNANTI','FRANCO','10061108');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','MIGNOLA','GENNARO','10091413');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MILANO','GIANLUCA','10091015');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MILO','GIOVANNI','10084075');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MILONE','GIOVANNI','10091303');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MINENNA','PASQUALE','10078775');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MINERVINI','GIUSEPPE','10091988');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','MINERVINI','FRANCESCO','10091470');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','MINGHETTI','ANGELO','10084592');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','MINI''','MICHELANGELO','10081685');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','MININ','GABRIELE','10200897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MINNA','MASSIMO','10083684');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MIOTTI','GIOVANNA','10085228');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MIRABELLI','ALESSANDRO','10062127');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','MIRABELLI','NICOLA','10090981');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','MIRACCO','ANGELO','10041759');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','MIRAGLIA','SALVATORE','10085377');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MIRAGLIA','GIUSEPPE','10092009');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MIRANTE','RICCARDO','10091942');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MIRARCHI','MASSIMILIANO','10092610');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','MISIANO','GIOVANNI','10061011');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','MISSIO','DAVIDE','10045112');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MISSIO','ALDO','10043197');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MITSCHEUNIG','GIORGIO','10091421');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','MIULLI','MARCO','10085523');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MO','ROBERTO','10061050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MOCCERO','GIOVANNI','10082535');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','MOCHI','ROBERTO','10045243');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','MODICA','GIUSEPPE','10078080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','MODICA','FILIPPO','10078079');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','MOFFA','DOMENICO','10080429');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MOIOLI','SERGIO','10092284');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','MOJOLI','GAETANO','10052229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MOLINARI','LUCA','10084178');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','MOLINARI','PAOLO','10080181');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','MOLINARO','RANIERI','10046456');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MOLINARO','MICHELE','10080226');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MOLINO','ALDO','10045322');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MOLLICHELLA','MARCO','10062225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','MONDELLI','MASSIMO','10083163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MONETTI','GIOVANNI','10089380');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','MONGELLI','ANDREA','10083881');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MONNI','MARCO','10085821');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MONTAGNA','ALESSANDRO','10092657');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','MONTAGNER','ANDREA','10046457');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','MONTALDO','RICCARDO','10080602');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MONTALTO','MARCELLO','10084932');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MONTANARELLA','MIRKO','10062128');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','MONTANARELLI','DONATO','10081793');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MONTANARO','LEONARDO','10045465');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','MONTANINI','DARIO','10089696');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MONTEFUSCO','AUGUSTO','10083080');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','MONTENEGRO','GIUSEPPE','10083299');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MONTESANO','ALDO ROCCO','10086145');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','MONTESANTO','SALVATORE','10045087');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MONTESANTO','VITO','10045088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MONTEVECCHI','MARCO','10083093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MONTI','ALESSANDRO','10092814');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','MONTI','GUIDO','10062129');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','MONTICELLO','JACOPO','10200874');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MONTICO','PIETRO','10085782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MONTINARO','LUIGI','10045912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','MONTIS','FABRIZIO','10089344');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','MONTONERI','SEBASTIANO','10090466');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','MORBIOLI','PAOLO','10079178');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','MORELLI','FRANCESCO','10051810');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','MORELLI','STEFANO','10079492');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MORETTI','MARCO','10090623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','MORETTO','ROBERTO','10078486');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MORO','LORIS UMBERTO','10091934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MORO','MAURIZIO','10082263');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','MORONI','ANTONIO','10085524');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','MORRESI','LUIGI','10061183');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','MORRONE','ALESSANDRO','10091525');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','MORVELI MENDIVIL','TOMAS GUILLERMO','10092816');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','MOSCA','DANIELE','10078425');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','MOSCARDI','ROBERTO','10090656');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MOSCARIELLO','AURELIO','10085905');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','MOSCATELLI','PIETRO','10045157');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','MOSCELLI','PIETRO','10083417');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','MOSCELLI','GIOVANNI','10083418');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MOTTA','ANTONIO','10092629');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','MOTTA','ANDREA','10092519');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MOTTA','MICHELE','10091494');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','MOTTI','NICOLA','10052309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MOZZETTI','MARCO','10092805');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','MOZZILLO','MARIO','10082783');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','MUDU','GIULIANO','10084980');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MUGGIRONI','MARIO','10200850');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','MUNEGHINA','MAURIZIO','10045851');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MURA','SALVATORE','10091247');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','MURA','BERNARDINO','10085213');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','MURA','MAURIZIO','10092492');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','MURACA','FILIPPO','10092208');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','MURATORE','MICHELANGELO','10061085');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MURCIA VALENCIA','IVAN JERONIMO','10092218');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','MURGIA','IGNAZIO','10045512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','MURRU','SIMONE','10200813');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','MURTAS','GIOVANNI','10043206');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','MUSTONE','ANTONIO','10045934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','MUSU','ALESSANDRO','10089878');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','MUZZANI','FILIPPO','10091613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','MUZZARELLI','MARCO','10091055');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','MY','SALVATORE','10046022');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','NADALIN','GABRIELE','10091264');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','NAITANA','PAOLO','10080795');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','NALDI','MAURIZIO','10083296');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','NAMIA','ANTONINO','10092470');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','NAPOLI','LIBORIO','10200741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','NAPOLI','GIUSEPPE','10086265');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','NAPOLITANO','NICOLANGELO','10045387');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','NAPPO','SALVATORE','10086850');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','NARDELLI','PIERLUIGI','10062134');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','NARDI','GIAN LUCA','10083017');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','NARDIN','OSCAR','10200826');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','NASELLI','FEDERICO','10092423');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','NASILLO','ANTONIO','10061053');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','NATALINI','GIOVANNI','10061242');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','NATALINI','GIANPAOLO','10051834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','NATOLI','ANTONINO','10044741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','NAVARRA','PAOLO','10085139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','NAVARRA','ANTONIO','10044166');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','NAZZARO','ANTONIO','10045381');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','NAZZARO','ANGELO','10045105');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','NECCIA','ROBERTO','10085834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','NEGRI BEVILACQUA','ANDREA GIOVANNI','10061026');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','NEGRO','PIETRO','10085246');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','NICASTRO','LUIGI','10092752');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','NICASTRO','MAURIZIO','10091924');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','NICCOLAINI','SANDRO','10052085');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','NICO','ALFREDO ROCCO','10091774');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','NICOLAI','GABRIELE','10062137');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','NICOLETTI','STEFANO','10062219');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','NICOTERA','FRANCESCO','10090956');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','NICOTRA','ANGELO MARIA','10091925');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','NIFFOI','DAVIDE','10092690');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','NIGRO','ALDO','10051888');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','NIRCHIO','NICOLA','10091951');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','NISCOSI','MARCO','10090252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','NISI','FABIO','10045831');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','NOBILIONE','MARCO','10062138');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','NOCCI','STEFANO','10085244');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','NONINO','ENEA','10092499');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','NOTARI','ALESSANDRO','10089711');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','NOTARIANNI','GESUALDO','10079226');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','NOTARIANNI','ENZO','10078528');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','NOTARIANNI','VINCENZO','10078047');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','NOTARNICOLA','ANGELO','10083415');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','NOVELLI','FABIO','10084301');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','NOVELLI','FABRIZIO','10084465');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','NOVELLI','GIUSEPPE','10082062');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','NOVELLO','ROBERTO','10046461');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','NUGHES','GIOVANNI MARIA','10084314');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','NUTRICATI','ANTONIO','10091983');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','NUTRICATI','CLAUDIO','10085148');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','NUZZOLESE','GIUSEPPE','10092133');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','OCCHINERI','RAFFAELE','10091638');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','ODDO','MARCO','10092273');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ODDO','DANIELE','10091893');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','OGGIANO','ROBERTO','10045624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','OLDANI','ETTORE MARIA','10061202');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','OLDANI','GIUSEPPE','10085046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','OLGIATI','MAURIZIO','10091616');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','OLIBONI','ROBERTO','10041405');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','OLIVA','DARIO','10044788');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','OLIVA FILIZZOLA','MARIO DOMINGOS','10080239');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','OLIVERI','ANGELO','10092639');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','OLIVETTO','PIERLUIGI','10085025');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','OLIVIERO','VINCENZO','10091734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','OLLA','BASILIO','10081824');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','ONETO','ANTONINO','10044551');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ORAFO','RITA','10041750');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ORDANO','RAIMONDO','10092446');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ORLACCHIO','COSIMO','10045738');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ORLANDI','LEONARDO','10091825');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ORLANDI','ENZO','10085661');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ORLANDO','ELIA','10091929');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ORLANDO','EMANUELE','10092019');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','ORLANDO','GIUSEPPE','10084714');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','ORLANDO','FRANCO','10089376');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','ORRU''','ERASMO','10052084');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','ORRU''','MAURILIO','10092602');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ORRU''','GIOELE','10200776');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','ORSI','ROMANO','10081130');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','ORSI','DANILO','10092585');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ORSINI','DANILO','10062143');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','ORSINI','BRUNO','10085341');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ORSO','LUCA','10091427');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','ORTENZIO','GAETANO','10091115');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','ORTENZIO','GIUSEPPE','10080880');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','ORTU','ALBERTO','10078922');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ORTU','PIER PAOLO','10082269');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','OSIMANI','MARCO','10085147');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','OSPIZIO','ENRICO','10200867');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','OSSOLA','GIOVANNI','10051938');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','OTTELLI','ALBERTO','10083946');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','OTTOBRE','ENZO','10092093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','OTTONE','VINCENZO','10082533');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','PACE','GIUSEPPE','10090356');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PACELLA','GIUSEPPE','10091827');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PADOVAN','MAURO','10061036');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','PADULA','NICOLA','10091722');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','PAESANTE','PIERPAOLO','10090761');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PAGANA','SILVIO','10092546');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','PAGANI','SIMONE','10200875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','PAGANI','GIANCARLO','10090626');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','PAGANO','VINCENZO','10092190');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','PAGANO','NUNZIO','10083761');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','PAGIN','ANDREA','10092115');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PAGLIARINI','AUGUSTO','10081252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','PAGLIARONI','ROBERTO','10080860');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PAGNIN','GIOVANNI','10062145');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PAGNUTTI','DANILO','10092412');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PAIANO','RAFFAELE','10091639');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','PAIAR','MIRKO','10091558');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PALA','PIER GIUSEPPE','10084501');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','PALA','SALVATORE','10085188');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PALATTELLA','PIETRO','10081469');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PALAZZO','ANDREA','10200745');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PALAZZOLO','SALVATORE','10086867');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','PALAZZOLO','PIERLUCA','10092535');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','PALENZONA','FEDERICO','10200827');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PALLADINI','DANIELE','10091623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','PALLADINO','GIOVANNI','10085452');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PALLANTE','ALDO','10062146');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PALMA','MARCO','10062147');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','PALMA','LUCIO','10092753');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PALMA','ARMANDO','10091251');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PALMA ESPOSITO','CIRO','10088620');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PALMAS','ANGELO','10090672');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PALMENTIERI','NUNZIA','10092703');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PALMERI','GIANLUCA MICHELE','10090229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PALMIERI','GIOVANNI','10083212');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','PALMIERI','ERNESTO','10081203');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','PALMIGIANO','FRANCESCO','10092815');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PALOSSI','ALESSANDRO','10091984');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','PALUMBO','EUGENIO','10061092');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PALUMBO','MARCO','10046142');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PALUMBO','ROBERTO','10083378');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','PAMPILLONIA','PIETRO','10092836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PANARIELLO','NICOLA','10084640');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','PANARITI','MICHELE','10081135');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','PANARO','FRANCESCO','10091636');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','PANI','GUIDO','10045941');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','PANITTI','SERGIO','10079261');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PANITTI','MICHAEL','10092419');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','PANSECCO','FAUSTO','10080925');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','PAOLETTI','SERGIO','10078957');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','PAOLI','CORRADO','10044091');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PAOLILLO','ALEXANDRO','10091426');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PAOLINI','LUIGI','10044864');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PAOLINI','SABATINO','10085609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','PAOLINO','MARCELLO','10086299');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PAONE','MAURO','10046571');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PAPA','PIETRO','10087789');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','PAPA','LEONARDO','10005261');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','PAPALEO','ANTONIO','10085504');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','PAPARELLA','DOMENICO','10079916');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PAPARELLA','GIANDONATO','10080980');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PAPARELLA','MICHELE','10084821');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','PAPPALARDO','GIUSEPPE','10090923');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PAPPALARDO','MARCO','10092164');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PAPPALARDO','MAURIZIO','10062149');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PARATORE','ANTONIO','10079807');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PARENTE','SIMONE','10091868');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','PARINI','LUCA','10200933');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PARISI','NICOLA MARCELLO','10091993');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','PARISI','FRANCESCO','10090193');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PARISI','FRANCESCO','10041641');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PARISI','GASPARE','10092740');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PARLANTI','ALESSANDRO','10092413');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','PARRINI','FRANCO','10085828');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PASCARELLA','ALFONSO','10080809');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','PASCHETTA','GRAZIANO','10080397');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PASCUZZI','EMILIO','10052187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PASCUZZI','PASQUALE','10079111');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PASCUZZI','MARIO','10091019');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','PASCUZZI','ALDO LORENZO','10080568');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PASSACANTILLI','MARCELLO','10090897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PASSARO','GIUSEPPE','10082703');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PASSERO','SALVATORE','10044891');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','PASSETTI','FABRIZIO','10081924');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PASSI','PAOLO','10084469');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PASTRELLO','FAUSTO','10052193');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PASTURA','PASQUALE','10084523');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PATARO','FRANCO','10086775');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PATARO','GIUSEPPE','10088556');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PATRIGNANI','GIUSEPPE','10078725');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PATRIZI','PAOLO','10081010');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','PATRONE','ANGELO','10086359');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','PATRUNO','GIUSEPPE','10080357');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PATTI','SALVATORE','10089377');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PATUANO','ANGELO','10041631');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','PAULETTI','CLAUDIO','10091343');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','PAUTASSO','GIOVANNI FABIO','10045162');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PAVANELLO','DANIELE','10084691');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PECCHIOLI','ALESSANDRO','10086231');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','PECORARO','ANTONIO','10092256');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PEDDITZI','ROBERTO','10081752');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PEDRAZZOLI','MASSIMILIANO','10092272');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','PEDRON','FABIO','10091344');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','PEIRONE','ENRICO','10051759');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PELAGAGGI','FABIO','10090588');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106352','PELECCA','ALESSANDRO','10092420');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','PELLEGATTA','GIANLUCA','10092586');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PELLEGATTA','UBALDO','10061132');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','PELLEGRINO','FABIO','10091741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','PELLEGRINO','GIOVANNI','10091120');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106381','PELLEGRINO','MATTEO','10082831');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PELLERITO','FABIO','10090753');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PELUCCHI','LOREDANA','10091229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','PELUSO','NATALINO','10083915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PELUSO','LUIGI GIANCARLO','10079340');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PENNELLA','DANIELE','10092187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PENSATO','MICHELE','10091968');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PENTELLA','DOMENICO','10092029');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','PEPE','NICOLA','10089946');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','PEPE','DAVIDE','10092743');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PEPE','ARMANDO','10045739');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PERALE','ALESSIO','10091542');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PERALE','ENOS','10091976');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PERCIBALLI','FRANCESCO','10081457');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','PERGOLA','ALESSANDRO','10092658');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','PERILLI','GIOVANNI','10088890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PERILLI','SERGIO','10084283');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','PERNA','ANTONIO','10081040');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PERNA','CIRO','10078986');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PERNAZZA','ANDREA','10078519');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PERNELLI','GIOVANNI','10086689');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PERNICE','LEONARDO','10092173');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PERRA','PAOLO','10084541');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','PERRELLA','FRANCO','10082576');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','PERRINI','FRANCESCO','10090942');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PERRINI','ROBERTO','10091823');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','PERROTTA','MASSIMO','10086362');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','PERUGIA','MARIO','10091531');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','PERUZZO','DAVIDE','10091889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','PESCI','RICCARDO','10091315');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','PESCI','GIANNI','10052178');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','PESENTI','ERNESTO','10080938');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','PETRACCARO','SPECIOSO LUCA','10092738');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PETRACHI','NICETA ANTONIO','10091241');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PETRACHI','IVANO','10046051');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','PETRIGLIERI','CARMELO','10045670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PETRIGLIERI','CESARE','10092424');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PETRIGLIERI','FRANCESCO','10092425');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','PETRIGLIERI','ALESSANDRO','10045669');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','PETRIGLIERI','CLAUDIO','10045671');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','PETRINO','ALESSANDRO','10092426');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PETRISANO','ANTONIO','10088542');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','PETRONI','FRANCESCO','10045404');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','PETROSINO','MARIO','10091580');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','PETRUCCI','FABIO ENZO','10090336');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','PEYRETTI','PAOLO','10092230');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PEZZA','FELICE ANTONIO','10083406');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PEZZELLA','ANTONIO','10084046');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PEZZILLO','ANIELLO','10086214');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PEZZOTTI','PIERGIORGIO','10085325');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PIANA','ROBERTO','10082895');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','PIANI','MARCO','10091409');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','PIANTINO','LORENZO','10091554');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PIASER','LUCA','10085245');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','PICARELLA','FRANCO','10080727');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PICARIELLO','CARMINE','10085474');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PICCI','FRANCESCO','10091640');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','PICCINI','ROBERTO','10046471');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PICCIONI','IGNAZIO','10045644');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','PICCIOTTI','VITO','10085529');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','PICCO','ROBERTO','10086431');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','PICCOLI','MARCO','10083313');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','PICCOLI','PAOLO','10046088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PICCOLINO','CARLO','10081933');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PICHIERRI','GABRIELE','10061137');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PIERGOTTI','ANTONIO','10083677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','PIERI','GIANLUCA','10090228');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','PIERRO','PIETRO','10078183');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','PIETRAGALLA','MICHELE','10088054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','PIETRAGALLA','BENEDETTO LUCA','10091948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','PIETRINI','GIUSEPPE','10083706');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PIETROSANTI','DARIO','10084285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','PIGLIACELLI','LUCIANO','10090479');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PILI','VINCENZO','10051588');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PILONI','RUBENS','10062157');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','PINO','GIUSEPPE','10091957');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','PINTORI','MICHELE','10092603');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PIPITONE','CARLO','10044875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','PIRAINO','MARCO','10083670');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','PIRAS','GIOVANNI','10052047');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PIRAS','PIETRO','10084637');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PIRAS','RICCARDO','10200843');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','PIRAS','PIERPAOLO','10085199');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PIRAS','GIORGIO','10092525');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','PIRASTRU','MAURO','10044965');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PIRINA','ANDREA','10085619');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PIRODDI','ENZO','10090818');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PIRODDI','ORLANDO','10084665');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','PIRRI','GIUSEPPE','10089972');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PIRRONE','GAETANO','10080654');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PIRULLI','DONATA ANTONIA','10062158');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','PISACANE','GERARDO','10086155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','PISANIELLO','ANTONIO','10061017');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','PISANO','ROBERTO','10084839');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PISANO','DOMENICO','10091808');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PISANO','ROBERTO ANTONIO','10200814');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PISCEDDA','VALERIO','10084309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PISCOPO','COSIMO EGIDIO','10084933');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','PISONI','GIANLUCA','10085490');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PISTOLATO','STEFANO','10078270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PISU','SANDRO','10084643');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','PITTALIS','SALVATORE','10085797');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PITTON','ENZO','10091977');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','PITZALIS','LUCA','10092691');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','PIUBENI','PAOLO','10085762');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','PIVA','MARIO','10083624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PIZZILEO','ANTONIO','10045954');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','PIZZIN','ENRICO','10045825');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','PIZZOLI','FABIO','10090314');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PIZZORNO','ANDREA','10091553');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PLANETA','GIROLAMO','10092702');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PLATI','BIAGIO','10045942');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PLATONE','LUIGI','10084052');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PLINI','PIETRO','10091985');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','PODDA','PIERO','10092478');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PODDA','MICHELE','10092894');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','POGGIALI','KATIA','10041788');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','POLETTI','MIRCO','10084633');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','POLI','MAURIZIO','10091476');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','POLIMENI','ROBERTO','10005220');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','POLISENO','GIUSEPPE','10084734');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','POLIZIA','DANIELE','10061115');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','POLIZZI','MATTIA','10091575');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','POLLANI','STEFANO','10082527');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','POLLI','ALDO','10083591');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','POLLIO','ALBERTO','10091231');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','POLLIO','ALBERTO','10091205');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','POLLIO','ENZO','10092005');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','POLLIO','MARIO','10084191');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','POLVERINI','LAURA','10062231');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','POLVERONI','MARCO','10089709');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','POLZONI','EUGENIO','10091287');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','POMPONIO','FABIO','10092289');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','PONTECORVI','FRANCO','10078875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','PONTILLO','MAURO','10086342');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PONTILLO','DANIELE','10084053');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','PONTONIO','AGOSTINO','10088683');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','POP','IONUT','10091657');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PORCEDDA','DAVIDE','10092510');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','PORCU','ENRICO','10200921');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','PORCU','FERNANDO','10052139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PORTA','PIER PAOLO','10045502');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','POSTERARO','FRANCESCO','10051001');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','POTECA','FRANCESCO','10091071');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','POTENZA','ROCCO','10044598');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','POZZATELLO','LORENZO','10092533');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','POZZERLE','MAURIZIO','10091384');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','PRATELLI','MARCO','10200835');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PRATICO''','ANTONINO','10080862');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','PREGHEFFI','IVO','10005139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','PRESTA ASCIUTTO','CARMELO','10092754');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','PRESTANDREA','TULLIO','10088825');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','PRESTIGIACOMO','SALVATORE','10092493');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','PRESUTTI','ENZO','10082568');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','PREVITI','DOMENICO','10087979');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','PRIMAVERILE','GIOVANNI','10091271');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PRIMICERI','VALERIO','10092049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PRINZI','GIULIO','10091309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PRISINZANO','DOMENICO','10085217');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','PROCENTESE','ANTONIO','10089372');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','PROCINO','GIUSEPPE','10078617');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','PROGRAMMA','DANILO','10045599');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PROPERZI','EMILIO','10084706');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','PROSPERI','FRANCO','10078602');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','PROVENZA','PIETRO','10092494');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','PROVENZANO','VITTORIO','10090107');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','PROVENZANO','CORRADO','10085058');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','PROVENZIANI','MICHELE','10062224');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','PRUNEDDU','IGNAZIO','10084543');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','PUDDINU','ROBERTO','10045093');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PUDDU','ANDREA','10200815');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','PUDDU','SERGIO','10045505');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','PUGLIESE','FRANCESCO','10091935');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','PUGLIESE','LEONARDO','10078942');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','PUGLISI','LUCIANO','10091156');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','PULEIO','PAOLO MIRKO','10091157');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','PULICE','SERGIO','10081549');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','PULIZZOTTO','GIUSEPPE','10092174');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','PULIZZOTTO','DANIELE','10092014');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','PULVIRENTI','GIUSEPPE ALESSANDR','10092237');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','PUNZO','GIUSEPPE','10061273');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','PUTTI','GIACOMO','10091185');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','QUADRELLI','LUCA','10045692');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','QUAGLIERI','ROBERTO','10084276');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','QUARANTA','FRANCESCO','10061139');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','QUARANTA','ANTONIO','10091705');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','QUARESIMA','ALESSANDRO','10041910');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','QUARTA','ORONZO','10091267');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','QUATTROMINI','BENIAMINO','10080175');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','QUATTRONE','PASQUALE','10088234');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','QUERO','GIUSEPPE','10061281');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','RABENSTEINER','OTHMAR','10089098');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','RACCUGLIA','SANTO','10083859');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','RAFASCHIERI','VITO','10085532');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','RAFFA','NUNZIO','10083856');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','RAFFINO','PIETRO','10092427');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','RAGONE','FRANCESCO NICOL','10084797');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','RAGONESI','GAETANO','10092547');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RAGOSTA','GUIDO','10045207');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RAGUSA','RICCARDO','10062163');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RAGUSA','SANTI','10092238');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','RAIMONDI','ROSARIO','10086671');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','RAIMONDO','FRANCESCO','10092428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RAINERI','GIANCARLO','10091921');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RAINONE','CARMINE','10084012');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','RAMACCI','FABIANO','10091533');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','RAMAGLIA','GIUSEPPE','10083977');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','RAMAIOLI','ANTONIO LUIGI','10085684');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','RAMONDELLI','FILIPPO','10061172');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','RAMPAZZO','LUCA','10092566');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RAMPONE','FRANCESCO','10089391');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','RANCATI','MAURIZIO','10080685');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RANDAZZO','ANTONINO','10092712');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','RANDO','MARCO','10092913');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','RANIERI','FABRIZIO','10061109');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','RANIERI','MARZIALE','10086843');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','RANZINI','ROBERTO','10200934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RAPACCIUOLO','GIOVANNI','10085605');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','RAPALLI','ALESSIO','10091402');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','RAPELLI','ANTONIO','10045542');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RAPICANO','ORESTE','10084900');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','RAPONE','MAURIZIO','10081600');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','RAPUANO','CRESCENZO','10045741');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','RAPUANO','DOMENICO','10045742');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','REALE','MARCO','10085294');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','REALE','GAETANO','10091194');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','REALE','PASQUALE','10045729');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','REATO','ADRIANO','10091345');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','REATO','GIOVANNI','10091355');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','REBOLINI','GIANLUCA','10090635');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','RECCHIA','ROBERTO','10045290');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','REGA','FELICE','10092067');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','REGAZZIN','GIORGIO','10045004');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','RENNA','NICOLO''','10200803');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','RENZETTI','LORENZO','10092495');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','RESTA','ROBERTO','10091523');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RESTA','IVANO','10091452');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','RESTIVO ALESSI','GIACOMO','10051848');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RESTUCCIA','FILIPPO','10083966');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','REZZELE','LUCA','10091387');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','RIBAUDO','ALESSIO','10092888');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RICAPITO','FILIPPO','10091904');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','RICCA','CARMINE','10088530');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','RICCA','MARCO','10046161');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','RICCI','CARLO','10084325');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','RICCI','PAOLO','10081042');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','RICCI','LUCA','10086403');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','RICCIARDI','GIUSEPPE','10080624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RICCIARDOLO','PAOLO','10091987');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','RICCIO','CRESCENZO','10091646');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RICCIO','NICOLA','10092002');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','RICCIO','GIUSEPPE','10092809');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','RICCIUTO','GIUSEPPE','10045743');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','RICCOBONO','GIUSEPPE','10044553');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RICHICHI','RAFFAELE','10061175');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','RICOTTA','FRANCESCO','10061178');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RICOTTONE','BRUNO','10092786');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','RIDOLFI','MARCO','10092765');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','RIDOLFI','ENRICO','10062167');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','RIGANO','MARCO','10090182');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','RIGGI','ROSARIO','10091366');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','RIMANO','STEFANO','10092116');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','RIO','ROBERTO','10044330');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','RIOLO','VITO','10061209');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RIPAMONTI','RICCARDO','10092050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RISPOLI','GIANPIERO','10062168');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','RITI','FRANCESCO ANDREA','10090502');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','RITI','MASSIMO','10090476');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','RIVA','MASSIMO CARLO','10091739');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RIVA','GENNARO','10085713');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','RIVRON','GAYLORD','10091330');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','RIZZA','MARIO SALVATORE','10080968');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','RIZZA','MARCO','10092548');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','RIZZO','SALVATORE','10091576');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','RIZZO','ALBERTO LUCIO','10082512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','RIZZO','ANGELO','10091235');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','RIZZO','GIORGIO','10076462');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','RIZZO','MAURIZIO','10041508');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','RIZZUTO','NICOLA','10083325');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','RIZZUTO','FRANCESCO','10091007');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ROBBIONI','EMANUELE','10091615');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','ROCCHI','VALTERE','10090667');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','ROCCO','GIUSEPPE','10082839');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ROMAGNOLI','GIUSEPPE','10090863');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ROMAGNOLI','PIETRO','10079915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','ROMAGNOLO','ALBERTO','10090225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','ROMALDETTI','AMERIGO','10081460');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','ROMANELLO','GIUSEPPE','10090944');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ROMANIN','RENATO','10046291');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','ROMANO','PASQUALE','10082649');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','ROMANO','ALFREDO','10045955');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','ROMANO','FRANCESCO TONIO','10090938');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','ROMANO','BASILE','10045128');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','ROMANO','EMMANUELE','10092659');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','ROMANO','NORBERTO','10084809');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','ROMANO','PELLEGRINO','10089382');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','ROMANO','GIUSEPPE','10061155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','ROMANO','RAFFAELE','10092505');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','ROMANO','FABIO','10092757');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','ROMBOLA''','DAVIDE','10091757');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','ROMEO','VINCENZO','10045464');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','ROMUALDI','ROBERTO','10084269');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','RONCA','ANSELMO','10084429');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RONDINE','GREGORIO','10046053');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ROSA','LUCIO PAOLO','10045005');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ROSATI','PAOLO','10092097');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','ROSATTI','DANIELE','10090615');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ROSIELLO','WALTER','10062169');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ROSSANO','VINCENZO','10200886');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','ROSSELLI','FRANCESCO','10090889');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','ROSSI','ALESSANDRO','10091979');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','ROSSI','TIZIANO','10200829');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','ROSSI','ANDREA','10091070');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','ROSSIGNOLI','ADRIANO','10082715');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','ROSSINO','DOMENICO','10083103');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','ROSSO','ILARIO','10043914');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','ROSSO','OSVALDO','10044403');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','ROSSO','MARIO','10081425');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','ROSSO','ALESSANDRO','10090418');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ROSTA','CARMELO','10092810');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ROSU','ADRIAN','10092812');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ROTA','ALESSANDRO','10062171');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','ROTONDO','GIOVANNI CLAUDI','10084201');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','ROVANI','LUCA','10090799');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','RUBAGOTTI','GIAN GABRIELE','10078238');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RUBERTI','VALTER','10046050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','RUBIN','RICCARDO','10045514');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RUBINO','GIUSEPPE','10091270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','RUGGERI','VITTORIO','10085042');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','RUGGIERI','VALERIO','10089945');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','RUGGIERO','ROSARIO','10091201');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','RUGOLOTTO','ALBERTO','10091770');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','RUISI','PIETRO','10090386');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','RUISI','GABRIELE','10092534');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','RUPERTO','EMILIO','10084609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','RUSCIO','FRANCESCO','10090609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','RUSSO','MICHELE','10084049');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','RUSSO','SALVATORE','10045541');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','RUSSO','SABINO','10092698');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','RUSSO','RAFFAELE','10045755');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','RUSSO','ANTONIO','10045744');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','RUSSO','MICHELE','10088225');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','RUSSO','STRATO','10061014');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','RUSSO','ANTONIO','10091811');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','RUSSO','MICHELE','10085464');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RUSSO','SALVATORE','10082784');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','RUSSO','SALVATORE','10086208');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','RUSSO','ANTONIO','10052223');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','RUSSO','CARLO','10200790');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RUSSO','SALVATORE','10046023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','RUSTICO','CESARE','10088520');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','RUTIGLIANO','GIOVANNI','10091268');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','RUVIOLI','SALVATORE','10090562');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','SABATO','MARIO','10091346');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SABATO','ENRICO','10045756');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SABATO','FRANCESCO','10091396');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','SABATTINI','GIOVANNI','10052090');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SACCHETTI','GIUSEPPE','10091980');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SACCHETTI','DOMENICO','10079019');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SACCO','ANGELO','10200836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SACCOMANDO','ROCCO','10045746');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SACCONI','MAURO','10086702');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SADDI','MARCO','10200816');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SAGITTARIO','FRANCESCO','10084175');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','SAGLIANO','GIOVANNI LUCA','10091766');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','SAI','ALESSANDRO','10091818');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SAINATO','DOMENICO','10091788');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SAITTA','ANTONINO','10092634');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SALA','FABIO','10089960');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SALA','ALBERTO','10085577');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SALA','ANTONIO','10083454');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SALAMINA','DONATELLO','10200894');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SALAMINO','GIUSEPPE','10083869');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','SALAMONE','FRANCESCO','10092630');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SALERNO','ANTONIO','10045958');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','SALGARI','FABIO','10091473');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','SALI','ROBERTO','10079508');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','SALIANO','FILIPPO','10085544');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SALIANO','RAFFAELE','10091506');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SALIANO','SALVATORE','10084030');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SALIERNO','FRANCESCO','10089975');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','SALTAFORMAGGIO','SERGIO','10091043');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','SALTAFORMAGGIO','VINCENZO','10092713');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SALUSTRI','MAURIZIO','10092245');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SALUZZI','BENEDETTO','10045674');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','SALVADOR','GUGLIELMO','10078039');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SALVALAIO','RICCARDO','10044763');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','SALVATO','ELIA','10081553');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','SALVATORE','PASQUALE','10080871');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','SALVATORE','DOMENICO','10082466');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SALVI','FEDERICO','10200895');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SALVI','EGIDIO','10091779');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','SANALITRO','ROBERTO','10084170');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','SANCARLO','VINCENZO','10092496');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','SANCARLO','RICCARDO','10092838');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SANDRIN','MATTEO','10092693');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','SANDRIN','DOMENICO','10200891');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SANGALLI','STEFANO','10061267');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','SANGALLI','DARIO','10090029');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SANGERMANO','VINCENZO','10089390');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','SANGIORGI','LUIGI','10091573');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SANNA','ANDREA','10092621');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SANNA','ALDO','10092604');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SANNA','ANDREA','10062218');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SANNA','FRANCESCO','10081771');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SANNA','SANDRO','10045993');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SANNA','BRUNO','10086457');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','SANNA','RICCARDO','10200851');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','SANNA','CLAUDIO','10092104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SANNA','SANDRO','10092605');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','SANSONE','PASQUALE','10089897');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SANSONE','PAOLO','10091512');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','SANTANGELO','EMANUELE','10091158');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','SANTANIELLO','FELICE','10088891');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SANTELLA','RAFFAELE','10084043');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SANTI','PAOLO','10079058');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SANTILLI','SANDRO','10085525');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SANTILLO','STEFANO','10089341');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SANTILLO','CIRO','10091324');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SANTISI','TOMMASO ROSARIO','10090463');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SANTODIROCCO','VITO','10092248');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','SANTORIELLO','DAVIDE','10092935');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','SANTORO','NICOLA','10085534');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SANTORO','PAOLO','10081470');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SANTORO','GABRIELE','10092829');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SANTORO','MAURO','10078987');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SANTUCCI','LUIGI','10045747');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SAPONARO','ANGELO','10091641');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SARDI','LORENZO','10091444');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SARDO','MICHELANGELO','10086010');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','SARPERI','LEONARDO','10045856');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','SARTI','FRANCESCO','10092746');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','SARTIRANA','GIANPIERO','10090321');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SASANELLI','MICHELE','10080866');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SASSO DEL VERME','DAVIDE','10091460');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','SASSONE','FRANCESCO','10092062');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','SASSONE','FERNANDO','10085330');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SASTRO','MASSIMILIANO','10086690');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SATTA','STEFANO','10091790');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SAVARESE','GIOVANNI','10091429');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SAVARESE','SALVATORE','10084040');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','SAVARRO','BERNARDINO','10052205');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SBAI','VITTORIO','10092736');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SBARAGLIA','ROBERTO','10084291');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106352','SCACCIATELLA','ENRICO','10092700');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SCAFURO','GAVINO','10092660');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','SCAGLIANTI','CRISTIANO','10045324');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SCAIA','ANDREA','10044540');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','SCALABRINI','DENIS','10091486');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','SCALDAFERRI','MARIO','10083141');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','SCALIA','PIERO','10092157');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','SCALZO','DOMENICO','10061090');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SCANO','GIAMPAOLO','10045503');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','SCANO','FABIO','10084645');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','SCANU','CLAUDIO','10084910');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','SCARABAGGIO','DONATO','10083255');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','SCARAMUCCIA','CRISTIANO','10085930');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','SCARAVILLI','CRISTIAN','10091973');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','SCARPA','GIANCARLO','10078663');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SCARPARO','PIER GIORGIO','10090757');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','SCHIAVO','DANIELE','10044276');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SCHIAVONE','LUIGI','10090649');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SCHIAVONE','AMATO','10084008');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SCHIAVONE','OTTAVIO','10079033');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','SCHICCHI','VINCENZO','10090643');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','SCHIFAUDO','GIOVANNI','10061084');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SCHILIRO''','ANTONIO','10092607');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCHILLACI','SALVATORE','10092714');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','SCHIMMENTI','SERGIO','10092037');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','SCHIPANI','FRANCESCO','10044802');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','SCHIRIPPA','VITO','10078930');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCIACCA','SERGIO ROSARIO','10091468');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCIACCA','CARMELO','10091124');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCIACCA','SALVATORE','10045717');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SCIANATICO','DOMENICO','10083409');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCILLIA','ROBERTO','10091095');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','SCIUTTO','ANTONIO','10051147');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','SCLAFANI','CIRO','10089948');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SCOGNAMIGLIO','GIOVANNI','10082650');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','SCOGNAMIGLIO','BRUNO','10084898');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','SCORDATO','ONOFRIO','10079458');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SCREPANTI','CLAUDIO','10062239');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106285','SCRIVANO','GIUSEPPE','10084717');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','SCUDERI','SAVERIO','10091922');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','SCUDERI','DAVIDE SALVATORE','10092521');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SCUDIERI','THOMAS','10200876');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SCURPA','PIETRO','10086196');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','SECCHI','STEFANO','10092479');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SECCHI','GIULIANO','10084326');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SECCHI','RINO','10085616');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SECCI','ALESSANDRO','10084845');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SECCI','MARZIO','10084331');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SECHI','GIANLUIGI','10092622');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SEDICINA','MICHELE','10089040');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SEGAT','TONINO','10084703');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','SEIDITA','MANUEL','10091069');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','SELLITTO','TOMMASO','10084858');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SELVA','DAVIDE','10091442');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SEMENZATO','FRANCO','10090335');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','SEMPLICI','SILVIO','10085320');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SEPIACCI','STEFANO','10084107');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SERAFIN','DAVIDE','10044958');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','SERAFINI','FABRIZIO','10045050');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SERAFINI','FRANCESCO','10079005');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SERDINO','FRANCESCO','10082285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','SERENA','ANTONIO','10088259');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SERENA','DARIO','10061145');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','SERINI','RENATO','10046544');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','SERIO','GIUSEPPE','10045793');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SERLINI','LUCIANO','10083075');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','SERRA','FRANCESCO','10045466');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SERRA','GIUSEPPE','10082933');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','SERRA','STEFANO','10091048');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SERRANO','GIUSEPPE','10083076');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SERRAO','FILIPPO','10092787');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SERRELI','MARCO','10200917');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106358','SERRIPIERRI','VITO','10092833');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','SETTE','GIUSEPPE','10084067');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','SETTIPANI','GIUSEPPE','10045349');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SEYE','ADAMA','10200898');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SFRISO','MARCO','10085023');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','SGADARI','VINCENZO','10045326');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','SGARLATA','DARIO','10092579');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','SGNAOLIN','CARLO','10062178');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SGURA','CARLO','10091453');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SIANO','ANIELLO','10085994');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SIBIO','AGOSTINO','10091612');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SICA','ERNESTO','10080199');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','SICCARDI','PAOLO','10091806');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','SICH','GIOVANNI','10046489');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','SICILIANO','FRANCO','10083204');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SICURELLA','CALOGERO','10085406');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','SIGNANI','ALESSIO','10091187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','SILANUS','ANTONELLO','10082932');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SILVESTRI','ROCCO','10081782');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','SILVESTRO','FABIO','10090864');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','SIMBARI','ROSARIO','10086371');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','SIMIONATO','ALBERTO','10061185');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SIMMINI','VINCENZO','10091244');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','SIMONE','DONATO','10084916');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SIMONE','DONATO','10084615');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','SIMONINI','ANGELO MARIO','10078586');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SINISI','RICCARDO','10062180');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SISINNI','LORENZO','10062181');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106628','SMALDONE','VINCENZO','10090510');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','SOCCI','ANDREA','10090053');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SOCCORSI','PASQUALE','10062182');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SODANO','GAETANO','10082846');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SODDU','DANILO','10200918');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SOLARO','ROBERTO','10082915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','SOLAVAGGIONE','LUCIANO','10084781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SOMMA','CARMELO','10090928');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SORBI','MARCO','10079365');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SORELLI','LUCA','10081609');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SORO','PIER PAOLO','10082836');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','SORO','STEFANO','10092469');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','SORO','RICCARDO','10045603');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SORRENTI','ANDREA','10084928');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SORRENTI','PAOLO','10092500');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SORRENTINO','VINCENZO','10092433');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','SORRENTINO','ANIELLO','10091368');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','SOTGIA','LUCA','10200910');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','SOTGIA','GIOVANNI','10045510');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','SPADA','PATRIZIO','10090941');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','SPADAFINA','PAOLO','10087797');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','SPAGNOLI','MARCO','10076787');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SPAGNOLI','GIAN LUCA','10083912');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','SPAGNOLO','GIUSEPPE','10044442');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SPAGNOLO','ALEX','10092531');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SPAGNOLO','VALERIO','10091500');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','SPANO','GEROLAMO','10043601');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','SPANO''','MASSIMO','10062183');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SPARAPANO','MICHELE','10092750');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','SPAZIANI','DANIELE','10091487');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','SPECIA','MARIO','10085774');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','SPERA','PAOLO','10045625');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106366','SPICCIANI','DOMENICO','10084066');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','SPIEZIO','GIANNI','10092507');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','SPINA','PIER PAOLO','10083480');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','SPINA','DANIELE','10200832');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','SPINELLA','SALVATORE','10092055');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','SPINELLI','FABRIZIO','10086455');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','SPINELLI','VITANTONIO','10084804');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SPINELLI','ALESSANDRO','10091570');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SPITALE','SIMONA','10062217');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','SPITALERI','PROSPERO','10092549');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','SPITALERI','ROSARIO','10092631');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','SPOSATO','ADAMO','10091020');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','SPOTO','AGOSTINO','10091645');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105326','SQUADRITO','DANIELE','10090223');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','SQUITIERI','LUCIO','10085574');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','STABILE','MASSIMO','10088270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106381','STABILE','GIANLUCA','10091192');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','STANCA','COSIMO','10046024');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','STANCO','GIUSEPPE SEBASTIANO','10092632');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106101','STASSI','MICHELE','10081439');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','STEFANINI','MARCO','10079632');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','STEFANO','AURELIO','10090552');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','STEFANONI','DAVIDE','10092064');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','STEFANONI','ENRICO','10062184');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','STEFANUTTI','GIANNI','10080154');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','STERCHELE','MARCO','10092793');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','STINGO','CIRO','10200877');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','STIRPE','ANDREA','10091091');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','STOMPANATO','MARIO','10090181');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','STOPPA','MORENO','10061182');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','STOPPACCIARO','FABRIZIO','10062185');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','STOPPINI','OLIVO','10079118');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','STORILLI','STEFANO','10091517');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','STORILLI','IMBRIANO ANTONIO','10091240');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','STORNELLI','FRANCO','10081812');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','STRADIOTTO','ROBERTO','10083604');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','STRAMAGLIA','NICOLA','10083913');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','STRAVIRCHI','GIAN MARIA','10085027');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','STRAZIOTA','MICHELE','10085395');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','STRIGNANO','MICHELE ROBERTO','10061031');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','STUMPO','FRANCESCO','10078907');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','SUMA','ORONZO','10091237');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','SURANO','ALFREDO','10091501');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','SURPOLI','LUCA','10084949');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','SUSCO','FLAMINIA','10091910');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','SUSU','ALEXANDRU','10092937');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','TABACCHI','ROBERTO','10044460');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','TABARRONI','LUIGI','10085150');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','TABASCIO','ROBERTO','10091437');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','TABORNI','MASSIMO','10079791');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','TAGLIA','MARIO','10082772');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','TAGLIABO''','MAURIZIO','10084918');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','TAGLIABUE','PAOLO ENEA','10091758');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','TAGLIABUE','MATTEO GIUSEPPE','10091482');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','TAGLIAFERRI','AGNELLO','10081270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','TALARICO','SALVATORE','10085249');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','TAMBORRA','FRANCESCO','10045796');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','TAMBURRANO','LUIGI ALBERTO','10084647');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','TANDOI','LUCA','10085736');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','TANGARI','PAOLO','10061211');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','TANGREDI','SERGIO','10091941');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','TARANTINO','SALVATORE','10083270');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','TARANTINO','GIOVANNI','10092188');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','TARDIVEL','MORENO','10091607');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','TARICCO','EDOARDO','10084554');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TARQUINI','FRANCESCO','10092414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','TARQUINI','LEANDRO','10084428');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','TARSIA','VITO','10081890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','TASSI','RICCARDO','10044203');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','TATA','ROBERTO','10090751');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106228','TEANI','CARLO','10078344');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','TEDDE','FRANCESCO','10052210');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','TEDESCO','ANDREA','10091412');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','TELESIO','VINCENZO','10085088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','TERRANOVA','FABIO','10092890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TERUZZI','GIACOMO','10092041');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TESSE','PAOLO','10062186');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','TESTA','MARIO','10081371');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TICCONI','DANILO','10092339');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','TIOZZO FASIOLO','ANDREA','10091843');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','TIRITIELLO','SALVATORE','10045792');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','TIRONE','LUIGI','10045748');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','TOCCHIO','ROBERTO','10084187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','TODDE','ROBERTO','10083858');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','TOFFOLI','ANDREA ENRICO','10046497');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','TOLIS','FABIO','10062187');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','TOMA','PIERLUIGI','10091430');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','TOMARCHIO','SANTO','10044671');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','TOMASICCHIO','GIUSEPPE','10092337');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','TOMMASI','GIAN MARIO','10200828');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','TONDOLO','GIOVANNI','10091113');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','TONELLI','MASSIMILIANO','10045907');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106229','TONNELLATO','GIUSEPPE','10090673');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','TONUSSI','MAURO','10046305');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TOPINO','FABRIZIO','10062188');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','TORA','MARCO','10084483');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','TORCHIA','SALVATORE','10092056');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','TORNESE','GIORGIO','10091219');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','TORRI','GIACOMO','10078312');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','TORRISI','ALFIO','10092229');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','TORRISI','GIUSEPPE','10091604');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','TORTATO','STEFANO','10091543');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','TORTORELLA','FRANCESCO','10078092');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','TOSELLI','FABRIZIO','10078033');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','TOSI','ANTONELLO','10062189');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','TOSTI','ROBERTO','10081722');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106105','TRAGNI','CARLO','10084032');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','TRAMACERE','STEFANO','10092227');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TRANDAFIR','RADU FLORIN','10092215');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106429','TRAVERSO','AURELIO','10079637');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','TREBINI','LUIGI','10082289');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','TREMEA','JIMMY','10091890');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','TRENTAROSSI','LUIGI','10078149');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','TRETOLA','CARMINE','10045935');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','TRIGGIANO','FRANCESCO','10083756');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','TRINCHESE','GIUSEPPE','10090007');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','TRIPOLI','FRANCESCO','10089379');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','TRIPPIEDI','SIMONE IGNAZIO MICHELE','10092739');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','TRONCI','ALESSANDRO','10083089');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','TRONCI','MAURO','10043613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','TRONCIA','FEDERICO','10090445');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','TRONCIA','GIORGIO','10090300');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','TRONCONE','GIUSEPPE','10086693');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','TRONU','MAURIZIO','10080547');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','TROPIANO','DOMENICO','10062190');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','TRUONO','GIOVANNI','10083767');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','TUBARO','ROBERTO','10046501');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','TUCCITTO','ANTONIO','10045676');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','TUFARIELLO','FRANCESCO','10091029');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','TUGNOLI','MAURIZIO','10045797');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','TULICI','ALEXANDRU NICOLAE','10092938');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','TUNDO','GIOVANNI','10092112');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','TUNNO','FABIO','10085958');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106428','TURCO','MASSIMO','10092155');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','TURCO','FRANCESCO','10091239');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','TURIANO','ANDREA','10092715');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','TURSI','ANTONIO','10084951');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106380','TUZZOLO','ENRICO','10079598');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','UGENTI','FRANCESCO','10084616');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','URSO','FILIPPO','10089349');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106466','USAI','ROBERTO','10085212');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106286','VACCARINO','GIUSEPPE','10090875');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','VACCARO','SAVERIO','10091021');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','VACCARO','GIUSEPPE','10043285');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','VACCARO','SALVATORE','10044466');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','VADINI','ENRICO','10044904');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','VAGNOZZI','MAURO','10082524');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','VALENTE','DOMENICO','10085179');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VALENTI','FEDERICO','10091975');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106656','VALENTI','MARCO','10092716');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','VALENTINI','GIANCARLO','10045365');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','VALERIO','MUZIO','10085215');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VALERIO','ALESSIO','10200878');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VALLETTA','MARIO','10091109');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VALLONE','NICOLA GREGORIO','10086427');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','VANETTI','IVAN LUIGI','10091548');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','VANZAN','MICHELE','10080781');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','VARANO','FRANCO','10090042');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','VARGIU','MARIO','10082294');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106636','VARLESE','FERDINANDO','10092789');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','VARRICCHIO','ANGELO','10092128');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','VARRICCHIO','PELLEGRINO','10091882');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','VARRICCHIO','MASSIMO','10045749');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VARTUCA','FRANCESCO','10091524');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','VARVARO','SANTO','10079673');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106381','VASTOLA','PASQUALE','10092883');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106253','VECCHIATO','DIEGO','10080480');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','VECCHIATO','MARIO','10079309');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','VELARDI','ANTONIO LUPO','10085923');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','VELLISCIG','ERIC','10200893');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','VELLO','MICHELANGELO SALVATORE','10200767');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','VELLO','CARMELO SALVAT','10082873');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VELLUCCI','ALESSIO FLORIDO AUGUSTO','10062194');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VELLUTO','FRANCESCO','10092717');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106351','VELTRI','FRANCESCO','10092718');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106426','VENNERI','FRANCESCO','10078677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','VENTRE','ANTONIO','10084613');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','VENTRONI','ATTILIO','10084503');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','VENTURA','STEFANO','10081588');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106256','VENTURI','BRENNO','10005327');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','VENTURINI','MARCO','10079633');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','VERBICARO','FRANCO','10091041');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106352','VERDE','VINCENZO','10092180');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106257','VERDECCHIA','LUIGI','10079252');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','VERDILE','PAOLO','10085138');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','VERI','ROSSANO','10046054');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','VERLOTTA','COSMO','10084014');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106270','VERNILE','GIUSEPPE','10081471');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','VERNILLO','IRMINIO','10045751');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','VERONA','ANTONIO CAMILLO CL','10041495');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VERONA','GABRIELE','10200935');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','VERONESI','LUCA','10090088');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','VERRENGIA','EDOARDO','10081710');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','VERRIELLO','SAVERIO','10083407');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','VEUTRO','LUCA','10091997');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','VIANO','STEFANO','10091600');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','VICARIO','ALFONSO','10091398');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106357','VICECONTE','ANTONIO DONATO','10092057');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106386','VICINO','RAFFAELLO','10092661');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VIGLIONE','STEFANO','10091702');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VILLA','LORENZO','10091759');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','VILLA','ANDREA','10091619');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VILLA','DARIO CELSO','10044507');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','VILLELLA','SANTO','10077031');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','VINCI','MICHELE','10091780');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VIOLA','ADELE','10062199');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','VIOLA','ROBERTO MARIO','10078409');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','VIRDIS','MARINO','10082660');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106387','VIRGA','FRANCESCO','10092258');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','VIRGILIO','GIUSEPPE','10082934');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','VISCOMI','PAOLO','10091596');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','VISENTINI','ROLANDO','10046511');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106649','VITA','NICOLA','10077308');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106104','VITALE','BIAGIO','10081003');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106661','VITALE','DOMENICO','10085688');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','VITI','CARLO','10084883');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','VITOLINO','LIVIO','10061204');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','VITTORELLI','MICHELE','10082917');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','VIZZINI','DIEGO','10091438');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106211','VIZZUSO','ROCCO RENZO','10061047');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','VLAD','GHEORGHE','10200834');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106470','VOCALE','GIANCARLO','10090509');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','VOLOSHENYUK','MAKSYM','10200896');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','VOLPE','LUIGI','10087600');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','VOLPI','FRANCO','10062200');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','VOLPICELLA','GIUSEPPE','10091414');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','VOMMARO','ANTONIO','10079982');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106480','VOTTO','GIUSEPPE','10041754');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','VULPIANI','MARIO','10062201');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106485','WIELAND','PETER MARIO','10084192');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105727','YERYMIYCHUK','VOLODYMYR','10090650');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','ZABOJA','LUCA ANGELO','10061205');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106611','ZACCAGNI','MARCO','10044955');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106061','ZACCARDO','SERGIO','10084721');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','ZACCHELLO','ISACCO','10041501');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ZAFFIRO','ROSSANA','10091634');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ZAMAGNI','GIOVANNI','10043624');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','ZAMBOLINI','LUCIANO VALERIO','10089332');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ZANANDREA','NICOLA','10200899');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106681','ZANATTA','FEDERICO','10091588');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ZANAZZI','ANTONIO','10090189');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ZANCARLI','VALERIO','10200866');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ZANETTI','ALBERTO','10091474');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106653','ZANETTI','CLAUDIO','10046073');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106411','ZANGA','FEDERICO','10091954');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ZANOVELLO','GIULIANO','10085299');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ZAPPALA''','LUCIANO ANTONINO','10092915');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106657','ZAPPALA''','DAVIS','10091363');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106106','ZAPPALA''','ROSARIO','10090994');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ZAPPALA''','SALVATORE LUCIANO','10092042');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ZAPPONE','ANTONIO','10092394');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106486','ZAPPULLA','SANTI','10045677');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ZEDDA','GIORGIO','10090815');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106665','ZEDDA','MAURIZIO','10200780');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106670','ZENONE','ANTONIO','10091104');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106646','ZERBINATI','PAOLO RUBES','10078281');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106680','ZEZZE','SABATO','10045752');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ZIGLIOLI','PAOLO','10062202');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ZILLI','FLAVIO','10046313');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106481','ZILLI','FABIO','10046521');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106654','ZINELLI','NICOLO''','10092567');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ZINGALE','DAMIANO','10092737');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106457','ZINI','GRAZIANO','10092135');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ZITO','ALESSIO','10200887');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106673','ZIZZARI','ANTONIO','10045956');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106671','ZOCCALI','ESPEDITO LUCIAN','10089733');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ZOCCHEDDU','ANDREA','10092623');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106266','ZOCCHEDDU','ANTONIO','10045853');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ZOIA','MICHELE','10081401');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('105426','ZONCA','MARIA TERESA','10062204');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','ZOTTI','GEPPINO','10045091');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106264','ZOTTI','MARIO','10045847');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ZOUMBARE','ABDOULAYE','10200888');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106103','ZUCCA','GIORGIO','10045357');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106672','ZUCCARO','GAETANO','10091159');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106439','ZUCCHERI','VALTER','10084487');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106454','ZUCCHETTO','FRANCESCO','10044826');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106626','ZUDDAS','DANIEL','10200880');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106102','ZULLO','DONATO FRANCESCO','10081303');
Insert into WORKS.SQUADRE_CENTRI_LAVORO ("workingGroupCode","surname","name","teamId") values ('106226','ZURLO','LUCA','10090046');

show errors

commit;

quit
