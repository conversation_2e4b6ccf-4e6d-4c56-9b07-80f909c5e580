set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE FORCE VIEW V_CAVI_LAVORI AS
  select
   pt."customerId"
  ,pt."contractId"
  ,pt."projectId"
  ,to_number(dt4.valore) "cableId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,pt."maker" "maker"
  ,pt."teamId" "teamId"
  ,pt."startPlannedDate" "startPlannedDate"
  ,pt."startWorkDate" "startWorkDate"
  ,pt."endPlannedDate"
  ,pt."estimatedDuration"
  ,pt."endWorkDate"
  ,pt."spentTime"
  ,pt."subContractCode"
  ,(select dt5.valore from v_dt_sistemi dt5 where dt5.id_sistema = s.id and dt5.nome = 'recoveryCable') "recoveryCable"
  ,(select dt6.valore from v_dt_sistemi dt6 where dt6.id_sistema = s.id and dt6.nome = 'infrastructureCheck') "infrastructureCheck"
  ,(select dt7.valore from v_dt_sistemi dt7 where dt7.id_sistema = s.id and dt7.nome = 'plannedBreakdown') "plannedBreakdown"
  ,(select dt8.valore from v_dt_sistemi dt8 where dt8.id_sistema = s.id and dt8.nome = 'interruptedMinitubes') "interruptedMinitubes"
  ,(select dt9.valore from v_dt_sistemi dt9 where dt9.id_sistema = s.id and dt9.nome = 'branchesCut') "branchesCut"
  ,(select dt10.valore from v_dt_sistemi dt10 where dt10.id_sistema = s.id and dt10.nome = 'pilingCheck') "pilingCheck"
  ,(select dt11.valore from v_dt_sistemi dt11 where dt11.id_sistema = s.id and dt11.nome = 'teamNote') "teamNote"
  ,(select dt12.valore from v_dt_sistemi dt12 where dt12.id_sistema = s.id and dt12.nome = 'assistantNote') "assistantNote"
  ,case
    when pt."maker" = 'Team' then (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SIRTI'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
    else (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SUBAPPALTO'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
   end "techninalAssistantsAssignee"
   ,case
      when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
      when a.stato_corrente in ('ATTESA_CARICAMENTO_AS_BUILT','ATTESA_GESTIONE_AS_BUILT') then 'FASE_AS_BUILT'
      else a.stato_corrente
    end "workStatus"
   ,case
      when a.stato_corrente = 'ATTESA_CARICAMENTO_AS_BUILT' then 'ATTESA_CARICAMENTO'
      when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
      when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
      else null
    end "asBuiltStatus"
from
  v_sistemi s
    join v_attivita a on a.id_sistema = s.id
    join pt_lavoro pt on pt.id_attivita = a.id
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'cableId'
where 1=1
  and s.tipo = 'CAVO'
  and a.nome_tipo_attivita = 'LAVORO'
  and a.id = (
    select max(vc.id)
    from v_attivita vc
      join v_sistemi vs on vs.id = vc.id_sistema
      join v_dt_sistemi vdts on vdts.id_sistema = vs.id
      join v_dt_sistemi vdts2 on vdts2.id_sistema = vs.id
      join v_dt_sistemi vdts3 on vdts3.id_sistema = vs.id
    where vs.tipo = s.tipo
    and vdts.nome = dt4.nome
    and vdts2.nome = 'customerId'
    and vdts3.nome = 'contractId'
    and vdts.valore = dt4.valore
    and vdts2.valore = pt."customerId"
    and vdts3.valore = pt."contractId"
  )
;

CREATE OR REPLACE VIEW V_CAVI_LAVORI_DETTAGLIO AS
  select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,cables."cableId"
  ,cables."cableName"
  ,cables."status"
  ,cables."fromNetworkElementName"
  ,cables."fromNetworkElementType"
  ,cables."toNetworkElementName"
  ,cables."toNetworkElementType"
  ,cables."cableType"
  ,cables."potential"
  ,cables."unitOfMeasure"
  ,cables."fromNetworkElementId"
  ,cables."fromNetworkElementGeoLocation"
  ,cables."toNetworkElementId"
  ,cables."toNetworkElementGeoLocation"
  ,cables."UI"
  ,cables."horizontalLength"
  ,cables."verticalLength"
  ,cables."fromStockLength"
  ,cables."toStockLength"
  ,cables."plannedUndergroundLength"
  ,cables."plannedAerialLength"
  ,cables."plannedFacadeLength"
  ,cables."doneUndergroundLength"
  ,cables."doneAerialLength"
  ,cables."doneFacadeLength"
  ,cables."doneHandmaidLength"
  ,cables."estimatedDuration"
  ,lav."id" "activityId"
  ,lav."status" "activityStatus"
  ,lav."maker" "activityMaker"
  ,lav."teamId" "activityTeamId"
  ,lav."startPlannedDate" "activityStartPlannedDate"
  ,lav."startWorkDate" "activityStartWorkDate"
  ,lav."endPlannedDate" "activityEndPlannedDate"
  ,lav."estimatedDuration" "activityEstimatedDuration"
  ,lav."endWorkDate" "activityEndWorkDate"
  ,lav."spentTime" "activitySpentTime"
  ,lav."subContractCode" "activitySubContractCode"
  ,lav."techninalAssistantsAssignee" "activityTechAssAssignee"
  ,lav."asBuiltStatus" "activityAsBuiltStatus"
  ,lav."recoveryCable" "activityRecoveryCable"
  ,lav."infrastructureCheck" "activityInfrastructureCheck"
  ,lav."plannedBreakdown" "activityPlannedBreakdown"
  ,lav."interruptedMinitubes" "activityInterruptedMinitubes"
  ,lav."branchesCut" "activityBranchesCut"
  ,lav."pilingCheck" "activityPilingCheck"
  ,lav."teamNote" "activityTeamNote"
  ,lav."assistantNote" "activityAssistantNote"
  ,case
    when cables."status" in ('Planned') and lav."status" is null then 'notWorkable'
    when cables."status" in ('Workable') and lav."status" is null then 'workable'
    when cables."status" in ('Done') then 'sinfoUpdated' -- NB: se l'attivita non esiste sarebbe un warning...
    when cables."status" in ('Accountable') then 'accountable' -- NB: se l'attivita non esiste sarebbe un warning...
    when
         (lav."status" in ('APERTA','NUOVO','INOLTRATO_SIRTI'))
      or (lav."status" = 'IN_LAVORAZIONE_SIRTI' and (lav."teamId" is null or lav."startPlannedDate" is null))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO' and lav."subContractCode" is null) -- NB: solo per garantire la fase di transizione
    then 'notPlanned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is null)
    then 'planned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and ((lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is not null)))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO') -- NB: volutamente viene ignorato il valore della lav."startPlannedDate"
    then 'workInProgress'
    when lav."status" = 'ATTESA_CARICAMENTO_AS_BUILT' then 'workEnded'
    when lav."status" = 'ATTESA_GESTIONE_AS_BUILT' then 'asBuiltNotWorked'
    when lav."status" in ('ESPLETATO','QUARANTENA') and cables."status" = 'Workable' then 'asBuiltWorked'
    when lav."status" in ('ANOMALIA','ASSURANCE_ANNULLAMENTO') then 'assurance'
    when lav."status" in ('ANNULLATO','ATTESA_ANNULLAMENTO') then 'cancelled'
    else null
  end "virtualStatus"
from
  v_cavi cables
    join v_progetti pfp on cables."customerId" = pfp."customerId" and cables."contractId" = pfp."contractId" and cables."projectId" = pfp."projectId"
    left join v_cavi_lavori lav on lav."cableId" = cables."cableId" and lav."customerId" = pfp."customerId" and lav."contractId" = pfp."contractId" and lav."projectId" =  pfp."projectId"
;

quit

