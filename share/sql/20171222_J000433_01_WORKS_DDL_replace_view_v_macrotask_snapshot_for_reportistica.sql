set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_macrotasks_snapshot as
select  mtr."customerId",
	  mtr."contractId",
	  mtr."projectId",
	  mtr."sinfoLastModified",
	  mt."permitsAreaId",
	  mt."type",
	  mt."category",
	  mt."subCategory",
	  mt."unitOfMeasure",
	  mt."toDoQuantity",
	  mt."doneQuantity",
	  mt."accountedQuantity",
	  mt."estimatedDuration" "duration"
from  macrotasks mt
	inner join macrotasks_run mtr on mtr."runId" = mt."runId"
where mt."historicizingRunId" is null ;

quit
