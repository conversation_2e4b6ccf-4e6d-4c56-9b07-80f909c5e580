set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

create or replace view v_r_centri_lavoro as
SELECT '49' "companyCode",
    substr(ccosto, 1, 2) || substr(ccosto, length(ccosto) - 3) "id",
    descr "name"
FROM
    anag.ccosto@service_anag
WHERE
    -- TIM / OF
    (
        (ccosto like '49%66__' or ccosto like '49%64__' or ccosto like '49%61__' or ccosto like '49%69__')
        and tipo in ('4')
    ) or
    (
        tipo in ('5')
        and (
            -- solo OF
            ccosto like '49%6818' or
            ccosto like '49%6819' or
            ccosto like '49%6827' or
            ccosto like '49%6856' or
            ccosto like '49%6857' or
            ccosto like '49%6895' or
            -- solo TIM
            ccosto like '49%6906' or
            ccosto like '49%6907'
        )
    );

show errors

quit
