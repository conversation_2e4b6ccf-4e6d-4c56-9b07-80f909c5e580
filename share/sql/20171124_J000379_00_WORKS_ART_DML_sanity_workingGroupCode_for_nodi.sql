set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into dati_tecnici
select s.id id_sistema
, null id_sottosistema
, (select id_tipo_dato_tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode') ID_TIPO_DATO_TECNICO
, dta.valore DESCRIZIONE
, seq_dati_tecnici.nextval ID_DATO_TECNICO
, (select id_operatore from operatori where login_operatore = 'ROOT') OPER_ULTIMA_VAR
, sysdate DATA_ULTIMA_VAR
, null ID_STORICO_IMPORT
from v_sistemi s
  join v_attivita vatt on vatt.id_sistema = s.id
  join v_dta dta on dta.id_attivita = vatt.id
where s.tipo = 'NODO'
and dta.nome = 'workingGroupCode'
and not exists(
  select 1
  from v_dt_sistemi dts
  where dts.id_sistema = s.id
  and dts.nome = 'workingGroupCode'
  and rownum<2
);

commit;

quit
