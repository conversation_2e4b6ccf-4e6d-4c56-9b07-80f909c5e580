set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

update dati_tecnici_attivita dta
set dta.descrizione = substr(dta.descrizione, 0, 2)
where dta.id_tipo_dato_tecnico_attivita = (
  select tdta.id_tipo_dato_tecnico_attivita
  from tipi_dati_tecnici_attivita tdta
  where tdta.descrizione = 'ring'
)
;

update dati_tecnici dt
set dt.descrizione = substr(dt.descrizione, 0, 2)
where dt.id_tipo_dato_tecnico = (
  select tdt.id_tipo_dato_tecnico
  from tipi_dati_tecnici tdt
  where tdt.nome = 'ring'
)
;

show errors

commit;

quit
