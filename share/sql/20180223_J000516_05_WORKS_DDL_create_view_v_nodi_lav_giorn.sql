set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_NODI_LAV_GIORN AS 
  select estraz."teamId"
  , ESTRAZ.DATA_ESECUZIONE "executionDate"
  , estraz."networkElementId"
  , estraz."spentTime"
  , estraz."eventType"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='recoveryCable' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "recoveryCable"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='infrastructureCheck' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "infrastructureCheck"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='plannedBreakdown' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "plannedBreakdown"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='interruptedMinitubes' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "interruptedMinitubes"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='branchesCut' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "branchesCut"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pilingCheck' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pilingCheck"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable144foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "cable144foConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable24foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "cable24foConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable48foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "cable48foConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable96foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "cable96foConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cableOver192foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "cableOver192foConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='foJunction' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "foJunction"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfpMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pfpMeasure"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfsMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pfsMeasure"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='ptaMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "ptaMeasure"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pteMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pteMeasure"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitterPermutations' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "splitterPermutations"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitter116Placing' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "splitter116Placing"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitter14Placing' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "splitter14Placing"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='terminations' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "terminations"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='continuousCablesConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "continuousCablesConnection"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='fibersPlacedJunction' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "fibersPlacedJunction"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='prewiredPFS' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "prewiredPFS"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfsPosition' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pfsPosition"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='junctionType' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "junctionType"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='junctionSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "junctionSite"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='ptaSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "ptaSite"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pteSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "pteSite"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='lackOfMaterial' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "lackOfMaterial"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='notAccessibleCockpit' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "notAccessibleCockpit"
    , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='notAccessibleCockpit' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "emptyingCockpit"
From (
with es as(
select v.id
  , dta.valore "teamId"
  , vsa.data_esecuzione
  , dts.valore "networkElementId"
  , (
      select dtats.valore
      from v_storia_attivita vsats
        join v_dta dtats on dtats.id_attivita = vsats.id_attivita and dtats.data_esecuzione = vsats.data_Esecuzione and dtats.nome = 'spentTime'
      where 1=1
      and vsats.id_attivita = v.id
      and vsats.data_esecuzione = vsa.data_esecuzione
      and vsats.azione = vsa.azione
      and vsats.data_esecuzione = (
        select max(vsats2.data_esecuzione)
        from v_storia_attivita vsats2
        where 1=1
        and vsats2.azione = vsats.azione
        and vsats2.id_attivita = vsats.id_Attivita
        and vsats2.data_esecuzione between trunc(vsats.data_esecuzione) and trunc(vsats.data_esecuzione)+1
      )
    )"currentSpentTime"
    , (
      select dtats.valore
      from v_dta dtats
      where dtats.id_attivita = v.id
      and dtats.nome = 'spentTime'
      and dtats.data_esecuzione = (
        select max(dtats2.data_esecuzione)
        from v_dta dtats2
        where 1=1
        and dtats2.id_attivita = dtats.id_Attivita
        and dtats2.nome = dtats.nome
        and dtats2.data_esecuzione < trunc(vsa.data_esecuzione)
      )
    )"previousSpentTime"
    ,(
      select dtats.valore
      from v_storia_attivita vsats
        join v_dta dtats on dtats.id_attivita = vsats.id_attivita and dtats.data_esecuzione = vsats.data_Esecuzione and dtats.nome = 'eventType'
      where 1=1
      and vsats.id_attivita = v.id
      and vsats.data_esecuzione = vsa.data_esecuzione
      and vsats.azione = vsa.azione
      and vsats.data_esecuzione = (
        select max(vsats2.data_esecuzione)
        from v_storia_attivita vsats2
        where 1=1
        and vsats2.azione = vsats.azione
        and vsats2.id_attivita = vsats.id_Attivita
        and vsats2.data_esecuzione between trunc(vsats.data_esecuzione) and trunc(vsats.data_esecuzione)+1
      )
    )"eventType"
from v_attivita v
  join v_sistemi s on s.id = v.id_Sistema
  join v_storia_attivita vsa on vsa.id_attivita = v.id and vsa.azione = 'AVANZAMENTO'
  join v_dta dta on dta.id_attivita = v.id and dta.data_esecuzione = vsa.data_esecuzione and dta.nome = 'teamId'
  join v_dta dta2 on dta2.id_attivita = v.id and dta2.data_esecuzione = vsa.data_esecuzione and dta2.nome = 'eventType'
  join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'networkElementId'
where 1=1
  and v.nome_tipo_Attivita = 'LAVORO'
  and s.tipo = 'NODO'
  and dta2.valore in ('COMPLETE','PARTIAL_COMPLETE','NOT_DONE')
)
select id
  ,"teamId"
  ,trunc(data_esecuzione) data_esecuzione
  ,"networkElementId"
  , sum ((nvl(es."currentSpentTime",0)- nvl(es."previousSpentTime",0))) "spentTime"
  ,"eventType"
From es
group by id
  ,"teamId"
  ,trunc(data_esecuzione)
  ,"networkElementId"
  ,"eventType"
)estraz
;

grant select on v_nodi_lav_giorn to works_rpt with grant option;

quit
