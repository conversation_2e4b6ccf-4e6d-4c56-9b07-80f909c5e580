set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_CAVI AS 
  select
   s."customerId"
  ,s."contractId"
  ,to_char(s."projectId") "projectId"
  ,c."cableId"
  ,c."cableName"
  ,c."cableType"
  ,c."potential"
  ,c."unitOfMeasure"
  ,c."fromNetworkElementId"
  ,c."fromNetworkElementName"
  ,c."fromNetworkElementType"
  ,c."fromNetworkElementGeoLocation"
  ,c."toNetworkElementId"
  ,c."toNetworkElementName"
  ,c."toNetworkElementType"
  ,c."toNetworkElementGeoLocation"
  ,c."UI"
  ,c."horizontalLength"
  ,c."verticalLength"
  ,c."fromStockLength"
  ,c."toStockLength"
  ,c."plannedUndergroundLength"
  ,c."plannedAerialLength"
  ,c."plannedFacadeLength"
  ,c."status"
  ,c."doneUndergroundLength"
  ,c."doneAerialLength"
  ,c."doneFacadeLength"
  ,c."doneHandmaidLength"
  ,c."estimatedDuration"
from CAVI c
  join sync_runs s on s."runId" = c."runId" and c."historicizingRunId" is null
;

CREATE OR REPLACE VIEW V_CAVI_LAVORI_DETTAGLIO AS 
  select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,cables."cableId"
  ,cables."cableName"
  ,cables."status"
  ,cables."fromNetworkElementName"
  ,cables."fromNetworkElementType"
  ,cables."toNetworkElementName"
  ,cables."toNetworkElementType"
  ,cables."cableType"
  ,cables."potential"
  ,cables."unitOfMeasure"
  ,cables."fromNetworkElementId"
  ,cables."fromNetworkElementGeoLocation"
  ,cables."toNetworkElementId"
  ,cables."toNetworkElementGeoLocation"
  ,cables."UI"
  ,cables."horizontalLength"
  ,cables."verticalLength"
  ,cables."fromStockLength"
  ,cables."toStockLength"
  ,cables."plannedUndergroundLength"
  ,cables."plannedAerialLength"
  ,cables."plannedFacadeLength"
  , case when cables."doneUndergroundLength" is null and cables."doneHandmaidLength" is null then null
    else nvl(cables."doneUndergroundLength",0) + nvl(cables."doneHandmaidLength",0)
    end "doneUndergroundLength"
  ,cables."doneAerialLength"
  ,cables."doneFacadeLength"
  ,cables."estimatedDuration"
  ,lav."id" "activityId"
  ,lav."status" "activityStatus"
  ,lav."maker" "activityMaker"
  ,lav."teamId" "activityTeamId"
  ,lav."startPlannedDate" "activityStartPlannedDate"
  ,lav."startWorkDate" "activityStartWorkDate"
  ,lav."endPlannedDate" "activityEndPlannedDate"
  ,lav."estimatedDuration" "activityEstimatedDuration"
  ,lav."endWorkDate" "activityEndWorkDate"
  ,lav."spentTime" "activitySpentTime"
  ,lav."subContractCode" "activitySubContractCode"
  ,lav."techninalAssistantsAssignee" "activityTechAssAssignee"
  ,lav."asBuiltStatus" "activityAsBuiltStatus"
  ,case
    when cables."status" in ('Planned') and lav."status" is null then 'notWorkable'
    when cables."status" in ('Workable') and lav."status" is null then 'workable'
    when cables."status" in ('Done') then 'sinfoUpdated' -- NB: se l'attivita non esiste sarebbe un warning...
    when cables."status" in ('Accounted') then 'accounted' -- NB: se l'attivita non esiste sarebbe un warning...
    when
         (lav."status" in ('APERTA','NUOVO','INOLTRATO_SIRTI'))
      or (lav."status" = 'IN_LAVORAZIONE_SIRTI' and (lav."teamId" is null or lav."startPlannedDate" is null))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO' and lav."subContractCode" is null) -- NB: solo per garantire la fase di transizione
    then 'notPlanned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is null)
    then 'planned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and ((lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is not null)))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO') -- NB: volutamente viene ignorato il valore della lav."startPlannedDate"
    then 'workInProgress'
    when lav."status" = 'ATTESA_CARICAMENTO_AS_BUILT' then 'workEnded'
    when lav."status" = 'ATTESA_GESTIONE_AS_BUILT' then 'asBuiltNotWorked'
    when lav."status" in ('ESPLETATO','QUARANTENA') and cables."status" = 'Workable' then 'asBuiltWorked'
    when lav."status" in ('ANOMALIA','ASSURANCE_ANNULLAMENTO') then 'assurance'
    when lav."status" in ('ANNULLATO','ATTESA_ANNULLAMENTO') then 'cancelled'
    else null
  end "virtualStatus"
from
  v_cavi cables
    join v_progetti pfp on cables."customerId" = pfp."customerId" and cables."contractId" = pfp."contractId" and cables."projectId" = pfp."projectId"
    left join v_cavi_lavori lav on lav."cableId" = cables."cableId" and lav."customerId" = pfp."customerId" and lav."contractId" = pfp."contractId" and lav."projectId" =  pfp."projectId"
;

quit
