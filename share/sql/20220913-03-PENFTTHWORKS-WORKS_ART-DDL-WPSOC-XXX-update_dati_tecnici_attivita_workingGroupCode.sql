set line 1000
set autocommit off
set echo on
set SERVEROUT off

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

-- Migrazione non condizionale (PTE e Manutenzioni)
update dati_tecnici_attivita dta
set dta.descrizione = nvl((select m.new_cdl from works.mappa_cdl m where m.old_cdl = dta.descrizione), dta.descrizione)
where dta.id_attivita in (
    select  a.id
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'PTE', 'CorrMaintenance','ExtrMaintenance','PreeMaintenance' )
) and dta.id_tipo_dato_tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_Dati_Tecnici_attivita where descrizione = 'workingGroupCode')
;

-- Migrazione condizionale (Network)
update dati_tecnici_attivita dta
set dta.descrizione = nvl((select m.new_cdl from works.mappa_cdl m where m.old_cdl = dta.descrizione), dta.descrizione)
where dta.id_attivita in (
    select  a.id
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    join    v_dt_sistemi nid
    on      nid.id_sistema = a.id_sistema
    and     nid.nome = 'networkId'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'Network', 'NetworkFibercop' )
    and     exists ( select 1 from works.network_da_migrare where "networkId" = nid.valore )
) and dta.id_tipo_dato_tecnico_attivita = (select id_tipo_Dato_Tecnico_attivita from tipi_Dati_Tecnici_attivita where descrizione = 'workingGroupCode')
;

show errors

commit;

quit
