set line 1000
set autocommit off
set echo on
set SER<PERSON>ROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

create synonym nodi_riepilogo for works.nodi_riepilogo;

create synonym CONF_NODI_POSA_MECCANICA for works.CONF_NODI_POSA_MECCANICA;
create synonym CONF_NODI_ATTESTAZIONE for works.CONF_NODI_ATTESTAZIONE;
create synonym CONF_NODI_TIPO_CABLAGGIO for works.CONF_NODI_TIPO_CABLAGGIO;
create synonym CONF_NODI_CAVO_SPILLATO for works.CONF_NODI_CAVO_SPILLATO;
create synonym CONF_NODI_TEMPO_TOTALE for works.CONF_NODI_TEMPO_TOTALE;


quit
