set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

alter table CONF_NODI_ATTESTAZIONE rename column "manHours" TO "manMinutes";
alter table CONF_NODI_CAVO_SPILLATO rename column "manHours" TO "manMinutes";
alter table CONF_NODI_POSA_MECCANICA rename column "manHours" TO "manMinutes";
alter table CONF_NODI_TIPO_CABLAGGIO rename column "manHours" TO "manMinutes";
alter table CONF_NODI_TEMPO_TOTALE rename column "manHours" TO "manMinutes";

COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_CAVO_SPILLATO."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_POSA_MECCANICA."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_TIPO_CABLAGGIO."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_TEMPO_TOTALE."manMinutes" IS 'Minuti/uomo stimati per l''intervento';

COMMENT ON COLUMN CAVI."estimatedDuration" IS 'Durata stimata della posa cavi (minuti)';
COMMENT ON COLUMN NODI_RIEPILOGO."estimatedDuration" IS 'Durata stimata dell''esecuzione delle giunzioni attestate sull''elemento di rete (minuti)';
COMMENT ON COLUMN MACROTASKS."estimatedDuration" IS 'Durata stimata del macrotask (minuti)';

quit
