set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

SET DEFINE OFF;
delete cfg_field_service;
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('NetworkFibercop','laying','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('NetworkFibercop','restoration','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('NetworkFibercop','civil','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','survey','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','infrastructure','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','laying','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','junction','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','testApp','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','testOTDR','1','NFS','Y');
Insert into WORKS.CFG_FIELD_SERVICE ("workType",TIPO_DATO_TECNICO_ATTIVITA,VALORE_TDTA,FIELD_SERVICE,ENABLED) values ('PTE','restoration','1','NFS','Y');

show errors

commit;

quit
