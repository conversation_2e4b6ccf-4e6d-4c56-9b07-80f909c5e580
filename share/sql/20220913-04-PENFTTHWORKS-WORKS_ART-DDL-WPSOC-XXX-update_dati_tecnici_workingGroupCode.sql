set line 1000
set autocommit off
set echo on
set SERVEROUT off

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

-- Migrazione non condizionale (PTE e Manutenzioni)
update dati_Tecnici dt
set dt.descrizione = nvl((select m.new_cdl from works.mappa_cdl m where m.old_cdl = dt.descrizione), dt.descrizione)
where dt.id_Sistema in (
    select  a.id_sistema
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'PTE', 'CorrMaintenance','ExtrMaintenance','PreeMaintenance' )
) and dt.id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode')
;

-- Migrazione condizionale (Network)
update dati_Tecnici dt
set dt.descrizione = nvl((select m.new_cdl from works.mappa_cdl m where m.old_cdl = dt.descrizione), dt.descrizione)
where dt.id_Sistema in (
    select  a.id_sistema
    from    v_attivita a
    join    v_dt_sistemi rt
    on      rt.id_sistema = a.id_sistema
    and     rt.nome = 'requestType'
    join    v_dt_sistemi nid
    on      nid.id_sistema = a.id_sistema
    and     nid.nome = 'networkId'
    where   a.nome_tipo_attivita = 'LAVORO'
    and     rt.valore in ( 'Network', 'NetworkFibercop' )
    and     exists ( select 1 from works.network_da_migrare where "networkId" = nid.valore )
) and dt.id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode')
;

show errors

commit;

quit
