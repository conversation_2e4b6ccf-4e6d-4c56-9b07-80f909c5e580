set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

ALTER TABLE MACROTASKS RENAME COLUMN "accountedQuantity" TO "accountableQuantity";

COMMENT ON COLUMN MACROTASKS."accountableQuantity" IS 'Quantità contabilizzabile. NOTA: In ambiente Sinfo, "contabilizzato" consiste nell''associazione di almeno un item di contabilità ad un asset di progetto, indipendentemente dal fatto che questo generi effettiva "Contabilità". Il valore rappresenta la quota-parte (<=) della quantità di lavoro realizzato';

COMMENT ON COLUMN CAVI."status" IS 'Indica lo stato SiNFO del cavo (valori possibili ''Planned'', ''Workable'', ''Done'', ''Accountable''). NOTA: il valore ''Accountable'' corrisponde al valore ''SinfoAccounted'' restituito da SiNFO';

COMMENT ON COLUMN NODI."status" IS 'Indica lo stato SiNFO della giunzione (valori possibili ''Planned'', ''Workable'', ''Done'', ''Accountable''). NOTA: il valore ''Accountable'' corrisponde al valore ''SinfoAccounted'' restituito da SiNFO';

ALTER TABLE NODI_RIEPILOGO RENAME COLUMN "accounted" TO "accountable";

COMMENT ON COLUMN NODI_RIEPILOGO."accountable" IS 'Totale delle giunzioni contabilizzabili attestate sull''elemento di rete';

quit
