set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever o<PERSON><PERSON>r  exit 2 rollback;

SET DEFINE OFF;
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106226','496226');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106228','496228');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106229','496229');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106253','496253');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106256','496256');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106257','496257');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106264','496264');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106266','496266');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106270','496270');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106285','496285');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106286','496286');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106411','496411');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106412','496412');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106413','496413');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106414','496414');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106415','496415');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106416','496416');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106417','496417');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106426','496426');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106428','496428');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106429','496429');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106439','496439');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106454','496454');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106455','496455');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106457','496457');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106466','496466');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106470','496470');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106471','496471');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106480','496480');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106481','496481');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106485','496485');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106486','496486');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106487','496487');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106611','496611');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106626','496626');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106628','496628');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106636','496636');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106646','496646');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106649','496649');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106653','496653');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106654','496654');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106656','496656');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106657','496657');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106661','496661');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106665','496665');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106670','496670');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106671','496671');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106672','496672');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106673','496673');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106680','496680');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106681','496681');
Insert into WORKS.MAPPA_CDL (OLD_CDL,NEW_CDL) values ('106211','496211');

show errors

commit;

quit
