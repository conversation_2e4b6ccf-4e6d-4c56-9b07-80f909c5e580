set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off


whenever oserror exit 1 rollback;
whenever sqlerror exit 2 rollback;


 declare
     sq varchar(4000) := '';
     c pls_integer;
     cur flat_dta.generic_cursor;
     d  v_dmlgen_dtaflat.d%type;
 begin
     sq := '';
     open cur for
         select d  from  v_dmlgen_dtaflat
         where id_tipo_attivita=(select id_tipo_attivita from v_ta_dtaflat  where nome_tipo_attivita='LAVORO')
         order by id_tipo_attivita,line_seq
     ;
     loop
         fetch cur into d;
         exit when cur%notfound;
         sq := sq || ' ' || d;
     end loop;
     close cur;
     dbms_output.put_line(sq);
     execute immediate sq;
     
 end;
/

 quit
