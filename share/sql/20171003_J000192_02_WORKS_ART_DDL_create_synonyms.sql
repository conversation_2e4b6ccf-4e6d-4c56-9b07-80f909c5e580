set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

accept user default 'works.' prompt 'dammi nome utente a cui concedere i permessi  (default works.) '

create or replace synonym MACRO<PERSON>SKS for &user.MACROTASKS;
create or replace synonym <PERSON>RO<PERSON>S<PERSON>_RUN for &user.MACROTASKS_RUN;
create or replace synonym SEQ_MACROTASKS_RUN for &user.SEQ_MACROTASKS_RUN;

quit
