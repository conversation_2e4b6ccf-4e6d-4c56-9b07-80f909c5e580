set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view V_MACROTASKS_RUN ("runId", "customerId", "contractId", "projectId", "runDate", "sinfoLastModified") as
select
    "runId"
  , "customerId"
  , "contractId"
  , "projectId"
  , "runDate"
  , "sinfoLastModified"
from sync_runs
where "syncType" = 'MACROTASKS'
;

create or replace synonym MACROTASKS_RUN for V_MACROTASKS_RUN;

quit
