set line 1000
set autocommit off
set echo on
set SER<PERSON><PERSON>UT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop table <PERSON>ROTASKS;
-- drop table MACROTASKS_RUN;
-- drop sequence SEQ_MACROTASKS_RUN;

create sequence SEQ_MACROTASKS_RUN START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

CREATE TABLE MACROTASKS_RUN
(
    "runId" NUMBER not null
  , "customerId" varchar(20) not null
  , "contractId" varchar(20) not null
  , "projectId" NUMBER not null
  , "runDate" DATE not null
  , "sinfoLastModified" DATE not null
  , CONSTRAINT MACROTASKS_RUN_PK PRIMARY KEY ( "runId" )
);

CREATE INDEX MACROTASKS_RUN_IDX1 ON MACROTASKS_RUN ("customerId", "contractId", "projectId", "sinfoLastModified");

ALTER TABLE MACROTASKS_RUN
ADD CONSTRAINT MACROTASKS_RUN_UK1 UNIQUE 
(
  "customerId", "contractId", "projectId", "runDate"
)
ENABLE;

COMMENT ON TABLE MACROTASKS_RUN IS 'Elenco dei campionamenti effettuati';

COMMENT ON COLUMN MACROTASKS_RUN."runId" IS 'Id univoco del campionamento';
COMMENT ON COLUMN MACROTASKS_RUN."customerId" IS 'Cliente a cui si riferisce il campionamento';
COMMENT ON COLUMN MACROTASKS_RUN."contractId" IS 'Contratto a cui si riferisce il campionamento';
COMMENT ON COLUMN MACROTASKS_RUN."projectId" IS 'Progetto a cui si riferisce il campionamento';
COMMENT ON COLUMN MACROTASKS_RUN."runDate" IS 'Data del campionamento';
COMMENT ON COLUMN MACROTASKS_RUN."sinfoLastModified" IS 'Data di ultima modifica dei macrotask restituita da SinfoWeb. Se uguale a quella indicata nel precedente campionamento non bisogna registrare i dati dei macrotask';

CREATE TABLE MACROTASKS
(
    "permitsAreaId" NUMBER
  , "workZoneid" NUMBER
  , "buildingId" NUMBER
  , "type" VARCHAR(20) not null
  , "category" VARCHAR(256) not null
  , "subCategory" VARCHAR(256)
  , "unitOfMeasure" VARCHAR(20) not null
  , "toDoQuantity" FLOAT not null
  , "doneQuantity" FLOAT not null
  , "duration" FLOAT not null
  , "runId" NUMBER not null
  , "historicizingRunId" NUMBER
);

ALTER TABLE MACROTASKS
ADD CONSTRAINT MACROTASKS_FK1 FOREIGN KEY
(
  "runId" 
)
REFERENCES MACROTASKS_RUN
(
  "runId" 
)
ENABLE;

ALTER TABLE MACROTASKS
ADD CONSTRAINT MACROTASKS_FK2 FOREIGN KEY
(
  "historicizingRunId" 
)
REFERENCES MACROTASKS_RUN
(
  "runId" 
)
ENABLE;

COMMENT ON TABLE MACROTASKS IS 'Elenco dei macrotask';

COMMENT ON COLUMN MACROTASKS."permitsAreaId" IS 'Area permessi a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."workZoneid" IS 'WorkZone a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."buildingId" IS 'Ui a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."type" IS 'Tipo di macrotask';
COMMENT ON COLUMN MACROTASKS."category" IS 'Categoria del macrotask';
COMMENT ON COLUMN MACROTASKS."subCategory" IS 'Sottocategoria del macrotask';
COMMENT ON COLUMN MACROTASKS."unitOfMeasure" IS 'Unità di misura';
COMMENT ON COLUMN MACROTASKS."toDoQuantity" IS 'Quantità da realizzare';
COMMENT ON COLUMN MACROTASKS."doneQuantity" IS 'Quantità realizzata';
COMMENT ON COLUMN MACROTASKS."duration" IS 'Durata stimata del macrotask';
COMMENT ON COLUMN MACROTASKS."runId" IS 'Id del campionamento';
COMMENT ON COLUMN MACROTASKS."historicizingRunId" IS 'Id del campionamento che ha sostituito quello indicato nella riga, se null si tratta del campionamento più recente';

quit
