
-- INIZ<PERSON> GLOBALE

-- Non modificare manualmente ma usare gen_dataconfig.sql

set echo on
set sqlblankline on
spool 20191003_J000000_00_WORKS_ART_DML_dataconfig.log
set define off

-- VERIFICO ESISTENZA COLONNA NUOVE FUNZIONALITA

DECLARE
 C NUMBER := 0;
 q VARCHAR2(400) := 'ALTER TABLE ACTION ADD interfaccia VARCHAR2(4000)';
 BEGIN
 SELECT NVL(
 (SELECT 1  FROM USER_TAB_COLUMNS UTC WHERE UTC.TABLE_NAME = 'ACTION' AND UTC.COLUMN_NAME = 'INTERFACCIA'),0)
 INTO C from dual;
 IF (C=0) THEN
 	execute immediate q;
 end if;
 end;
/

-- TIPI_SISTEMA

INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'Gestione della struttura albero documentale',NULL,'ALBERO DOCUMENTALE',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'Tipo sistema su cui effettuare il test di regressione','Y','API::TEST',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'CANTIERE','Y','CANTIERE',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'CAVO','Y','CAVO',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'GIUNZIONE','Y','GIUNZIONE',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'Sistema di prova per migrazione','Y','kart',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'KART_HISTORY','Y','KART_HISTORY',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'kart1','Y','kart1',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'kart2','Y','kart2',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'LAVORO','Y','LAVORO',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'MACROLAVORO','Y','MACROLAVORO',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'NETWORK','Y','NETWORK',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'NODO','Y','NODO',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'PROJECT','Y','PROJECT',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'ROE','Y','ROE',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'SQUADRA','Y','SQUADRA',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'SUBAPPALTO','Y','SUBAPPALTO',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'TERMINAZIONE','Y','TERMINAZIONE',NULL,NULL);
INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval,'Gestione Work Zone','Y','WZ',NULL,NULL);

UPDATE TIPI_SISTEMA set DESCRIZIONE='Sistema di prova per migrazione',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='kart',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'kart';
UPDATE TIPI_SISTEMA set DESCRIZIONE='kart1',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='kart1',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'kart1';
UPDATE TIPI_SISTEMA set DESCRIZIONE='kart2',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='kart2',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'kart2';
UPDATE TIPI_SISTEMA set DESCRIZIONE='Gestione della struttura albero documentale',MANUTENIBILE=NULL,NOME_TIPO_SISTEMA='ALBERO DOCUMENTALE',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'ALBERO DOCUMENTALE';
UPDATE TIPI_SISTEMA set DESCRIZIONE='Tipo sistema su cui effettuare il test di regressione',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='API::TEST',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'API::TEST';
UPDATE TIPI_SISTEMA set DESCRIZIONE='KART_HISTORY',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='KART_HISTORY',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'KART_HISTORY';
UPDATE TIPI_SISTEMA set DESCRIZIONE='Gestione Work Zone',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='WZ',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'WZ';
UPDATE TIPI_SISTEMA set DESCRIZIONE='SUBAPPALTO',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='SUBAPPALTO',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'SUBAPPALTO';
UPDATE TIPI_SISTEMA set DESCRIZIONE='CANTIERE',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='CANTIERE',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'CANTIERE';
UPDATE TIPI_SISTEMA set DESCRIZIONE='MACROLAVORO',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='MACROLAVORO',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'MACROLAVORO';
UPDATE TIPI_SISTEMA set DESCRIZIONE='GIUNZIONE',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='GIUNZIONE',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'GIUNZIONE';
UPDATE TIPI_SISTEMA set DESCRIZIONE='NETWORK',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='NETWORK',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'NETWORK';
UPDATE TIPI_SISTEMA set DESCRIZIONE='NODO',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='NODO',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'NODO';
UPDATE TIPI_SISTEMA set DESCRIZIONE='LAVORO',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='LAVORO',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'LAVORO';
UPDATE TIPI_SISTEMA set DESCRIZIONE='PROJECT',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='PROJECT',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'PROJECT';
UPDATE TIPI_SISTEMA set DESCRIZIONE='CAVO',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='CAVO',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'CAVO';
UPDATE TIPI_SISTEMA set DESCRIZIONE='SQUADRA',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='SQUADRA',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'SQUADRA';
UPDATE TIPI_SISTEMA set DESCRIZIONE='TERMINAZIONE',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='TERMINAZIONE',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'TERMINAZIONE';
UPDATE TIPI_SISTEMA set DESCRIZIONE='ROE',MANUTENIBILE='Y',NOME_TIPO_SISTEMA='ROE',PREFISSO_IMPORT_AUTOMATICO=NULL,TOTALE=NULL where NOME_TIPO_SISTEMA= 'ROE';

-- TIPI_DATI_TECNICI

INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'accountingQuantity','1','1','N','N',NULL,NULL,NULL,'accountingQuantity',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'tdt alfa','1','1','N','N',NULL,NULL,NULL,'alfa',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'ameliaId','1','1','N','N',NULL,NULL,NULL,'ameliaId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'API_TDT_01','1','1','N','N',NULL,NULL,NULL,'API_TDT_01',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'API_TDT_02','1','1','N','N',NULL,NULL,NULL,'API_TDT_02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'API_TDT_03','1','1','N','N',NULL,NULL,NULL,'API_TDT_03',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'assetId','1','1','N','Y',NULL,NULL,NULL,'assetId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'assistantNote','1','1','N','N',NULL,NULL,NULL,'assistantNote',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'tdt beta','1','1','N','N',NULL,NULL,NULL,'beta',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'branchesCut','1','1','N','N',NULL,NULL,NULL,'branchesCut',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'businessProject','1','1','N','N',NULL,NULL,NULL,'businessProject',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'businessProjectDesc','1','1','N','N',NULL,NULL,NULL,'businessProjectDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'buyer','1','1','N','N',NULL,NULL,NULL,'buyer',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'buyerDesc','1','1','N','N',NULL,NULL,NULL,'buyerDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cableId','1','1','N','N',NULL,NULL,NULL,'cableId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Posa Cavo','1','1','N','N',NULL,NULL,NULL,'cableLaying',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cableName','1','1','N','N',NULL,NULL,NULL,'cableName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cableOver192foConnection','1','1','N','N',NULL,NULL,NULL,'cableOver192foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Tipologia cavo','1','1','N','N',NULL,NULL,NULL,'cableType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cable144foConnection','1','1','N','N',NULL,NULL,NULL,'cable144foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cable192foConnection','1','1','N','N',NULL,NULL,NULL,'cable192foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cable24foConnection','1','1','N','N',NULL,NULL,NULL,'cable24foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cable48foConnection','1','1','N','N',NULL,NULL,NULL,'cable48foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cable96foConnection','1','1','N','N',NULL,NULL,NULL,'cable96foConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cablingType','1','1','N','N',NULL,NULL,NULL,'cablingType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cadastralCode','1','1','N','N',NULL,NULL,NULL,'cadastralCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'canOpenPermits','1','1','N','N',NULL,NULL,NULL,'canOpenPermits',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'canOpenWorks','1','1','N','N',NULL,NULL,NULL,'canOpenWorks',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'category','1','1','N','N',NULL,NULL,NULL,'category',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'centralId','1','1','N','N',NULL,NULL,NULL,'centralId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'centralPoint','1','1','N','N',NULL,NULL,NULL,'centralPoint',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'city','1','1','N','N',NULL,NULL,NULL,'city',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'cityId','1','1','N','N',NULL,NULL,NULL,'cityId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Lavori Civili','1','1','N','N',NULL,NULL,NULL,'civil',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'continuousCablesConnection','1','1','N','N',NULL,NULL,NULL,'continuousCablesConnection',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'contractId','1','1','N','N',NULL,NULL,NULL,'contractId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'country','1','1','N','N',NULL,NULL,NULL,'country',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerGoodsTotalAmount','1','1','N','N',NULL,NULL,NULL,'customerGoodsTotalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerId','1','1','N','N',NULL,NULL,NULL,'customerId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerProjectType','1','1','N','N',NULL,NULL,NULL,'customerProjectType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerProjectTypeDesc','1','1','N','N',NULL,NULL,NULL,'customerProjectTypeDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerTechnicalAssistant','1','1','N','N',NULL,NULL,NULL,'customerTechnicalAssistant',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerTechnicalAssistantDesc','1','1','N','N',NULL,NULL,NULL,'customerTechnicalAssistantDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerWBE','1','1','N','N',NULL,NULL,NULL,'customerWBE',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerWBEDesc','1','1','N','N',NULL,NULL,NULL,'customerWBEDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'customerWorkingArea','1','1','N','N',NULL,NULL,NULL,'customerWorkingArea',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Guasto Disservito','1','1','N','N',NULL,NULL,NULL,'defectWithDisservice',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Guasto Non Disservito','1','1','N','N',NULL,NULL,NULL,'defectWithoutDisservice',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Progettazione','1','1','N','N',NULL,NULL,NULL,'design',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'FILE DOCUMENTO','1','1','N','N',NULL,NULL,NULL,'DOCUMENTO',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneAerialLength','1','1','N','N',NULL,NULL,NULL,'doneAerialLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneAerialLengthV','1','1','N','N',NULL,NULL,NULL,'doneAerialLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneFacadeLength','1','1','N','N',NULL,NULL,NULL,'doneFacadeLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneFacadeLengthV','1','1','N','N',NULL,NULL,NULL,'doneFacadeLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneFromStockLength','1','1','N','N',NULL,NULL,NULL,'doneFromStockLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneFromStockLengthV','1','1','N','N',NULL,NULL,NULL,'doneFromStockLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneHandmaidLength','1','1','N','N',NULL,NULL,NULL,'doneHandmaidLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneHandmaidLengthV','1','1','N','N',NULL,NULL,NULL,'doneHandmaidLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneToStockLength','1','1','N','N',NULL,NULL,NULL,'doneToStockLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneToStockLengthV','1','1','N','N',NULL,NULL,NULL,'doneToStockLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneUndergroundLength','1','1','N','N',NULL,NULL,NULL,'doneUndergroundLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'doneUndergroundLengthV','1','1','N','N',NULL,NULL,NULL,'doneUndergroundLengthV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'emptyingCockpit','1','1','N','N',NULL,NULL,NULL,'emptyingCockpit',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'endPlannedDate','1','1','N','N',NULL,NULL,NULL,'endPlannedDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'endWorkDate','1','1','N','N',NULL,NULL,NULL,'endWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'enhancementFactorDesc','1','1','N','N',NULL,NULL,NULL,'enhancementFactorDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Durata stimata dell''attività espressa in ore','1','1','N','N',NULL,NULL,NULL,'estimatedDuration',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'externalSequence','1','1','N','N',NULL,NULL,NULL,'externalSequence',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'fibersPlacedJunction','1','1','N','N',NULL,NULL,NULL,'fibersPlacedJunction',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'foJunction','1','1','N','N',NULL,NULL,NULL,'foJunction',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'forecastEndDate','1','1','N','N',NULL,NULL,NULL,'forecastEndDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'forecastStartDate','1','1','N','N',NULL,NULL,NULL,'forecastStartDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'fromNetworkElementGeoLocation','1','1','N','N',NULL,NULL,NULL,'fromNetworkElementGeoLocation',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Identificativo SiNFO dell''elemento di rete di partenza','1','1','N','N',NULL,NULL,NULL,'fromNetworkElementId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Identificativo mnemonico (nome) dell''elemento di rete di partenza','1','1','N','N',NULL,NULL,NULL,'fromNetworkElementName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'fromNetworkElementType','1','1','N','N',NULL,NULL,NULL,'fromNetworkElementType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'goodsRecipient','1','1','N','N',NULL,NULL,NULL,'goodsRecipient',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'goodsSupplier','1','1','N','N',NULL,NULL,NULL,'goodsSupplier',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'goodsSupplierDesc','1','1','N','N',NULL,NULL,NULL,'goodsSupplierDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Infrastruttura','1','1','N','N',NULL,NULL,NULL,'infrastructure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'infrastructureCheck','1','1','N','N',NULL,NULL,NULL,'infrastructureCheck',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'inputCableId','1','1','N','N',NULL,NULL,NULL,'inputCableId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'inputCableName','1','1','N','N',NULL,NULL,NULL,'inputCableName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'inputCablePotential','1','1','N','N',NULL,NULL,NULL,'inputCablePotential',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'inputCablePotentialPlanned','1','1','N','N',NULL,NULL,NULL,'inputCablePotentialPlanned',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Ispezione Sede Posa','1','1','N','N',NULL,NULL,NULL,'installationPlaceSurvey',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Revisione Impianti','1','1','N','N',NULL,NULL,NULL,'installationReview',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'interruptedMinitubes','1','1','N','N',NULL,NULL,NULL,'interruptedMinitubes',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Riferimento ISO','1','1','N','N',NULL,NULL,NULL,'isoReference',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Giunzione','1','1','N','N',NULL,NULL,NULL,'junction',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'junctionSite','1','1','N','N',NULL,NULL,NULL,'junctionSite',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'junctionType','1','1','N','N',NULL,NULL,NULL,'junctionType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'lackOfMaterial','1','1','N','N',NULL,NULL,NULL,'lackOfMaterial',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'latitude','1','1','N','N',NULL,NULL,NULL,'latitude',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'longitudelongitude','1','1','N','N',NULL,NULL,NULL,'longitude',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'maintenanceId','1','1','N','N',NULL,NULL,NULL,'maintenanceId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Misure','1','1','N','N',NULL,NULL,NULL,'measurements',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'messageId','1','1','N','N',NULL,NULL,NULL,'messageId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'MOSTotalAmount','1','1','N','N',NULL,NULL,NULL,'MOSTotalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'name','1','1','N','N',NULL,NULL,NULL,'name',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkDesc','1','1','N','N',NULL,NULL,NULL,'networkDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkElementFulfilled','1','1','N','N',NULL,NULL,NULL,'networkElementFulfilled',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkElementId','1','1','N','N',NULL,NULL,NULL,'networkElementId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkElementIsDone','1','1','N','N',NULL,NULL,NULL,'networkElementIsDone',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkElementName','1','1','N','N',NULL,NULL,NULL,'networkElementName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkElementType','1','1','N','N',NULL,NULL,NULL,'networkElementType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkId','1','1','N','N',NULL,NULL,NULL,'networkId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'networkStatus','1','1','N','N',NULL,NULL,NULL,'networkStatus',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'notAccessibleCockpit','1','1','N','N',NULL,NULL,NULL,'notAccessibleCockpit',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'onFieldAssistant','1','1','N','N',NULL,NULL,NULL,'onFieldAssistant',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'onFieldIntegrationDisabled','1','1','N','N',NULL,NULL,NULL,'onFieldIntegrationDisabled',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'operation','1','1','N','N',NULL,NULL,NULL,'operation',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'operationalContext','1','1','N','N',NULL,NULL,NULL,'operationalContext',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'operationDesc','1','1','N','N',NULL,NULL,NULL,'operationDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'opticalConnectionOLT','1','1','N','N',NULL,NULL,NULL,'opticalConnectionOLT',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'opticalConnectionOSU','1','1','N','N',NULL,NULL,NULL,'opticalConnectionOSU',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'outputCableId','1','1','N','N',NULL,NULL,NULL,'outputCableId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'outputCableName','1','1','N','N',NULL,NULL,NULL,'outputCableName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'outputCablePotential','1','1','N','N',NULL,NULL,NULL,'outputCablePotential',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'outputCablePotentialPlanned','1','1','N','N',NULL,NULL,NULL,'outputCablePotentialPlanned',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Bretellaggio','1','1','N','N',NULL,NULL,NULL,'patchCord',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Visita Tracciato','1','1','N','N',NULL,NULL,NULL,'pathSurvey',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'permitsAreaId','1','1','N','N',NULL,NULL,NULL,'permitsAreaId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pfpId','1','1','N','N',NULL,NULL,NULL,'pfpId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pfpMeasure','1','1','N','N',NULL,NULL,NULL,'pfpMeasure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pfpName','1','1','N','N',NULL,NULL,NULL,'pfpName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pfsMeasure','1','1','N','N',NULL,NULL,NULL,'pfsMeasure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pfsPosition','1','1','N','N',NULL,NULL,NULL,'pfsPosition',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pilingCheck','1','1','N','N',NULL,NULL,NULL,'pilingCheck',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Lunghezza tratta aerea progettata','1','1','N','N',NULL,NULL,NULL,'plannedAerialLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'plannedBreakdown','1','1','N','N',NULL,NULL,NULL,'plannedBreakdown',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Lunghezza tratta su facciata progettata','1','1','N','N',NULL,NULL,NULL,'plannedFacadeLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'plannedFromStockLength','1','1','N','N',NULL,NULL,NULL,'plannedFromStockLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'plannedSplicesAndTermChanges','1','1','N','N',NULL,NULL,NULL,'plannedSplicesAndTermChanges',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'plannedToStockLength','1','1','N','N',NULL,NULL,NULL,'plannedToStockLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Lunghezza tratta sotterranea progettata','1','1','N','N',NULL,NULL,NULL,'plannedUndergroundLength',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'polygon','1','1','N','N',NULL,NULL,NULL,'polygon',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pop','1','1','N','N',NULL,NULL,NULL,'pop',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'popId','1','1','N','N',NULL,NULL,NULL,'popId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'poseTypeChanges','1','1','N','N',NULL,NULL,NULL,'poseTypeChanges',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'poseTypeChangesV','1','1','N','N',NULL,NULL,NULL,'poseTypeChangesV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Potenzialità del cavo','1','1','N','N',NULL,NULL,NULL,'potential',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'prewiredPFS','1','1','N','N',NULL,NULL,NULL,'prewiredPFS',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'privatePermitsClosedKO','1','1','N','N',NULL,NULL,NULL,'privatePermitsClosedKO',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'privatePermitsClosedOK','1','1','N','N',NULL,NULL,NULL,'privatePermitsClosedOK',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'privatePermitsOnGoing','1','1','N','N',NULL,NULL,NULL,'privatePermitsOnGoing',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'privatePermitsTotal','1','1','N','N',NULL,NULL,NULL,'privatePermitsTotal',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'projectId','1','1','N','N',NULL,NULL,NULL,'projectId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'propositionContractId','1','1','N','N',NULL,NULL,NULL,'propositionContractId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'propositionCurrency','1','1','N','N',NULL,NULL,NULL,'propositionCurrency',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'propositionDate','1','1','N','N',NULL,NULL,NULL,'propositionDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'propositionNumber','1','1','N','N',NULL,NULL,NULL,'propositionNumber',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'province','1','1','N','N',NULL,NULL,NULL,'province',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'ptaMeasure','1','1','N','N',NULL,NULL,NULL,'ptaMeasure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'ptaSite','1','1','N','N',NULL,NULL,NULL,'ptaSite',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pteMeasure','1','1','N','N',NULL,NULL,NULL,'pteMeasure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'pteSite','1','1','N','N',NULL,NULL,NULL,'pteSite',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'publicPermitsClosedKO','1','1','N','N',NULL,NULL,NULL,'publicPermitsClosedKO',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'publicPermitsClosedOK','1','1','N','N',NULL,NULL,NULL,'publicPermitsClosedOK',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'publicPermitsOnGoing','1','1','N','N',NULL,NULL,NULL,'publicPermitsOnGoing',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'publicPermitsTotal','1','1','N','N',NULL,NULL,NULL,'publicPermitsTotal',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Riepilogo Trimestre','1','1','N','N',NULL,NULL,NULL,'quarterSummary',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'recoveryCable','1','1','N','N',NULL,NULL,NULL,'recoveryCable',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'requestDate','1','1','N','N',NULL,NULL,NULL,'requestDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'requestorContext','1','1','N','N',NULL,NULL,NULL,'requestorContext',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'requestType','1','1','N','N',NULL,NULL,NULL,'requestType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'resourcesTotalAmount','1','1','N','N',NULL,NULL,NULL,'resourcesTotalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'restoration','1','1','N','N',NULL,NULL,NULL,'restoration',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'ring','1','1','N','N',NULL,NULL,NULL,'ring',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'ringId','1','1','N','N',NULL,NULL,NULL,'ringId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'roeId','1','1','N','N',NULL,NULL,NULL,'roeId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'SAPDivision','1','1','N','N',NULL,NULL,NULL,'SAPDivision',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'SAPDivisionDesc','1','1','N','N',NULL,NULL,NULL,'SAPDivisionDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'servicesTotalAmount','1','1','N','N',NULL,NULL,NULL,'servicesTotalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'spentTime','1','1','N','N',NULL,NULL,NULL,'spentTime',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'splicedFibers','1','1','N','N',NULL,NULL,NULL,'splicedFibers',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'splitterPermutations','1','1','N','N',NULL,NULL,NULL,'splitterPermutations',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'splitter116Placing','1','1','N','N',NULL,NULL,NULL,'splitter116Placing',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'splitter14Placing','1','1','N','N',NULL,NULL,NULL,'splitter14Placing',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'startPlannedDate','1','1','N','N',NULL,NULL,NULL,'startPlannedDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'startWorkDate','1','1','N','N',NULL,NULL,NULL,'startWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'status','1','1','N','N',NULL,NULL,NULL,'status',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'subCategory','1','1','N','N',NULL,NULL,NULL,'subCategory',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'subContractCode','1','1','N','N',NULL,NULL,NULL,'subContractCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'subContractName','1','1','N','N',NULL,NULL,NULL,'subContractName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'summaryNetwork','1','1','N','N',NULL,NULL,NULL,'summaryNetwork',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'supplier','1','1','N','N',NULL,NULL,NULL,'supplier',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'supplierDesc','1','1','N','N',NULL,NULL,NULL,'supplierDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'supplierGoodsTotalAmount','1','1','N','N',NULL,NULL,NULL,'supplierGoodsTotalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Sopralluogo','1','1','N','N',NULL,NULL,NULL,'survey',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'teamId','1','1','N','N',NULL,NULL,NULL,'teamId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'teamName','1','1','N','N',NULL,NULL,NULL,'teamName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'teamNote','1','1','N','N',NULL,NULL,NULL,'teamNote',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'technicalSite','1','1','N','N',NULL,NULL,NULL,'technicalSite',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'technicalSiteDesc','1','1','N','N',NULL,NULL,NULL,'technicalSiteDesc',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'terminatedFibers','1','1','N','N',NULL,NULL,NULL,'terminatedFibers',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'terminations','1','1','N','N',NULL,NULL,NULL,'terminations',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Collaudo','1','1','N','N',NULL,NULL,NULL,'test',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'toAssignQuantity','1','1','N','N',NULL,NULL,NULL,'toAssignQuantity',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'toNetworkElementGeoLocation','1','1','N','N',NULL,NULL,NULL,'toNetworkElementGeoLocation',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Identificativo SiNFO dell''elemento di rete d''arrivo','1','1','N','N',NULL,NULL,NULL,'toNetworkElementId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Identificativo mnemonico (nome) dell''elemento di rete d''arrivo','1','1','N','N',NULL,NULL,NULL,'toNetworkElementName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'toNetworkElementType','1','1','N','N',NULL,NULL,NULL,'toNetworkElementType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'totalAccountingQuantity','1','1','N','N',NULL,NULL,NULL,'totalAccountingQuantity',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'totalAccountingQuantityV','1','1','N','N',NULL,NULL,NULL,'totalAccountingQuantityV',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'totalAmount','1','1','N','N',NULL,NULL,NULL,'totalAmount',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Unità di misura','1','1','N','N',NULL,NULL,NULL,'unitOfMeasure',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Aggiornamento Banca Dati','1','1','N','N',NULL,NULL,NULL,'updateDatabase',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'updateDatabaseF1','1','1','N','N',NULL,NULL,NULL,'updateDatabaseF1',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'updateDatabaseF2','1','1','N','N',NULL,NULL,NULL,'updateDatabaseF2',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'username','1','1','N','N',NULL,NULL,NULL,'username',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'warnings','1','1','N','Y',NULL,NULL,NULL,'warnings',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'workingGroupCode','1','1','N','N',NULL,NULL,NULL,'workingGroupCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'workOrderId','1','1','N','N',NULL,NULL,NULL,'workOrderId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'worksClosedKO','1','1','N','N',NULL,NULL,NULL,'worksClosedKO',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'worksClosedOK','1','1','N','N',NULL,NULL,NULL,'worksClosedOK',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'Sorveglianza Lavori','1','1','N','N',NULL,NULL,NULL,'worksManning',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'worksOnGoing','1','1','N','N',NULL,NULL,NULL,'worksOnGoing',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'worksTotal','1','1','N','N',NULL,NULL,NULL,'worksTotal',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'workType','1','1','N','N',NULL,NULL,NULL,'workType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval,'workZoneId','1','1','N','N',NULL,NULL,NULL,'workZoneId',NULL,NULL,NULL,NULL,NULL);

UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='FILE DOCUMENTO',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'DOCUMENTO';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='tdt alfa',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'alfa';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='tdt beta',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'beta';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='API_TDT_01',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'API_TDT_01';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='API_TDT_02',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'API_TDT_02';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='API_TDT_03',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'API_TDT_03';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='plannedSplicesAndTermChanges',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedSplicesAndTermChanges';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkElementFulfilled',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkElementFulfilled';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='propositionContractId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'propositionContractId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='technicalSite',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'technicalSite';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='technicalSiteDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'technicalSiteDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='goodsRecipient',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'goodsRecipient';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerWorkingArea',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerWorkingArea';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='forecastStartDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'forecastStartDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='forecastEndDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'forecastEndDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='propositionCurrency',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'propositionCurrency';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneToStockLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneToStockLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='poseTypeChangesV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'poseTypeChangesV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneUndergroundLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneUndergroundLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneHandmaidLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneHandmaidLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneAerialLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneAerialLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneFacadeLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneFacadeLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneToStockLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneToStockLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Posa Cavo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cableLaying';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='accountingQuantity',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'accountingQuantity';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='workOrderId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'workOrderId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerTechnicalAssistant',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerTechnicalAssistant';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='SAPDivision',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'SAPDivision';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='projectId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'projectId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='plannedToStockLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedToStockLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='plannedFromStockLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedFromStockLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='operation',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'operation';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='SAPDivisionDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'SAPDivisionDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='polygon',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'polygon';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='popId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'popId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='category',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'category';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='requestDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'requestDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Lunghezza tratta su facciata progettata',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedFacadeLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneAerialLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneAerialLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneFacadeLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneFacadeLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneHandmaidLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneHandmaidLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='teamNote',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'teamNote';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='onFieldAssistant',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'onFieldAssistant';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='city',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'city';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cableName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cableName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='terminatedFibers',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'terminatedFibers';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='country',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'country';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pop',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pop';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Unità di misura',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'unitOfMeasure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='endPlannedDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'endPlannedDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pilingCheck',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pilingCheck';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkElementName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkElementName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='publicPermitsOnGoing',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'publicPermitsOnGoing';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='centralPoint',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'centralPoint';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='name',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'name';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='contractId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'contractId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='username',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'username';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='inputCableId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'inputCableId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pfpName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pfpName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='subContractName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'subContractName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='messageId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'messageId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerWBE',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerWBE';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='enhancementFactorDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'enhancementFactorDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerTechnicalAssistantDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerTechnicalAssistantDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='supplierGoodsTotalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'supplierGoodsTotalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerGoodsTotalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerGoodsTotalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cable144foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cable144foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Giunzione',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'junction';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Sopralluogo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'survey';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='worksOnGoing',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'worksOnGoing';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='worksClosedKO',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'worksClosedKO';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='splicedFibers',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'splicedFibers';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='ringId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'ringId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Sorveglianza Lavori',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'worksManning';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Guasto Non Disservito',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'defectWithoutDisservice';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cableId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cableId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='roeId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'roeId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='assetId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='Y',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'assetId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneFromStockLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneFromStockLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='publicPermitsTotal',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'publicPermitsTotal';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='publicPermitsClosedOK',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'publicPermitsClosedOK';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='publicPermitsClosedKO',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'publicPermitsClosedKO';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='privatePermitsOnGoing',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'privatePermitsOnGoing';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='privatePermitsTotal',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'privatePermitsTotal';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='privatePermitsClosedOK',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'privatePermitsClosedOK';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='toAssignQuantity',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'toAssignQuantity';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='restoration',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'restoration';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='businessProject',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'businessProject';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='goodsSupplier',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'goodsSupplier';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='privatePermitsClosedKO',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'privatePermitsClosedKO';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='ring',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'ring';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='centralId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'centralId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Identificativo mnemonico (nome) dell''elemento di rete di partenza',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'fromNetworkElementName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Identificativo SiNFO dell''elemento di rete di partenza',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'fromNetworkElementId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='infrastructureCheck',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'infrastructureCheck';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='externalSequence',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'externalSequence';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cadastralCode',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cadastralCode';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='province',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'province';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Identificativo SiNFO dell''elemento di rete d''arrivo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'toNetworkElementId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Lunghezza tratta sotterranea progettata',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedUndergroundLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Lunghezza tratta aerea progettata',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedAerialLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='opticalConnectionOLT',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'opticalConnectionOLT';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='maintenanceId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'maintenanceId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='canOpenPermits',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'canOpenPermits';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='subContractCode',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'subContractCode';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='subCategory',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'subCategory';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='status',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'status';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='teamId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'teamId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='fromNetworkElementType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'fromNetworkElementType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='toNetworkElementType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'toNetworkElementType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='fromNetworkElementGeoLocation',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'fromNetworkElementGeoLocation';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='toNetworkElementGeoLocation',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'toNetworkElementGeoLocation';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Tipologia cavo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cableType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkElementType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkElementType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='latitude',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'latitude';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='requestType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'requestType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Identificativo mnemonico (nome) dell''elemento di rete d''arrivo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'toNetworkElementName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='recoveryCable',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'recoveryCable';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='plannedBreakdown',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'plannedBreakdown';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='interruptedMinitubes',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'interruptedMinitubes';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='branchesCut',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'branchesCut';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cable192foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cable192foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cable24foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cable24foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cable48foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cable48foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cable96foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cable96foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cityId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cityId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='updateDatabaseF2',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'updateDatabaseF2';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='permitsAreaId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'permitsAreaId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pfpId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pfpId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneUndergroundLength',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneUndergroundLength';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Collaudo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'test';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='operationalContext',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'operationalContext';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Bretellaggio',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'patchCord';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Revisione Impianti',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'installationReview';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Guasto Disservito',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'defectWithDisservice';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='splitterPermutations',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'splitterPermutations';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='splitter116Placing',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'splitter116Placing';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='splitter14Placing',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'splitter14Placing';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='terminations',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'terminations';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='continuousCablesConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'continuousCablesConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='fibersPlacedJunction',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'fibersPlacedJunction';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='prewiredPFS',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'prewiredPFS';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cablingType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cablingType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='inputCableName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'inputCableName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='inputCablePotential',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'inputCablePotential';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='inputCablePotentialPlanned',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'inputCablePotentialPlanned';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='outputCableId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'outputCableId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='outputCableName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'outputCableName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='outputCablePotential',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'outputCablePotential';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='outputCablePotentialPlanned',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'outputCablePotentialPlanned';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='ameliaId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'ameliaId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='summaryNetwork',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'summaryNetwork';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Riferimento ISO',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'isoReference';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='cableOver192foConnection',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'cableOver192foConnection';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='foJunction',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'foJunction';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pfpMeasure',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pfpMeasure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pfsMeasure',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pfsMeasure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='ptaMeasure',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'ptaMeasure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pteMeasure',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pteMeasure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='workType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'workType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='startPlannedDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'startPlannedDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='startWorkDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'startWorkDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='endWorkDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'endWorkDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='teamName',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'teamName';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='assistantNote',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'assistantNote';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='onFieldIntegrationDisabled',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'onFieldIntegrationDisabled';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Lavori Civili',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'civil';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Misure',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'measurements';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Infrastruttura',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'infrastructure';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='updateDatabaseF1',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'updateDatabaseF1';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkElementIsDone',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkElementIsDone';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Visita Tracciato',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pathSurvey';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Riepilogo Trimestre',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'quarterSummary';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='workingGroupCode',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'workingGroupCode';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='opticalConnectionOSU',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'opticalConnectionOSU';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Progettazione',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'design';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='goodsSupplierDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'goodsSupplierDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='totalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'totalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='totalAccountingQuantity',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'totalAccountingQuantity';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='businessProjectDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'businessProjectDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='requestorContext',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'requestorContext';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='supplier',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'supplier';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='supplierDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'supplierDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='servicesTotalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'servicesTotalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Ispezione Sede Posa',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'installationPlaceSurvey';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='spentTime',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'spentTime';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='totalAccountingQuantityV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'totalAccountingQuantityV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkStatus',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkStatus';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='warnings',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='Y',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'warnings';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerWBEDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerWBEDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='operationDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'operationDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerProjectType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerProjectType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='customerProjectTypeDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'customerProjectTypeDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='buyer',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'buyer';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='buyerDesc',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'buyerDesc';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='resourcesTotalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'resourcesTotalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='canOpenWorks',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'canOpenWorks';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='worksTotal',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'worksTotal';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='worksClosedOK',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'worksClosedOK';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='networkElementId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'networkElementId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='longitudelongitude',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'longitude';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pfsPosition',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pfsPosition';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='junctionType',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'junctionType';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='junctionSite',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'junctionSite';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='ptaSite',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'ptaSite';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='pteSite',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'pteSite';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='lackOfMaterial',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'lackOfMaterial';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='notAccessibleCockpit',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'notAccessibleCockpit';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='emptyingCockpit',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'emptyingCockpit';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='poseTypeChanges',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'poseTypeChanges';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Potenzialità del cavo',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'potential';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Durata stimata dell''attività espressa in ore',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'estimatedDuration';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='doneFromStockLengthV',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'doneFromStockLengthV';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='propositionNumber',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'propositionNumber';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='propositionDate',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'propositionDate';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='MOSTotalAmount',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'MOSTotalAmount';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='workZoneId',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'workZoneId';
UPDATE TIPI_DATI_TECNICI set DESCRIZIONE='Aggiornamento Banca Dati',FORM_SEQUENCE=1,FORM_GROUP=1,SECRET_VALUE='N',MULTIPLEX_VALUE='N',URL=NULL,MORTO=NULL,TELNET=NULL,FLAGFILE=NULL,NON_MODIFICABILE=NULL,FLAG=NULL,FLAG_VALORE_VINCOLATO=NULL,LISTA_OGGETTI=NULL where NOME= 'updateDatabase';

-- AGING_CALENDARS

INSERT INTO AGING_CALENDARS values(seq_aging_calendars.nextval,'H24_7x7','Calendario lavorativo h24 7 su 7','');
INSERT INTO AGING_CALENDARS values(seq_aging_calendars.nextval,'H08-20_1-6','Calendario lavorativo da lunedì al sabato dalle 8 alle 20','7');
INSERT INTO AGING_CALENDARS values(seq_aging_calendars.nextval,'H08-17_1-5','Calendario lavorativo da lunedì al venerdì dalle 8 alle 17','6,7');

update AGING_CALENDARS
  set description = 'Calendario lavorativo h24 7 su 7'
    , weekend = ''
  where id_calendar = (select id_calendar from aging_calendars where label = 'H24_7x7');

update AGING_CALENDARS
  set description = 'Calendario lavorativo da lunedì al sabato dalle 8 alle 20'
    , weekend = '7'
  where id_calendar = (select id_calendar from aging_calendars where label = 'H08-20_1-6');

update AGING_CALENDARS
  set description = 'Calendario lavorativo da lunedì al venerdì dalle 8 alle 17'
    , weekend = '6,7'
  where id_calendar = (select id_calendar from aging_calendars where label = 'H08-17_1-5');


-- AGING_WORKING_HOURS

INSERT INTO AGING_WORKING_HOURS values((select id_calendar from aging_calendars where label = 'H24_7x7'),'1,2,3,4,5,6,7','00:00:00','23:59:59');
INSERT INTO AGING_WORKING_HOURS values((select id_calendar from aging_calendars where label = 'H08-20_1-6'),'1,2,3,4,5,6','08:00:00','19:59:59');
INSERT INTO AGING_WORKING_HOURS values((select id_calendar from aging_calendars where label = 'H08-17_1-5'),'1,2,3,4,5','08:00:00','16:59:59');

update AGING_WORKING_HOURS
  set day = '1,2,3,4,5,6,7'
    , start_time = '00:00:00'
    , end_time = '23:59:59'
  where id_calendar = (select id_calendar from aging_calendars where label = 'H24_7x7');

update AGING_WORKING_HOURS
  set day = '1,2,3,4,5,6'
    , start_time = '08:00:00'
    , end_time = '19:59:59'
  where id_calendar = (select id_calendar from aging_calendars where label = 'H08-20_1-6');

update AGING_WORKING_HOURS
  set day = '1,2,3,4,5'
    , start_time = '08:00:00'
    , end_time = '16:59:59'
  where id_calendar = (select id_calendar from aging_calendars where label = 'H08-17_1-5');


-- TIPI_ATTIVITA

INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'Attivita'' su cui effettuare test di regressione','API::TEST::01',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'Attivita'' su cui effettuare test di regressione','API::TEST::03',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'AS_BUILT','AS_BUILT',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'gestione kart','kartatt',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'KART_HISTORY','KART_HISTORY',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'LAVORO','LAVORO',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'tmp1','tmp1',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'tmp2','tmp2',NULL,NULL,NULL);
INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval,'Gestione Work Zone','WZ_LC',NULL,NULL,NULL);

UPDATE TIPI_ATTIVITA set DESCRIZIONE='Attivita'' su cui effettuare test di regressione', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'API::TEST::01';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='Attivita'' su cui effettuare test di regressione', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'API::TEST::03';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='AS_BUILT', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'AS_BUILT';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='gestione kart', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'kartatt';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='KART_HISTORY', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'KART_HISTORY';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='LAVORO', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'LAVORO';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='tmp1', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'tmp1';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='tmp2', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'tmp2';
UPDATE TIPI_ATTIVITA set DESCRIZIONE='Gestione Work Zone', AUTOPARSE=NULL,ID_CALENDAR=NULL, UI_ROUTE=NULL where NOME_TIPO_ATTIVITA= 'WZ_LC';

-- TIPI_ATTIVITA_SLA

delete tipi_attivita_sla;

-- VERIFICO ESISTENZA TABELLA TIPI_ATTIVITA_COMP

delete TIPI_ATTIVITA_COMP;
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'API::TEST::01'),null,null,null);
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'API::TEST::03'),null,null,null);
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'KART_HISTORY'),null,null,null);
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'AS_BUILT'),'1',null,'1');
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'LAVORO'),null,null,null);
INSERT INTO TIPI_ATTIVITA_COMP values (seq_tipi_attivita_comp.nextval,(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = 'WZ_LC'),null,null,null);

-- Gestione Behaviours

delete tipi_attivita_behaviour;

-- BEHAVIOUR_EVENT_LOOKUP

delete BEHAVIOUR_EVENT_LOOKUP;
INSERT INTO BEHAVIOUR_EVENT_LOOKUP values (1,'on_create','Evento generato in fase di creazione di un''attività');
INSERT INTO BEHAVIOUR_EVENT_LOOKUP values (2,'on_father_step','Evento generato a seguito dello step di un''attività padre');
INSERT INTO BEHAVIOUR_EVENT_LOOKUP values (3,'on_child_step','Evento generato a seguito dello step di un''attività figlia');
INSERT INTO BEHAVIOUR_EVENT_LOOKUP values (4,'on_step','Evento generato a seguito dello step');

-- BEHAVIOUR_LOOKUP

delete BEHAVIOUR_LOOKUP;
INSERT INTO BEHAVIOUR_LOOKUP values (1,'FollowFather','Follow Father: l''attività figlia si movimenta in funzione dell''attività padre');
INSERT INTO BEHAVIOUR_LOOKUP values (2,'ObserveChildren','Observe Children: l''attività padre si movimenta in funzione dell''attività figlia');
INSERT INTO BEHAVIOUR_LOOKUP values (3,'CUSTOM','Comportamenti custom');

-- TIPI_ATTIVITA_BEHAVIOUR


-- TIPI_DATI_TECNICI_ATTIVITA

INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'accountingDone',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'MEMO  ',NULL,NULL,'accountingNote',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'accountingUser',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'address',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'API::TDTA::01',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'API::TDTA::02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'API::TDTA::03',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'asBuiltId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'cableLaying',NULL,NULL,'Posa Cavo',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'cadastralCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'centralPoint',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'cityId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'civil',NULL,NULL,'Lavori Civili',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'contractId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'customerId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'defectWithDisservice',NULL,NULL,'Guasto Disservito',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'defectWithoutDisservice',NULL,NULL,'Guasto Non Disservito',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'design',NULL,NULL,'Progettazione',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_CMI',NULL,NULL,'DOC. MISURE',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_DCO',NULL,NULL,'DOC. DOCUMENTAZIONE COLLAUDO',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_DOF',NULL,NULL,'DOC. DOCUMENTAZIONE FOTOGRAFICA',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_FIR',NULL,NULL,'DOC. FIR',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_VAR',NULL,NULL,'DOC. VARIE',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'__DTC_WKI',NULL,NULL,'DOC. WALK IN',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'endPlannedDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'endWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'estimatedDuration',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'eventType',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'flagFIR',NULL,NULL,'Previste attività di scavo, ripristino, smantellamento',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'ids',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'infrastructure',NULL,NULL,'Infrastruttura',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'installationPlaceSurvey',NULL,NULL,'Ispezione Sede Posa',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'installationReview',NULL,NULL,'Revisione Impianti',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'junction',NULL,NULL,'Giunzione',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'kart_data',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'latitude',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'longitude',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'maker',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'measurements',NULL,NULL,'Misure',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'name',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'MEMO  ',NULL,NULL,'note',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'onFieldAssistant',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'onFieldIntegrationDisabled',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'opticalConnectionOLT',NULL,NULL,'opticalConnectionOLT',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'opticalConnectionOSU',NULL,NULL,'opticalConnectionOSU',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'patchCord',NULL,NULL,'Bretellaggio',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'pathSurvey',NULL,NULL,'Visita Tracciato',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'permitsAreaId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'planningServiceCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'MEMO  ',NULL,NULL,'planningServiceDescription',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'planningServiceId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'planningServiceResult',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'polygon',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'pop',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'popId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'projectId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'quarterSummary',NULL,NULL,'Riepilogo Trimestre',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'requestDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'restoration',NULL,NULL,'restoration',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'result',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'ring',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'ringId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'slaEnd',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'slaStart',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'spentTime',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'startPlannedDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'startWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'subcontract',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'subContractCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'subContractEndWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'subContractName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'ISODAT',NULL,NULL,'subContractStartWorkDate',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'survey',NULL,NULL,'Sopralluogo',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1','-1','DATA  ',NULL,NULL,'tdta01','Y',NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'LIST  ','0,1,2,3,4,5,6,7,8,9','5','tdta02',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'POPUP ','A,B,C,D,E,F,G,H,I','A','tdta03',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'teamId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'teamName',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'test',NULL,NULL,'Collaudo',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'ttId',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'MEMO  ',NULL,NULL,'ttKOreason',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'updateDatabase',NULL,NULL,'Agg. Banca Dati',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'updateDatabaseF1',NULL,NULL,'updateDatabaseF1',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'updateDatabaseF2',NULL,NULL,'updateDatabaseF2',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'STRING',NULL,NULL,'username',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'MEMO  ',NULL,NULL,'validationNote',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'workingGroupCode',NULL,NULL,NULL,NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'BOOL  ',NULL,NULL,'worksManning',NULL,NULL,'Sorveglianza Lavori',NULL,NULL);
INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval,'-1','-1',NULL,'NUMBER',NULL,NULL,'workZoneId',NULL,NULL,NULL,NULL,NULL);

UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='DATA  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='tdta01',OBBLIGATORIO='Y',MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'tdta01';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='LIST  ',VALORI='0,1,2,3,4,5,6,7,8,9',VALORE_DEFAULT='5',DESCRIZIONE='tdta02',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'tdta02';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='POPUP ',VALORI='A,B,C,D,E,F,G,H,I',VALORE_DEFAULT='A',DESCRIZIONE='tdta03',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'tdta03';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='API::TDTA::01',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'API::TDTA::01';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='kart_data',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'kart_data';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='API::TDTA::03',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'API::TDTA::03';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='API::TDTA::02',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'API::TDTA::02';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='projectId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'projectId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='permitsAreaId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'permitsAreaId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='maker',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'maker';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='ringId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'ringId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='ids',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'ids';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='cadastralCode',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'cadastralCode';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='subContractEndWorkDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'subContractEndWorkDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='accountingUser',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'accountingUser';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='MEMO  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='accountingNote',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'accountingNote';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='flagFIR',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Previste attività di scavo, ripristino, smantellamento',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'flagFIR';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='pathSurvey',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Visita Tracciato',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'pathSurvey';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='cableLaying',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Posa Cavo',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'cableLaying';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='workZoneId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'workZoneId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='MEMO  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='note',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'note';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='requestDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'requestDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='polygon',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'polygon';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='planningServiceCode',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'planningServiceCode';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='teamId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'teamId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='cityId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'cityId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='junction',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Giunzione',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'junction';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='patchCord',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Bretellaggio',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'patchCord';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='onFieldIntegrationDisabled',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'onFieldIntegrationDisabled';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='test',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Collaudo',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'test';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='measurements',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Misure',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'measurements';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='defectWithDisservice',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Guasto Disservito',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'defectWithDisservice';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='defectWithoutDisservice',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Guasto Non Disservito',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'defectWithoutDisservice';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='name',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'name';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='contractId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'contractId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='username',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'username';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='customerId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'customerId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='slaStart',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'slaStart';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='slaEnd',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'slaEnd';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='startPlannedDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'startPlannedDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='planningServiceResult',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'planningServiceResult';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='startWorkDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'startWorkDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='endWorkDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'endWorkDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='latitude',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'latitude';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='address',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'address';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='teamName',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'teamName';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='ring',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'ring';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='pop',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'pop';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='popId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'popId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='subContractName',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'subContractName';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='subContractCode',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'subContractCode';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='subContractStartWorkDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'subContractStartWorkDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='onFieldAssistant',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'onFieldAssistant';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='accountingDone',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'accountingDone';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='MEMO  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='validationNote',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'validationNote';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='opticalConnectionOLT',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='opticalConnectionOLT',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'opticalConnectionOLT';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='civil',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Lavori Civili',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'civil';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='survey',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Sopralluogo',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'survey';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='opticalConnectionOSU',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='opticalConnectionOSU',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'opticalConnectionOSU';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='updateDatabaseF1',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='updateDatabaseF1',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'updateDatabaseF1';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='design',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Progettazione',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'design';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_CMI',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. MISURE',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_CMI';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_DCO',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. DOCUMENTAZIONE COLLAUDO',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_DCO';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_DOF',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. DOCUMENTAZIONE FOTOGRAFICA',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_DOF';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_VAR',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. VARIE',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_VAR';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_WKI',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. WALK IN',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_WKI';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='__DTC_FIR',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='DOC. FIR',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= '__DTC_FIR';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='installationPlaceSurvey',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Ispezione Sede Posa',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'installationPlaceSurvey';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='quarterSummary',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Riepilogo Trimestre',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'quarterSummary';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='infrastructure',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Infrastruttura',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'infrastructure';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='centralPoint',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'centralPoint';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='subcontract',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'subcontract';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='result',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'result';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='MEMO  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='planningServiceDescription',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'planningServiceDescription';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='planningServiceId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'planningServiceId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='longitude',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'longitude';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='estimatedDuration',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'estimatedDuration';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='STRING',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='eventType',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'eventType';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='ISODAT',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='endPlannedDate',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'endPlannedDate';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='ttId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'ttId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='MEMO  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='ttKOreason',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'ttKOreason';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='asBuiltId',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'asBuiltId';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='workingGroupCode',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'workingGroupCode';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='NUMBER',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='spentTime',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA=NULL,SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'spentTime';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='updateDatabase',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Agg. Banca Dati',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'updateDatabase';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='updateDatabaseF2',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='updateDatabaseF2',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'updateDatabaseF2';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='restoration',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='restoration',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'restoration';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='installationReview',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Revisione Impianti',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'installationReview';
UPDATE TIPI_DATI_TECNICI_ATTIVITA set TIPO_UI='BOOL  ',VALORI=NULL,VALORE_DEFAULT=NULL,DESCRIZIONE='worksManning',OBBLIGATORIO=NULL,MORTO=NULL,ETICHETTA='Sorveglianza Lavori',SUGGERIMENTO=NULL,VALORE_PREDEFINITO=NULL where DESCRIZIONE= 'worksManning';

-- STATI

INSERT INTO STATI values(to_char(seq_stati.nextval),'ACQUISITA','N',NULL,NULL,NULL,'ACQUISITA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ANNULLATA','Q',NULL,NULL,NULL,'ANNULLATA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ANNULLATO','Q',NULL,NULL,NULL,'ANNULLATO',NULL,NULL,'80');
INSERT INTO STATI values(to_char(seq_stati.nextval),'ANOMALIA','N',NULL,NULL,NULL,'ANOMALIA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'*** Any Status ***','Y',NULL,NULL,NULL,'___ANY_STATUS___',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'APERTA','A',NULL,NULL,NULL,'APERTA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'API::STATO::CHIUSA','Q',NULL,NULL,NULL,'API::STATO::CHIUSA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'API::STATO::ERRORE','Q',NULL,NULL,NULL,'API::STATO::ERRORE',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'API::STATO::01','N',NULL,NULL,NULL,'API::STATO::01',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'API::STATO::02','N',NULL,NULL,NULL,'API::STATO::02',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ASSURANCE_ANNULLAMENTO','N',NULL,NULL,NULL,'ASSURANCE_ANNULLAMENTO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ATTESA_ANNULLAMENTO','N',NULL,NULL,NULL,'ATTESA_ANNULLAMENTO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ATTESA_APERTURA_TT','N',NULL,NULL,NULL,'ATTESA_APERTURA_TT',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ATTESA_CHIUSURA_TT','N',NULL,NULL,NULL,'ATTESA_CHIUSURA_TT',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ATTESA_GESTIONE_AS_BUILT','N',NULL,NULL,NULL,'ATTESA_GESTIONE_AS_BUILT',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ATTESA_RENDICONTAZIONE','N',NULL,NULL,NULL,'ATTESA_RENDICONTAZIONE',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'CHIUSA','C',NULL,NULL,NULL,'CHIUSA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'STATO DI ERRORE','Q',NULL,NULL,NULL,'ERRORE',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ESPLETATO','Q',NULL,NULL,NULL,'ESPLETATO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'ESPLETATO_SENZA_AS_BUILT','Q',NULL,NULL,NULL,'ESPLETATO_SENZA_AS_BUILT',NULL,NULL,'80');
INSERT INTO STATI values(to_char(seq_stati.nextval),'IN_LAVORAZIONE_SIRTI','N',NULL,NULL,NULL,'IN_LAVORAZIONE_SIRTI',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'IN_LAVORAZIONE_SUBAPPALTO','N',NULL,NULL,NULL,'IN_LAVORAZIONE_SUBAPPALTO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'INOLTRATO_SIRTI','N',NULL,NULL,NULL,'INOLTRATO_SIRTI',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'INOLTRATO_SUBAPPALTO','N',NULL,NULL,NULL,'INOLTRATO_SUBAPPALTO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'KART_STATO_1','N',NULL,NULL,NULL,'KART_STATO_1',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'KART_STATO_2','N',NULL,NULL,NULL,'KART_STATO_2',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'KART_STATO_3','N',NULL,NULL,NULL,'KART_STATO_3',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'KO','Q',NULL,NULL,NULL,'KO',NULL,NULL,'81');
INSERT INTO STATI values(to_char(seq_stati.nextval),'KO_TT','Q',NULL,NULL,NULL,'KO_TT',NULL,NULL,'81');
INSERT INTO STATI values(to_char(seq_stati.nextval),'NUOVO','N',NULL,NULL,NULL,'NUOVO',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'STATO VIRTUALE UTILIZZATO SOLO DA API::ART','V',NULL,NULL,NULL,'___PARKED___',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'QUARANTENA','N',NULL,NULL,NULL,'QUARANTENA',NULL,NULL,NULL);
INSERT INTO STATI values(to_char(seq_stati.nextval),'START','X',NULL,NULL,NULL,'START',NULL,NULL,NULL);

UPDATE STATI set DESCRIZIONE='KART_STATO_3',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='KART_STATO_3',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'KART_STATO_3';
UPDATE STATI set DESCRIZIONE='KART_STATO_2',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='KART_STATO_2',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'KART_STATO_2';
UPDATE STATI set DESCRIZIONE='STATO VIRTUALE UTILIZZATO SOLO DA API::ART',FLAG_STATO_PARTENZA='V',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='___PARKED___',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= '___PARKED___';
UPDATE STATI set DESCRIZIONE='APERTA',FLAG_STATO_PARTENZA='A',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='APERTA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'APERTA';
UPDATE STATI set DESCRIZIONE='CHIUSA',FLAG_STATO_PARTENZA='C',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='CHIUSA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'CHIUSA';
UPDATE STATI set DESCRIZIONE='START',FLAG_STATO_PARTENZA='X',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='START',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'START';
UPDATE STATI set DESCRIZIONE='KART_STATO_1',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='KART_STATO_1',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'KART_STATO_1';
UPDATE STATI set DESCRIZIONE='ANNULLATA',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ANNULLATA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ANNULLATA';
UPDATE STATI set DESCRIZIONE='STATO DI ERRORE',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ERRORE',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ERRORE';
UPDATE STATI set DESCRIZIONE='API::STATO::01',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='API::STATO::01',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'API::STATO::01';
UPDATE STATI set DESCRIZIONE='API::STATO::02',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='API::STATO::02',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'API::STATO::02';
UPDATE STATI set DESCRIZIONE='API::STATO::CHIUSA',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='API::STATO::CHIUSA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'API::STATO::CHIUSA';
UPDATE STATI set DESCRIZIONE='API::STATO::ERRORE',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='API::STATO::ERRORE',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'API::STATO::ERRORE';
UPDATE STATI set DESCRIZIONE='*** Any Status ***',FLAG_STATO_PARTENZA='Y',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='___ANY_STATUS___',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= '___ANY_STATUS___';
UPDATE STATI set DESCRIZIONE='ACQUISITA',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ACQUISITA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ACQUISITA';
UPDATE STATI set DESCRIZIONE='INOLTRATO_SUBAPPALTO',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='INOLTRATO_SUBAPPALTO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'INOLTRATO_SUBAPPALTO';
UPDATE STATI set DESCRIZIONE='INOLTRATO_SIRTI',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='INOLTRATO_SIRTI',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'INOLTRATO_SIRTI';
UPDATE STATI set DESCRIZIONE='IN_LAVORAZIONE_SIRTI',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='IN_LAVORAZIONE_SIRTI',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'IN_LAVORAZIONE_SIRTI';
UPDATE STATI set DESCRIZIONE='QUARANTENA',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='QUARANTENA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'QUARANTENA';
UPDATE STATI set DESCRIZIONE='ESPLETATO',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ESPLETATO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ESPLETATO';
UPDATE STATI set DESCRIZIONE='KO',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='KO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=81 where NOME= 'KO';
UPDATE STATI set DESCRIZIONE='ATTESA_CHIUSURA_TT',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ATTESA_CHIUSURA_TT',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ATTESA_CHIUSURA_TT';
UPDATE STATI set DESCRIZIONE='ASSURANCE_ANNULLAMENTO',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ASSURANCE_ANNULLAMENTO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ASSURANCE_ANNULLAMENTO';
UPDATE STATI set DESCRIZIONE='ANOMALIA',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ANOMALIA',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ANOMALIA';
UPDATE STATI set DESCRIZIONE='ATTESA_GESTIONE_AS_BUILT',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ATTESA_GESTIONE_AS_BUILT',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ATTESA_GESTIONE_AS_BUILT';
UPDATE STATI set DESCRIZIONE='KO_TT',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='KO_TT',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=81 where NOME= 'KO_TT';
UPDATE STATI set DESCRIZIONE='NUOVO',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='NUOVO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'NUOVO';
UPDATE STATI set DESCRIZIONE='ATTESA_RENDICONTAZIONE',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ATTESA_RENDICONTAZIONE',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ATTESA_RENDICONTAZIONE';
UPDATE STATI set DESCRIZIONE='ESPLETATO_SENZA_AS_BUILT',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ESPLETATO_SENZA_AS_BUILT',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=80 where NOME= 'ESPLETATO_SENZA_AS_BUILT';
UPDATE STATI set DESCRIZIONE='ANNULLATO',FLAG_STATO_PARTENZA='Q',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ANNULLATO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=80 where NOME= 'ANNULLATO';
UPDATE STATI set DESCRIZIONE='ATTESA_ANNULLAMENTO',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ATTESA_ANNULLAMENTO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ATTESA_ANNULLAMENTO';
UPDATE STATI set DESCRIZIONE='ATTESA_APERTURA_TT',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='ATTESA_APERTURA_TT',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'ATTESA_APERTURA_TT';
UPDATE STATI set DESCRIZIONE='IN_LAVORAZIONE_SUBAPPALTO',FLAG_STATO_PARTENZA='N',PRIORITA=NULL,MORTO=NULL,GENERA_MAIL=NULL,NOME='IN_LAVORAZIONE_SUBAPPALTO',GENERA_MAIL_MITTENTE=NULL,INTERFACCIA=NULL,ID_TIPO_ATTIVITA=NULL where NOME= 'IN_LAVORAZIONE_SUBAPPALTO';

-- ACTION

INSERT INTO ACTION values (to_char(seq_action.nextval),'ACQUISIZIONE',NULL,NULL,'ACQUISIZIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'Aggiorna proprieta'' attivita''','T',NULL,'__AGGIORNA_PROPRIETA__',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AGGIUNGI_INFO',NULL,NULL,'AGGIUNGI_INFO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ALLINEAMENTO','U',NULL,'__ALLINEAMENTO__',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ANNULLAMENTO',NULL,NULL,'ANNULLAMENTO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ANNULLAMENTO_AS_BUILT',NULL,NULL,'ANNULLAMENTO_AS_BUILT',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ANNULLAMENTO_FORZATO',NULL,NULL,'ANNULLAMENTO_FORZATO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ANNULLAMENTO_SIRTI_OFFLINE',NULL,NULL,'ANNULLAMENTO_SIRTI_OFFLINE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'ANOMALIA',NULL,NULL,'ANOMALIA',NULL);
INSERT INTO ACTION values ('A','APERTURA','A',NULL,'APERTURA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'APERTURA_TT_KO',NULL,NULL,'APERTURA_TT_KO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'APERTURA_TT_OK',NULL,NULL,'APERTURA_TT_OK',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'API::AZIONE::CHIUSURA',NULL,NULL,'API::AZIONE::CHIUSURA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'API::AZIONE::ERRORE',NULL,NULL,'API::AZIONE::ERRORE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'API::AZIONE::01',NULL,NULL,'API::AZIONE::01',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'API::AZIONE::02',NULL,NULL,'API::AZIONE::02',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AS_BUILT_NON_NECESSARIO',NULL,NULL,'AS_BUILT_NON_NECESSARIO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'Assegnazione virtuale','G',NULL,'__ASSEGNAZIONE__',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AVANZAMENTO',NULL,NULL,'AVANZAMENTO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'CARICAMENTO_AS_BUILT',NULL,NULL,'CARICAMENTO_AS_BUILT',NULL);
INSERT INTO ACTION values ('C','CHIUSURA','C',NULL,'CHIUSURA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'CHIUSURA_AS_BUILT',NULL,NULL,'CHIUSURA_AS_BUILT',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'CHIUSURA_TT_KO',NULL,NULL,'CHIUSURA_TT_KO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'CHIUSURA_TT_OK',NULL,NULL,'CHIUSURA_TT_OK',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'CONFERMA',NULL,NULL,'CONFERMA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'Deassegnazione virtuale','H',NULL,'__DEASSEGNAZIONE__',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'FINE_LAVORI',NULL,NULL,'FINE_LAVORI',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'FINE_LAVORI_SIRTI',NULL,NULL,'FINE_LAVORI_SIRTI',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'FINE_LAVORI_SUBAPPALTO',NULL,NULL,'FINE_LAVORI_SUBAPPALTO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART','V',NULL,'___GOTO___',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'INIZIO_LAVORI',NULL,NULL,'INIZIO_LAVORI',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'INOLTRO_RENDICONTAZIONE',NULL,NULL,'INOLTRO_RENDICONTAZIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'KART_ERRORE',NULL,NULL,'KART_ERRORE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'KART_STEP1',NULL,NULL,'KART_STEP1',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'KART_STEP2',NULL,NULL,'KART_STEP2',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'KART_STEP3',NULL,NULL,'KART_STEP3',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'LAVORABILE_DA_SIRTI',NULL,NULL,'LAVORABILE_DA_SIRTI',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'LAVORABILE_DA_SIRTI_OFFLINE',NULL,NULL,'LAVORABILE_DA_SIRTI_OFFLINE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'LAVORABILE_DA_SUBAPPALTO',NULL,NULL,'LAVORABILE_DA_SUBAPPALTO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART','V',NULL,'___LOOP___',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'MODIFICA',NULL,NULL,'MODIFICA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'MODIFICA_DATI',NULL,NULL,'MODIFICA_DATI',NULL);
INSERT INTO ACTION values ('S','PARCHEGGIATA','S',NULL,'PARCHEGGIATA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART','V',NULL,'___PARK___',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'PIANIFICAZIONE',NULL,NULL,'PIANIFICAZIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RENDICONTAZIONE',NULL,NULL,'RENDICONTAZIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART','V',NULL,'___RESTORE___',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RICHIESTA_ANNULLAMENTO',NULL,NULL,'RICHIESTA_ANNULLAMENTO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RICHIESTA_ANNULLAMENTO_KO',NULL,NULL,'RICHIESTA_ANNULLAMENTO_KO',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RICHIESTA_ANNULLAMENTO_OK',NULL,NULL,'RICHIESTA_ANNULLAMENTO_OK',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RICHIESTA_APERTURA_TT',NULL,NULL,'RICHIESTA_APERTURA_TT',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RICHIESTA_LAVORAZIONE',NULL,NULL,'RICHIESTA_LAVORAZIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'RIPRESA',NULL,NULL,'RIPRESA',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'SOSPENSIONE',NULL,NULL,'SOSPENSIONE',NULL);
INSERT INTO ACTION values (to_char(seq_action.nextval),'TIMEOUT',NULL,NULL,'TIMEOUT',NULL);

UPDATE ACTION set DESCRIZIONE='KART_STEP2',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='KART_STEP2',INTERFACCIA=NULL where NOME= 'KART_STEP2';
UPDATE ACTION set DESCRIZIONE='KART_STEP3',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='KART_STEP3',INTERFACCIA=NULL where NOME= 'KART_STEP3';
UPDATE ACTION set DESCRIZIONE='KART_STEP1',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='KART_STEP1',INTERFACCIA=NULL where NOME= 'KART_STEP1';
UPDATE ACTION set DESCRIZIONE='AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART',FLAG_ACTION_PARTENZA='V',MORTO=NULL,NOME='___PARK___',INTERFACCIA=NULL where NOME= '___PARK___';
UPDATE ACTION set DESCRIZIONE='AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART',FLAG_ACTION_PARTENZA='V',MORTO=NULL,NOME='___GOTO___',INTERFACCIA=NULL where NOME= '___GOTO___';
UPDATE ACTION set DESCRIZIONE='AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART',FLAG_ACTION_PARTENZA='V',MORTO=NULL,NOME='___LOOP___',INTERFACCIA=NULL where NOME= '___LOOP___';
UPDATE ACTION set DESCRIZIONE='AZIONE "VIRTUALE" UTILIZZATA SOLO DA API::ART',FLAG_ACTION_PARTENZA='V',MORTO=NULL,NOME='___RESTORE___',INTERFACCIA=NULL where NOME= '___RESTORE___';
UPDATE ACTION set DESCRIZIONE='APERTURA',FLAG_ACTION_PARTENZA='A',MORTO=NULL,NOME='APERTURA',INTERFACCIA=NULL where NOME= 'APERTURA';
UPDATE ACTION set DESCRIZIONE='CHIUSURA',FLAG_ACTION_PARTENZA='C',MORTO=NULL,NOME='CHIUSURA',INTERFACCIA=NULL where NOME= 'CHIUSURA';
UPDATE ACTION set DESCRIZIONE='PARCHEGGIATA',FLAG_ACTION_PARTENZA='S',MORTO=NULL,NOME='PARCHEGGIATA',INTERFACCIA=NULL where NOME= 'PARCHEGGIATA';
UPDATE ACTION set DESCRIZIONE='API::AZIONE::01',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='API::AZIONE::01',INTERFACCIA=NULL where NOME= 'API::AZIONE::01';
UPDATE ACTION set DESCRIZIONE='API::AZIONE::02',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='API::AZIONE::02',INTERFACCIA=NULL where NOME= 'API::AZIONE::02';
UPDATE ACTION set DESCRIZIONE='API::AZIONE::CHIUSURA',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='API::AZIONE::CHIUSURA',INTERFACCIA=NULL where NOME= 'API::AZIONE::CHIUSURA';
UPDATE ACTION set DESCRIZIONE='API::AZIONE::ERRORE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='API::AZIONE::ERRORE',INTERFACCIA=NULL where NOME= 'API::AZIONE::ERRORE';
UPDATE ACTION set DESCRIZIONE='KART_ERRORE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='KART_ERRORE',INTERFACCIA=NULL where NOME= 'KART_ERRORE';
UPDATE ACTION set DESCRIZIONE='Aggiorna proprieta'' attivita''',FLAG_ACTION_PARTENZA='T',MORTO=NULL,NOME='__AGGIORNA_PROPRIETA__',INTERFACCIA=NULL where NOME= '__AGGIORNA_PROPRIETA__';
UPDATE ACTION set DESCRIZIONE='ALLINEAMENTO',FLAG_ACTION_PARTENZA='U',MORTO=NULL,NOME='__ALLINEAMENTO__',INTERFACCIA=NULL where NOME= '__ALLINEAMENTO__';
UPDATE ACTION set DESCRIZIONE='Assegnazione virtuale',FLAG_ACTION_PARTENZA='G',MORTO=NULL,NOME='__ASSEGNAZIONE__',INTERFACCIA=NULL where NOME= '__ASSEGNAZIONE__';
UPDATE ACTION set DESCRIZIONE='Deassegnazione virtuale',FLAG_ACTION_PARTENZA='H',MORTO=NULL,NOME='__DEASSEGNAZIONE__',INTERFACCIA=NULL where NOME= '__DEASSEGNAZIONE__';
UPDATE ACTION set DESCRIZIONE='LAVORABILE_DA_SUBAPPALTO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='LAVORABILE_DA_SUBAPPALTO',INTERFACCIA=NULL where NOME= 'LAVORABILE_DA_SUBAPPALTO';
UPDATE ACTION set DESCRIZIONE='MODIFICA_DATI',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='MODIFICA_DATI',INTERFACCIA=NULL where NOME= 'MODIFICA_DATI';
UPDATE ACTION set DESCRIZIONE='AVANZAMENTO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='AVANZAMENTO',INTERFACCIA=NULL where NOME= 'AVANZAMENTO';
UPDATE ACTION set DESCRIZIONE='CARICAMENTO_AS_BUILT',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='CARICAMENTO_AS_BUILT',INTERFACCIA=NULL where NOME= 'CARICAMENTO_AS_BUILT';
UPDATE ACTION set DESCRIZIONE='ANNULLAMENTO_AS_BUILT',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ANNULLAMENTO_AS_BUILT',INTERFACCIA=NULL where NOME= 'ANNULLAMENTO_AS_BUILT';
UPDATE ACTION set DESCRIZIONE='TIMEOUT',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='TIMEOUT',INTERFACCIA=NULL where NOME= 'TIMEOUT';
UPDATE ACTION set DESCRIZIONE='APERTURA_TT_OK',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='APERTURA_TT_OK',INTERFACCIA=NULL where NOME= 'APERTURA_TT_OK';
UPDATE ACTION set DESCRIZIONE='APERTURA_TT_KO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='APERTURA_TT_KO',INTERFACCIA=NULL where NOME= 'APERTURA_TT_KO';
UPDATE ACTION set DESCRIZIONE='RICHIESTA_ANNULLAMENTO_KO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RICHIESTA_ANNULLAMENTO_KO',INTERFACCIA=NULL where NOME= 'RICHIESTA_ANNULLAMENTO_KO';
UPDATE ACTION set DESCRIZIONE='RENDICONTAZIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RENDICONTAZIONE',INTERFACCIA=NULL where NOME= 'RENDICONTAZIONE';
UPDATE ACTION set DESCRIZIONE='INOLTRO_RENDICONTAZIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='INOLTRO_RENDICONTAZIONE',INTERFACCIA=NULL where NOME= 'INOLTRO_RENDICONTAZIONE';
UPDATE ACTION set DESCRIZIONE='LAVORABILE_DA_SIRTI_OFFLINE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='LAVORABILE_DA_SIRTI_OFFLINE',INTERFACCIA=NULL where NOME= 'LAVORABILE_DA_SIRTI_OFFLINE';
UPDATE ACTION set DESCRIZIONE='ACQUISIZIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ACQUISIZIONE',INTERFACCIA=NULL where NOME= 'ACQUISIZIONE';
UPDATE ACTION set DESCRIZIONE='LAVORABILE_DA_SIRTI',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='LAVORABILE_DA_SIRTI',INTERFACCIA=NULL where NOME= 'LAVORABILE_DA_SIRTI';
UPDATE ACTION set DESCRIZIONE='CONFERMA',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='CONFERMA',INTERFACCIA=NULL where NOME= 'CONFERMA';
UPDATE ACTION set DESCRIZIONE='FINE_LAVORI',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='FINE_LAVORI',INTERFACCIA=NULL where NOME= 'FINE_LAVORI';
UPDATE ACTION set DESCRIZIONE='AS_BUILT_NON_NECESSARIO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='AS_BUILT_NON_NECESSARIO',INTERFACCIA=NULL where NOME= 'AS_BUILT_NON_NECESSARIO';
UPDATE ACTION set DESCRIZIONE='FINE_LAVORI_SUBAPPALTO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='FINE_LAVORI_SUBAPPALTO',INTERFACCIA=NULL where NOME= 'FINE_LAVORI_SUBAPPALTO';
UPDATE ACTION set DESCRIZIONE='SOSPENSIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='SOSPENSIONE',INTERFACCIA=NULL where NOME= 'SOSPENSIONE';
UPDATE ACTION set DESCRIZIONE='RICHIESTA_ANNULLAMENTO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RICHIESTA_ANNULLAMENTO',INTERFACCIA=NULL where NOME= 'RICHIESTA_ANNULLAMENTO';
UPDATE ACTION set DESCRIZIONE='RICHIESTA_LAVORAZIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RICHIESTA_LAVORAZIONE',INTERFACCIA=NULL where NOME= 'RICHIESTA_LAVORAZIONE';
UPDATE ACTION set DESCRIZIONE='ANOMALIA',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ANOMALIA',INTERFACCIA=NULL where NOME= 'ANOMALIA';
UPDATE ACTION set DESCRIZIONE='ANNULLAMENTO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ANNULLAMENTO',INTERFACCIA=NULL where NOME= 'ANNULLAMENTO';
UPDATE ACTION set DESCRIZIONE='CHIUSURA_AS_BUILT',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='CHIUSURA_AS_BUILT',INTERFACCIA=NULL where NOME= 'CHIUSURA_AS_BUILT';
UPDATE ACTION set DESCRIZIONE='INIZIO_LAVORI',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='INIZIO_LAVORI',INTERFACCIA=NULL where NOME= 'INIZIO_LAVORI';
UPDATE ACTION set DESCRIZIONE='FINE_LAVORI_SIRTI',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='FINE_LAVORI_SIRTI',INTERFACCIA=NULL where NOME= 'FINE_LAVORI_SIRTI';
UPDATE ACTION set DESCRIZIONE='RICHIESTA_ANNULLAMENTO_OK',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RICHIESTA_ANNULLAMENTO_OK',INTERFACCIA=NULL where NOME= 'RICHIESTA_ANNULLAMENTO_OK';
UPDATE ACTION set DESCRIZIONE='RIPRESA',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RIPRESA',INTERFACCIA=NULL where NOME= 'RIPRESA';
UPDATE ACTION set DESCRIZIONE='MODIFICA',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='MODIFICA',INTERFACCIA=NULL where NOME= 'MODIFICA';
UPDATE ACTION set DESCRIZIONE='ANNULLAMENTO_SIRTI_OFFLINE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ANNULLAMENTO_SIRTI_OFFLINE',INTERFACCIA=NULL where NOME= 'ANNULLAMENTO_SIRTI_OFFLINE';
UPDATE ACTION set DESCRIZIONE='PIANIFICAZIONE',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='PIANIFICAZIONE',INTERFACCIA=NULL where NOME= 'PIANIFICAZIONE';
UPDATE ACTION set DESCRIZIONE='AGGIUNGI_INFO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='AGGIUNGI_INFO',INTERFACCIA=NULL where NOME= 'AGGIUNGI_INFO';
UPDATE ACTION set DESCRIZIONE='RICHIESTA_APERTURA_TT',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='RICHIESTA_APERTURA_TT',INTERFACCIA=NULL where NOME= 'RICHIESTA_APERTURA_TT';
UPDATE ACTION set DESCRIZIONE='CHIUSURA_TT_KO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='CHIUSURA_TT_KO',INTERFACCIA=NULL where NOME= 'CHIUSURA_TT_KO';
UPDATE ACTION set DESCRIZIONE='CHIUSURA_TT_OK',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='CHIUSURA_TT_OK',INTERFACCIA=NULL where NOME= 'CHIUSURA_TT_OK';
UPDATE ACTION set DESCRIZIONE='ANNULLAMENTO_FORZATO',FLAG_ACTION_PARTENZA=NULL,MORTO=NULL,NOME='ANNULLAMENTO_FORZATO',INTERFACCIA=NULL where NOME= 'ANNULLAMENTO_FORZATO';

-- TIPI_DATI_TECNICI_ATT_ACTION

delete tipi_dati_tecnici_att_action;
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta01'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),'C',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta01'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),'C',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta02'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select id_action from action where nome='KART_STEP1'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta01'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select id_action from action where nome='KART_STEP2'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta02'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select id_action from action where nome='KART_STEP2'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta03'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select id_action from action where nome='KART_STEP3'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta03'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::01'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::02'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::03'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::01'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::01'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::02'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::02'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::01'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::02'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::03'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::01'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::02'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='API::TDTA::03'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta02'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='kart_data'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select id_action from action where nome='KART_STEP3'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='tdta03'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select id_action from action where nome='KART_STEP3'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='kart_data'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='centralPoint'),7,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='workZoneId'),4,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='name'),8,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='contractId'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='username'),9,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='customerId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='projectId'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='permitsAreaId'),3,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='note'),10,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='requestDate'),5,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='polygon'),6,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='contractId'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='customerId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='projectId'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='slaStart'),12,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='slaEnd'),13,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startPlannedDate'),14,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='address'),10,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='estimatedDuration'),3,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endPlannedDate'),15,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='teamId'),19,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='teamName'),20,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='maker'),11,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ring'),7,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='pop'),5,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='popId'),6,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ringId'),8,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractName'),17,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractCode'),16,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='workingGroupCode'),9,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='cadastralCode'),4,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='onFieldIntegrationDisabled'),18,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='test'),21,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='updateDatabase'),22,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='civil'),23,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='junction'),24,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='cableLaying'),25,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='survey'),26,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='flagFIR'),28,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='updateDatabaseF2'),29,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='opticalConnectionOLT'),30,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='opticalConnectionOSU'),31,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='updateDatabaseF1'),32,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='restoration'),33,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='design'),27,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_CMI'),44,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_DCO'),45,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_DOF'),46,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_VAR'),47,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_WKI'),48,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='__DTC_FIR'),49,'N','N','Y','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='patchCord'),34,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='pathSurvey'),35,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='installationPlaceSurvey'),36,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='installationReview'),37,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='worksManning'),38,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='measurements'),39,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='quarterSummary'),40,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='defectWithDisservice'),41,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='defectWithoutDisservice'),42,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='infrastructure'),43,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='slaStart'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='slaEnd'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startPlannedDate'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endPlannedDate'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractName'),3,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractCode'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CONFERMA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceResult'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CONFERMA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceDescription'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CONFERMA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceId'),3,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CONFERMA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceCode'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='ANOMALIA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceResult'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='ANOMALIA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceDescription'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='ANOMALIA'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceCode'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='note'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startPlannedDate'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startWorkDate'),4,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endWorkDate'),5,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='eventType'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endPlannedDate'),3,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='teamId'),6,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='teamName'),7,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='spentTime'),9,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='onFieldAssistant'),8,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startWorkDate'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endWorkDate'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='flagFIR'),2,'N','Y','N','Y');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CARICAMENTO_AS_BUILT'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='asBuiltId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CARICAMENTO_AS_BUILT'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractStartWorkDate'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='CARICAMENTO_AS_BUILT'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractEndWorkDate'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='ANNULLAMENTO_AS_BUILT'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ttKOreason'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='startPlannedDate'),2,'Y','N','N','Y');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='endPlannedDate'),3,'N','N','N','Y');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractName'),1,'Y','N','Y','Y');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractCode'),0,'Y','N','Y','Y');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='asBuiltId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractStartWorkDate'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='FINE_LAVORI_SUBAPPALTO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractEndWorkDate'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='SOSPENSIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='accountingDone'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_KO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceResult'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_KO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceDescription'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_KO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceCode'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceId'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='accountingUser'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='accountingDone'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='accountingNote'),3,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='planningServiceId'),0,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractStartWorkDate'),2,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractEndWorkDate'),3,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='validationNote'),1,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='contractId'),1,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='customerId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ids'),2,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractStartWorkDate'),3,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),'A',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='subContractEndWorkDate'),4,'N','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ttId'),0,'Y','N','N','N');
INSERT INTO tipi_dati_tecnici_att_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='ttKOreason'),0,'Y','N','N','N');

-- TDTA_TAGS

delete TDTA_TAGS;

-- ACTIVITY_PROPERTIES_GROUP (multitables)

delete AP;
delete AT_AP_GROUPS;
delete AP_GROUPS;


insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'customerId') ,0 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'contractId') ,1 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'ids') ,2 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'ttId') ,3 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'ttKOreason') ,4 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractStartWorkDate') ,5 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'AS_BUILT') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractEndWorkDate') ,6 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'maker') ,0 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'slaStart') ,1 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'slaEnd') ,2 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'startPlannedDate') ,3 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'customerId') ,4 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'contractId') ,5 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractCode') ,6 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractName') ,7 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'teamId') ,8 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'teamName') ,9 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'workingGroupCode') ,10 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'startWorkDate') ,11 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'endWorkDate') ,12 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'spentTime') ,13 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'address') ,14 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'cadastralCode') ,15 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'endPlannedDate') ,16 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'estimatedDuration') ,17 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'pop') ,18 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'popId') ,19 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'projectId') ,20 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'ring') ,21 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'ringId') ,22 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractStartWorkDate') ,23 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'subContractEndWorkDate') ,24 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'onFieldAssistant') ,25 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'accountingUser') ,26 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'accountingNote') ,27 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'validationNote') ,28 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'planningServiceId') ,29 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'planningServiceResult') ,30 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'planningServiceCode') ,31 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'planningServiceDescription') ,32 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'accountingDone') ,33 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'onFieldIntegrationDisabled') ,34 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'flagFIR') ,35 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'updateDatabase') ,36 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'test') ,37 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'junction') ,38 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'civil') ,39 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'cableLaying') ,40 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'survey') ,41 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'updateDatabaseF1') ,42 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'updateDatabaseF2') ,43 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'opticalConnectionOSU') ,44 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'opticalConnectionOLT') ,45 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'restoration') ,46 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'design') ,47 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'patchCord') ,48 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'pathSurvey') ,49 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'installationPlaceSurvey') ,50 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'installationReview') ,51 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'worksManning') ,52 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'measurements') ,53 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'quarterSummary') ,54 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'defectWithDisservice') ,55 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'defectWithoutDisservice') ,56 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);
insert into ap values ( seq_ap.nextval ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = 'LAVORO') ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = 'infrastructure') ,57 ,(select id from ap_groups where descrizione = '') ,null ,'Y' ,null ,null);

-- TIPI_ATTIVITA_TIPI_ALLEGATI

INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'CMI','MISURE',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_CMI'));
INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'DCO','DOCUMENTAZIONE COLLAUDO',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_DCO'));
INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'DOF','DOCUMENTAZIONE FOTOGRAFICA',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_DOF'));
INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'VAR','VARIE',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_VAR'));
INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'WKI','WALK IN',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_WKI'));
INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval,(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = 'LAVORO'),'FIR','FIR',(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '__DTC_FIR'));

-- GRUPPI

INSERT INTO GRUPPI values(seq_gruppi.nextval,'Amministratore di ART','y',NULL,NULL,'ADMIN',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Gruppo da utilizzare nei test di regressione',NULL,NULL,NULL,'API::GRUPPO::01',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Manager territoriale (Nord, Centro e Sud)',NULL,NULL,NULL,'AREA_MANAGER',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Assistente Tecnico per OC non-OF',NULL,NULL,NULL,'AT',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Assistente Tecnico per lavori civili e posa cavi',NULL,NULL,NULL,'AT_CIVILE',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Assistente Tecnico per giunzioni',NULL,NULL,NULL,'AT_GIUNZIONI',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Responsabile di una o più città',NULL,NULL,NULL,'CITY_MANAGER',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Responsabile della contabilità progetti',NULL,NULL,NULL,'CONTABILE',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Coordinatore gruppo Assistenti Tecnici per lavori civili e posa cavi',NULL,NULL,NULL,'COORDINATORE_AT_CIVILE',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Coordinatore gruppo Assistenti Tecnici per giunzioni',NULL,NULL,NULL,'COORDINATORE_AT_GIUNZIONI',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'COORDINATORE_DELIVERY',NULL,NULL,NULL,'COORDINATORE_DELIVERY',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'FOCAL_POINT',NULL,NULL,NULL,'FOCAL_POINT',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Responsabile della pianificazione attività di campo',NULL,NULL,NULL,'PLAN_AND_PROGRAM',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Supervisiona le attività di tutti i Centri Lavoro',NULL,NULL,NULL,'PM',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Si occupa di apportare le modifiche al progetto “in realizzazione”. Fa capo al Supporto Progettazione',NULL,NULL,NULL,'PROGETTISTA',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Centro Progettazione',NULL,NULL,NULL,'PROGETTISTA_CP',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'QSA',NULL,NULL,NULL,'QSA',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'È responsabile dell''apertura ed avanzamento della permessistica sulle Aree Permesso. Fa capo al Supporto Progettazione',NULL,NULL,NULL,'REFERENTE_PERMESSI_AP',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'È responsabile dell''apertura ed avanzamento della permessistica sui Building. In genere è una società esterna',NULL,NULL,NULL,'REFERENTE_PERMESSI_BUILDING',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Manager Regionale che saranno abilitati sui Centri Lavoro di pertinenza.',NULL,NULL,NULL,'REGIONAL_MANAGER',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Amministratore privilegiato di ART','Y','Y',NULL,'ROOT',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Accede in modifica ai soli Centri Lavoro abilitati e alle sole azioni abilitate',NULL,NULL,NULL,'SERVICE',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Fornisce supporto (e coordina) ai progettisti ed ai referenti permessi',NULL,NULL,NULL,'SUPPORTO_PROGETTAZIONE',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Utente di ART',NULL,NULL,NULL,'USER',NULL,NULL);
INSERT INTO GRUPPI values(seq_gruppi.nextval,'Utente per Construction TIM + OLO',NULL,NULL,NULL,'USER01',NULL,NULL);

update GRUPPI set DESCRIZIONE='Amministratore di ART',AMMINISTRATORE='y',ROOT=NULL,MORTO=NULL,NOME='ADMIN',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'ADMIN';
update GRUPPI set DESCRIZIONE='Gruppo da utilizzare nei test di regressione',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='API::GRUPPO::01',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'API::GRUPPO::01';
update GRUPPI set DESCRIZIONE='Manager territoriale (Nord, Centro e Sud)',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='AREA_MANAGER',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'AREA_MANAGER';
update GRUPPI set DESCRIZIONE='Assistente Tecnico per OC non-OF',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='AT',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'AT';
update GRUPPI set DESCRIZIONE='Assistente Tecnico per lavori civili e posa cavi',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='AT_CIVILE',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'AT_CIVILE';
update GRUPPI set DESCRIZIONE='Assistente Tecnico per giunzioni',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='AT_GIUNZIONI',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'AT_GIUNZIONI';
update GRUPPI set DESCRIZIONE='Responsabile di una o più città',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='CITY_MANAGER',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'CITY_MANAGER';
update GRUPPI set DESCRIZIONE='Responsabile della contabilità progetti',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='CONTABILE',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'CONTABILE';
update GRUPPI set DESCRIZIONE='Coordinatore gruppo Assistenti Tecnici per lavori civili e posa cavi',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='COORDINATORE_AT_CIVILE',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'COORDINATORE_AT_CIVILE';
update GRUPPI set DESCRIZIONE='Coordinatore gruppo Assistenti Tecnici per giunzioni',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='COORDINATORE_AT_GIUNZIONI',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'COORDINATORE_AT_GIUNZIONI';
update GRUPPI set DESCRIZIONE='COORDINATORE_DELIVERY',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='COORDINATORE_DELIVERY',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'COORDINATORE_DELIVERY';
update GRUPPI set DESCRIZIONE='FOCAL_POINT',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='FOCAL_POINT',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'FOCAL_POINT';
update GRUPPI set DESCRIZIONE='Responsabile della pianificazione attività di campo',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='PLAN_AND_PROGRAM',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'PLAN_AND_PROGRAM';
update GRUPPI set DESCRIZIONE='Supervisiona le attività di tutti i Centri Lavoro',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='PM',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'PM';
update GRUPPI set DESCRIZIONE='Si occupa di apportare le modifiche al progetto “in realizzazione”. Fa capo al Supporto Progettazione',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='PROGETTISTA',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'PROGETTISTA';
update GRUPPI set DESCRIZIONE='Centro Progettazione',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='PROGETTISTA_CP',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'PROGETTISTA_CP';
update GRUPPI set DESCRIZIONE='QSA',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='QSA',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'QSA';
update GRUPPI set DESCRIZIONE='È responsabile dell''apertura ed avanzamento della permessistica sulle Aree Permesso. Fa capo al Supporto Progettazione',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='REFERENTE_PERMESSI_AP',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'REFERENTE_PERMESSI_AP';
update GRUPPI set DESCRIZIONE='È responsabile dell''apertura ed avanzamento della permessistica sui Building. In genere è una società esterna',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='REFERENTE_PERMESSI_BUILDING',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'REFERENTE_PERMESSI_BUILDING';
update GRUPPI set DESCRIZIONE='Manager Regionale che saranno abilitati sui Centri Lavoro di pertinenza.',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='REGIONAL_MANAGER',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'REGIONAL_MANAGER';
update GRUPPI set DESCRIZIONE='Amministratore privilegiato di ART',AMMINISTRATORE='Y',ROOT='Y',MORTO=NULL,NOME='ROOT',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'ROOT';
update GRUPPI set DESCRIZIONE='Accede in modifica ai soli Centri Lavoro abilitati e alle sole azioni abilitate',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='SERVICE',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'SERVICE';
update GRUPPI set DESCRIZIONE='Fornisce supporto (e coordina) ai progettisti ed ai referenti permessi',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='SUPPORTO_PROGETTAZIONE',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'SUPPORTO_PROGETTAZIONE';
update GRUPPI set DESCRIZIONE='Utente di ART',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='USER',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'USER';
update GRUPPI set DESCRIZIONE='Utente per Construction TIM + OLO',AMMINISTRATORE=NULL,ROOT=NULL,MORTO=NULL,NOME='USER01',PRIVATO=NULL,AUTOGENERATO=NULL where NOME= 'USER01';

delete gruppi_comp;


-- TIPI_ATTIVITA_AP_GRUPPI_PERM

delete TIPI_ATTIVITA_AP_GRUPPI_PERM;

-- RPT_GRUPPI

delete RPT_PERMESSI;
delete RPT;
delete RPT_GRUPPI;

-- RPT


-- RPT_PERMESSI


-- PERMISSION_ACTION

delete permission_action;
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP2'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP2'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_3'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select ID_STATO from stati where nome='KART_STATO_3'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::01'),(select ID_STATO from stati where nome='API::STATO::01'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::01'),(select ID_STATO from stati where nome='API::STATO::01'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::01'),(select id_action from action where nome='API::AZIONE::02'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::01'),(select id_action from action where nome='API::AZIONE::02'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_STATO from stati where nome='API::STATO::CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_STATO from stati where nome='API::STATO::CHIUSA'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_STATO from stati where nome='API::STATO::ERRORE'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select ID_STATO from stati where nome='API::STATO::02'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_STATO from stati where nome='API::STATO::ERRORE'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_STATO from stati where nome='API::STATO::CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::CHIUSURA'),(select ID_STATO from stati where nome='API::STATO::CHIUSA'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_STATO from stati where nome='API::STATO::ERRORE'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='API::AZIONE::ERRORE'),(select ID_STATO from stati where nome='API::STATO::ERRORE'),(select id_gruppo from gruppi where NOME='API::GRUPPO::01'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP2'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP2'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_2'),(select id_action from action where nome='KART_STEP1'),(select ID_STATO from stati where nome='KART_STATO_1'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_action from action where nome='KART_ERRORE'),(select ID_STATO from stati where nome='ERRORE'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_action from action where nome='KART_ERRORE'),(select ID_STATO from stati where nome='ERRORE'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_action from action where nome='KART_STEP3'),(select ID_STATO from stati where nome='KART_STATO_3'),(select id_gruppo from gruppi where NOME='USER'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='ACQUISIZIONE'),(select ID_STATO from stati where nome='ACQUISITA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='ACQUISIZIONE'),(select ID_STATO from stati where nome='ACQUISITA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select ID_STATO from stati where nome='ACQUISITA'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select ID_STATO from stati where nome='ACQUISITA'),'C',(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='RICHIESTA_LAVORAZIONE'),(select ID_STATO from stati where nome='NUOVO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='RICHIESTA_LAVORAZIONE'),(select ID_STATO from stati where nome='NUOVO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI_OFFLINE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI_OFFLINE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='LAVORABILE_DA_SUBAPPALTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='NUOVO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_action from action where nome='CONFERMA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_action from action where nome='CONFERMA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_action from action where nome='ANOMALIA'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_action from action where nome='ANOMALIA'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='LAVORABILE_DA_SIRTI'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_action from action where nome='MODIFICA_DATI'),(select ID_STATO from stati where nome='ANOMALIA'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='FINE_LAVORI_SIRTI'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='FINE_LAVORI_SIRTI'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='SOSPENSIONE'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='SOSPENSIONE'),(select ID_STATO from stati where nome='INOLTRATO_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='RENDICONTAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO_SIRTI_OFFLINE'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO_SIRTI_OFFLINE'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_action from action where nome='ANNULLAMENTO_SIRTI_OFFLINE'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='CARICAMENTO_AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='CARICAMENTO_AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='AS_BUILT_NON_NECESSARIO'),(select ID_STATO from stati where nome='ESPLETATO_SENZA_AS_BUILT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='AS_BUILT_NON_NECESSARIO'),(select ID_STATO from stati where nome='ESPLETATO_SENZA_AS_BUILT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_action from action where nome='CHIUSURA_AS_BUILT'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_action from action where nome='CHIUSURA_AS_BUILT'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_action from action where nome='ANNULLAMENTO_AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_action from action where nome='ANNULLAMENTO_AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_RENDICONTAZIONE'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_action from action where nome='TIMEOUT'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_action from action where nome='TIMEOUT'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_OK'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_OK'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_KO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ATTESA_ANNULLAMENTO'),(select id_action from action where nome='RICHIESTA_ANNULLAMENTO_KO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='AVANZAMENTO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='RIPRESA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='RIPRESA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SIRTI'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='ANNULLAMENTO_FORZATO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='ASSURANCE_ANNULLAMENTO'),(select id_action from action where nome='ANNULLAMENTO_FORZATO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI'),(select ID_STATO from stati where nome='ESPLETATO'),(select id_gruppo from gruppi where NOME='SERVICE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='ANNULLAMENTO'),(select ID_STATO from stati where nome='ANNULLATO'),(select id_gruppo from gruppi where NOME='AT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='PIANIFICAZIONE'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI_SUBAPPALTO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='FINE_LAVORI_SUBAPPALTO'),(select ID_STATO from stati where nome='ATTESA_GESTIONE_AS_BUILT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='MODIFICA'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select ID_STATO from stati where nome='IN_LAVORAZIONE_SUBAPPALTO'),(select id_action from action where nome='INOLTRO_RENDICONTAZIONE'),(select ID_STATO from stati where nome='QUARANTENA'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='START'),'A',(select ID_STATO from stati where nome='APERTA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='RICHIESTA_APERTURA_TT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='APERTA'),(select id_action from action where nome='RICHIESTA_APERTURA_TT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_OK'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_APERTURA_TT'),(select id_action from action where nome='APERTURA_TT_KO'),(select ID_STATO from stati where nome='KO'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_KO'),(select ID_STATO from stati where nome='KO_TT'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ROOT'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='ADMIN'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='AT_CIVILE'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='COORDINATORE_AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='AT_GIUNZIONI'));
INSERT INTO permission_action values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select ID_STATO from stati where nome='ATTESA_CHIUSURA_TT'),(select id_action from action where nome='CHIUSURA_TT_OK'),(select ID_STATO from stati where nome='CHIUSA'),(select id_gruppo from gruppi where NOME='PLAN_AND_PROGRAM'));

-- TDTA_UI_URL

delete TDTA_UI_URL;

-- SERIALIZZAZIONE_ENTITA

delete SERIALIZZAZIONE_ENTITA;
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Centro Lavoro','["system"]["properties"]["workingGroupCode"]','Centro Lavoro',NULL,NULL,'string','10',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','ID','["id"]','ID attività',NULL,NULL,'activityId','0',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Descrizione','["info"]["description"]','Descrizione',NULL,NULL,'string','1',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Stato','["info"]["status"]','Stato ROE',NULL,NULL,'string','2',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Assegnatario','["properties"]["maker"]','Assegnatario',NULL,NULL,'string','50',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Codice subappalto','["properties"]["subContractCode"]','Codice subappalto',NULL,NULL,'string','51',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Ragione sociale subappalto','["properties"]["subContractName"]','Ragione sociale subappalto',NULL,NULL,'string','52',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Identificativo squadra','["properties"]["teamId"]','Identificativo squadra',NULL,NULL,'string','55',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Nome squadra','["properties"]["teamName"]','Nome squadra',NULL,NULL,'string','56',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Data inizo subappalto','["properties"]["startPlannedDate"]','Data inizo subappalto',NULL,NULL,'timestamp_with_time_zone','53',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Data fine subappalto','["properties"]["endPlannedDate"]','Data fine subappalto',NULL,NULL,'timestamp_with_time_zone','54',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Data inizo squadra','["properties"]["slaStart"]','Data inizo squadra',NULL,NULL,'timestamp_with_time_zone','57',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Data fine squadra','["properties"]["slaEnd"]','Data fine squadra',NULL,NULL,'timestamp_with_time_zone','58',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Aggiornamento banca dati','["properties"]["updateDatabase"]','Aggiornamento banca dati',NULL,NULL,'boolean','65',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Collaudo','["properties"]["test"]','Collaudo',NULL,NULL,'boolean','64',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Giunzione','["properties"]["junction"]','Giunzione',NULL,NULL,'boolean','63',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Lavori civili','["properties"]["civil"]','Lavori civili',NULL,NULL,'boolean','61',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Posa cavo','["properties"]["cableLaying"]','Posa cavo',NULL,NULL,'boolean','62',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Sopralluogo','["properties"]["survey"]','Sopralluogo',NULL,NULL,'boolean','60',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Aggiornamanto Banca Dati F1','["properties"]["updateDatabaseF1"]','Aggiornamanto Banca Dati F1',NULL,NULL,'boolean','66',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Aggiornamanto Banca Dati F2','["properties"]["updateDatabaseF2"]','Aggiornamanto Banca Dati F2',NULL,NULL,'boolean','67',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','OSU','["properties"]["opticalConnectionOSU"]','OSU',NULL,NULL,'boolean','68',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','OLT','["properties"]["opticalConnectionOLT"]','OLT',NULL,NULL,'boolean','69',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Ripristino','["properties"]["restoration"]','Ripristino',NULL,NULL,'boolean','70',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','Progettazione','["properties"]["design"]','Progettazione',NULL,NULL,'boolean','59',NULL);
INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('ATTIVITA','LAVORO','NetworkId','["system"]["properties"]["networkId"]','NetworkId',NULL,NULL,'string','3',NULL);

-- MODULI

insert into MODULI values (1,'INVENTARIO','inventario','#8080FF','#F0F0FF','#0000A0',40);
insert into MODULI values (2,'ATTIVITA','attivita','#FFD400','#FFF7F1','#E5B700',20);
insert into MODULI values (3,'EVENTI','eventi','#FC848E','#FFF0F0','#A00000',10);
insert into MODULI values (4,'SLA','sla','green','#F0FFF0','#00A000',30);
insert into MODULI values (5,'REQUEST','request','violet','#FFF0FF','#D700E2',50);
insert into MODULI values (6,'SETTAGGI','settaggi','#00E0E0','#F0FFFF','#00A0A0',60);
insert into MODULI values (8,'HOME','home','#c0c0c0','#ffffff','#a0a0a0',0);
insert into MODULI values (9,'DOCUMENTI','documenti','#6ddeb4','#d3ffe5','#31e9a5',70);
insert into MODULI values (10,'REPORT','report','#E00000','#FFF7F1','#990000',65);

UPDATE MODULI set ID_MODULO=1,DESCRIZIONE='INVENTARIO',URL='inventario',COLORE='#8080FF',COLORE_CHIARO='#F0F0FF',COLORE_SCURO='#0000A0',ORDINE=40 where ID_MODULO= 1;
UPDATE MODULI set ID_MODULO=2,DESCRIZIONE='ATTIVITA',URL='attivita',COLORE='#FFD400',COLORE_CHIARO='#FFF7F1',COLORE_SCURO='#E5B700',ORDINE=20 where ID_MODULO= 2;
UPDATE MODULI set ID_MODULO=3,DESCRIZIONE='EVENTI',URL='eventi',COLORE='#FC848E',COLORE_CHIARO='#FFF0F0',COLORE_SCURO='#A00000',ORDINE=10 where ID_MODULO= 3;
UPDATE MODULI set ID_MODULO=4,DESCRIZIONE='SLA',URL='sla',COLORE='green',COLORE_CHIARO='#F0FFF0',COLORE_SCURO='#00A000',ORDINE=30 where ID_MODULO= 4;
UPDATE MODULI set ID_MODULO=5,DESCRIZIONE='REQUEST',URL='request',COLORE='violet',COLORE_CHIARO='#FFF0FF',COLORE_SCURO='#D700E2',ORDINE=50 where ID_MODULO= 5;
UPDATE MODULI set ID_MODULO=6,DESCRIZIONE='SETTAGGI',URL='settaggi',COLORE='#00E0E0',COLORE_CHIARO='#F0FFFF',COLORE_SCURO='#00A0A0',ORDINE=60 where ID_MODULO= 6;
UPDATE MODULI set ID_MODULO=8,DESCRIZIONE='HOME',URL='home',COLORE='#c0c0c0',COLORE_CHIARO='#ffffff',COLORE_SCURO='#a0a0a0',ORDINE=0 where ID_MODULO= 8;
UPDATE MODULI set ID_MODULO=9,DESCRIZIONE='DOCUMENTI',URL='documenti',COLORE='#6ddeb4',COLORE_CHIARO='#d3ffe5',COLORE_SCURO='#31e9a5',ORDINE=70 where ID_MODULO= 9;
UPDATE MODULI set ID_MODULO=10,DESCRIZIONE='REPORT',URL='report',COLORE='#E00000',COLORE_CHIARO='#FFF7F1',COLORE_SCURO='#990000',ORDINE=65 where ID_MODULO= 10;

-- MODULI_DETTAGLIO

delete permission_moduli;
delete moduli_dettaglio;
insert into MODULI_DETTAGLIO values (1066,8,'Lista delle attivita'' in carico a #operatore#','ricerca_templ.html','<INPUT TYPE=hidden NAME="da_home" VALUE="yes">
<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
<INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
<INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
<INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
<INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',400,NULL,NULL,NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (87,8,'Vedi attivita','ricerca_templ.html','
<INPUT TYPE=hidden NAME="lista" VALUE="YES">
<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
<INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
<INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
<INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
<INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
<INPUT TYPE=hidden NAME="da_home" VALUE="yes">',30,NULL,'S',NULL,NULL,'Image_Commento.JPG');

insert into MODULI_DETTAGLIO values (90,8,'Vedi request','ricerca_aperti_da.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',35,NULL,'S',NULL,NULL,'Image_Commento.JPG');

insert into MODULI_DETTAGLIO values (93,8,'Crea nuova attivita','nuovo_ticket.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',10,NULL,'S',NULL,NULL,'Image_Commento.JPG');

insert into MODULI_DETTAGLIO values (94,6,'Statistiche di accesso','sessioni.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',90,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1033,1,'Motore di ricerca inventario','consultazione_inventario.html',NULL,40,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1034,6,'Gestione alerting',NULL,NULL,15,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1035,6,'Gestione query','gestione_query.html',NULL,70,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1040,3,'Schedulazione Controlli PROBE','schedulatore.html',NULL,80,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1041,3,'Vedi allarmi PROBE','vista_luna.html',NULL,90,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1042,9,'Edita struttura documentazione','documentazione_edita.html',NULL,20,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1043,9,'Consultazione Documentazione','documentazione.html',NULL,10,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1044,6,'Gestione Dashboard','genera_dashboard.html',NULL,75,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1045,2,'Vista Tabellare','../',NULL,70,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1046,6,'Imposta Notifica Apertura','notifiche_sms_apertura.html',NULL,76,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1047,2,'Crea attivita'' Multiple','nuovo_multiticket.html',NULL,76,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1050,6,'Controllo Antivirus','gestione_antivirus.html',NULL,65,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1051,10,'Vista Attivita'' in Lavorazione','vista_report.html','<input type=''hidden'' name=''tab'' value=''1''>
<input type=''hidden'' name=''titolo'' value="VISTA ATTIVITA IN LAVORAZIONE">',40,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1052,10,'Storia Lavorazione Attivita''','vista_report.html','<input type=''hidden'' name=''tab'' value=''2''>
<input type=''hidden'' name=''titolo'' value="STORIA LAVORAZIONE ATTIVITA">',50,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1053,10,'Query relative al modulo report','query.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO"><INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N"><INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1"><INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE"><INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',100,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (48,6,'Cambio password','cambio_password.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,NULL,NULL,NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (49,6,'Profilo delle notifiche','notifica_visualizza.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (50,6,'Modifica dati personali di #operatore#','modifica_dati_pers.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,NULL,NULL,NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (51,6,'Gestione accessi','modifica_dati_pers.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (54,1,'Gestione Managed Elements','gestione_gruppi.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (56,5,'Ricerca Request per ID','ricerca_id.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (63,2,'Gestione Attivita''',NULL,'<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',70,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1054,6,'Gestione Parser','../','<input type=hidden name=modulo value="settaggi">',120,NULL,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (70,4,'Gestione indicatori','gestione_tipologie.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (76,6,'Setup ART','gestione_enti.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1,2,'Lista delle attivita'' non chiuse e gestite da #operatore#','ricerca_templ.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (2,2,'Vista attivita'' per stato / aperte da #operatore#','vista_attivita.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (3,2,'Crea nuova attivita','nuovo_ticket.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (4,2,'Ricerca attivita'' per ID','ricerca_id.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (5,2,'Motore di ricerca attivita''','ricerca_avanzata.html','<INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
<INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
<INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (6,2,'Query relative al modulo attivita''','query.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',60,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (24,3,'Query relative al modulo eventi','query.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',70,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (25,3,'Gestione code delle notifiche','gestione_coda_notifiche.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',60,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (27,4,'Vista indicatori',NULL,'<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (33,1,'Vista grafica ME','parco_macchine_grafica.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (35,1,'Query relative al modulo inventario','query.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (36,5,'Richieste ad Art','query.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,NULL,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1056,6,'Gestione Password','gestione_password.html','<input type=hidden name=modulo value="settaggi">',10,51,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (37,5,'Lista richieste attive','vedi_request.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,36,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (38,5,'Storico richieste Art','posta_storico.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,36,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (52,6,'Gestione Utenti','gestione_utenti.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,51,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (88,8,'gestite da $art{nome_cognome_operatore}','ricerca_templ.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',40,87,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (89,8,'aperte da $art{nome_cognome_operatore}','ricerca_aperti_da.html','
		  <INPUT TYPE=hidden NAME="lista" VALUE="YES">
		  <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
		  <INPUT TYPE=hidden NAME="stato_valore_p" VALUE="N">
		  <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
		  <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
		  <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
		  <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',50,87,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (91,8,'il cui mittente &egrave; $art{nome_cognome_operatore}','request_da.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',10,90,'S',NULL,NULL,'Image_Commento.JPG');

insert into MODULI_DETTAGLIO values (92,8,'assegnate a $art{nome_cognome_operatore}','request_a.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',20,90,'S',NULL,NULL,'Image_Commento.JPG');

insert into MODULI_DETTAGLIO values (98,6,'Configurazione icone delle azioni','controllo_icone.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',90,76,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (99,1,'Import dati tecnici ME','gestione_import.html','
        <INPUT TYPE=hidden NAME="lista" VALUE="YES">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
        <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
        <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
        <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
        <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">
        <INPUT TYPE=hidden NAME="da_home" VALUE="yes">',60,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (53,6,'Gestione Gruppi','gestione_gruppi.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,51,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (55,1,'Inserimento nuovo Managed Element','new_sistemi.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',70,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (57,1,'Gestione Tipi Dati Tecnici ME','gestione_tipi_dati_tecn.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (58,1,'Gestione Categorie (raggruppamento)','gestione_categorie.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (59,1,'Gestione Service Items (raggruppamento)','gestione_service_item.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (60,1,'Associazione/Dissociazione ME/Service Items (raggruppamento)','associa_service_item.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (61,1,'Gestione Icone Oggetti','gestione_tipi_oggetto.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',60,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (64,2,'Gestione diagrammi di flusso','new_permission_action.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,63,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1036,6,'Set-up Alerting','set_up_allarmi.html',NULL,10,1034,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1037,6,'Vista alert aperti','allarmi_aperti.html',NULL,11,1034,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1038,6,'Vista alert chiusi','allarmi_chiusi.html',NULL,12,1034,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1039,6,'Vista alert scaduti','allarmi_scaduti.html',NULL,13,1034,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (1055,6,'Mail Parser','mail_parser.html','<input type=hidden name=modulo value="settaggi">',10,1054,'S',NULL,NULL,NULL);
insert into MODULI_DETTAGLIO values (65,2,'Gestione stati','config_stati.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,63,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (66,2,'Gestione azioni','config_action.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,63,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (67,2,'Gestione Tipi Attivita''','gestione_tipi_att.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,63,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (68,2,'Gestione Tipi Dati Tecnici attivita''','gest_tipi_dati_tecnici_att.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,63,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (86,1,'Gestione Tipologie (raggruppamento)','gestione_tipologie.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,54,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (71,4,'Gestione Indicatori disponibilita'' Managed Elements','gestione_indicatori_disp.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,70,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (72,4,'Gestione Indicatori tempi su attivita''','gestione_indicatori_att.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,70,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (73,4,'Gestione parametri TI e IP','gestione_TIIP.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,70,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (74,4,'Gestione classi disponibilita''','gestione_classi.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',40,70,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (75,4,'Gestione enti','gestione_enti.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',50,70,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (77,6,'Configurazione ART','config_art.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,76,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (78,6,'Configurazione menu','config_menu.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,76,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (79,6,'Configurazione icona cliente','config_oggetti.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,76,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (28,4,'Vista indicatori tempi su attivita''','indicatori_tempi_attivita.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',10,27,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (29,4,'Vista indicatori disponibilita'' Managed Elements','indicatori_disponibilita.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',20,27,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (30,4,'Analisi indicatori disponibilita'' Managed Elements','analisi_indicatori_disp.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',30,27,'S',NULL,NULL,NULL);

insert into MODULI_DETTAGLIO values (1000,1,'Export dati tecnici ME','gestione_export.html','<INPUT TYPE=hidden NAME="attivo_p" VALUE="STATO">
                <INPUT TYPE=hidden NAME="stato_criterio_p" VALUE="N">
                <INPUT TYPE=hidden NAME="richiesta_non_chiuse_personali_p" VALUE="1">
                <INPUT TYPE=hidden NAME="attivo_p" VALUE="OPERATORE">
                <INPUT TYPE=hidden NAME="operatore_valore_p" VALUE="$art{id_operatore}">',60,54,'S',NULL,NULL,NULL);


-- PERMISSION_MODULI

insert into permission_moduli values(1,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(2,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(3,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(4,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(5,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(6,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(24,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(25,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(27,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(28,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(29,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(30,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(33,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(35,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(36,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(37,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(38,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(48,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(49,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(50,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(51,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(52,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(53,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(54,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(55,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(56,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(57,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(58,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(59,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(60,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(61,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(62,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(63,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(64,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(65,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(66,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(67,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(68,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(70,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(71,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(72,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(73,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(74,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(75,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(76,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(77,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(78,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(79,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(80,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(81,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(86,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(87,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(88,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(89,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(90,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(91,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(92,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(93,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(94,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(98,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(99,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1000,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1033,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1034,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1035,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1036,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1037,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1038,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1039,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1040,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1041,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1042,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1043,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into permission_moduli values(1047,'GRUPPI', (select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));

-- ATTIVITA_PER_TIPI_SISTEMA

delete ATTIVITA_PER_TIPI_SISTEMA;
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='kartatt'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='kart'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='tmp1'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='kart'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='tmp2'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='kart'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::01'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='API::TEST'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='API::TEST::03'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='API::TEST'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='KART_HISTORY'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='KART_HISTORY'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='WZ_LC'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='WZ'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='CAVO'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='NODO'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='CANTIERE'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='LAVORO'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='LAVORO'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='SQUADRA'),NULL);
INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values((select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='AS_BUILT'),(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='SUBAPPALTO'),NULL);

-- ICONE_AZIONI

insert into ICONE_AZIONI values ('ACTION_RIPRISTINA','Ripristina l''azione',10,'img/icons/sveglia2.gif','img/icons/sveglia2.gif');
insert into ICONE_AZIONI values ('ACTION_MODIFICA','Modifica l''azione',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('SISTEMA_MODIFICA','Modifica il sistema',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('TIPI_SOTTOATTIVITA_CREA','Crea un nuovo tipo sottoattivita',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('TIPI_SOTTOATTIVITA_CANCELLA','Cancella il tipo sottattivita',10,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('TIPI_SOTTOATTIVITA_MODIFICA','Modifica il tipo sottoattivita',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('TDTA_MODIFICA','Modifica il tipo dato tecnico attivita',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('STATO_CREA','Crea uno stato',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('NOTIFICA_FORZA','Forza la consegna della notifica',10,'img/icons/forza2.gif','img/icons/forza_crit2.gif');
insert into ICONE_AZIONI values ('NOTIFICA_ARCHIVIA','Archivia la notifica',10,'img/icons/folder2.gif','img/icons/folder_crit2.gif');
insert into ICONE_AZIONI values ('MANUTENZIONE_MODIFICA','Modifica degli oggetti a questa attivita manutentiva',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('MANUTENZIONE_CANCELLA','Cancella degli oggetti dall''attivita manutentiva',10,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('EVENTO_RICONOSCIMENTO','Riconosci l''evento',10,'img/icons/prendi2.gif','img/icons/prendi_crit2.gif');
insert into ICONE_AZIONI values ('EVENTO_ARCHIVIA','Archivia l''evento',10,'img/icons/folder2.gif','img/icons/folder_crit2.gif');
insert into ICONE_AZIONI values ('CATEGORIA_MODIFICA','Modifica la categoria',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('CATEGORIA_CREA','Crea una categoria',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('CATEGORIA_CANCELLA','Cancella una categoria',10,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('SERVICE_ITEM_VEDI','Vedi il Service Item',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SERVICE_ITEM_RIPRISTINA','Ripristina il Service Item',10,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('SERVICE_ITEM_SOSPENDI','Sospendi il Service Item',10,'img/icons/zzz2.gif','img/icons/zzz2.gif');
insert into ICONE_AZIONI values ('SERVICE_ITEM_MODIFICA','Modifica il Service Item',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('DATI_TECNICI_VEDI','Vedi i dati tecnici',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('DATI_TECNICI_MODIFICA','Modifica i dati tecnici',10,'img/icons/modifica_bau2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('DATI_TECNICI_RIPRISTINA','Ripristina il dato tecnico',10,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('DATI_TECNICI_SOSPENDI','Sospendi il dato tecnico',10,'img/icons/zzz2.gif','img/icons/zzz2.gif');
insert into ICONE_AZIONI values ('TIPO_OGGETTO_MODIFICA','Modifica il tipo oggetto',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('TIPO_OGGETTO_RIPRISTINA','Ripistina il tipo oggetto',10,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('TIPO_OGGETTO_SOSPENDI','Sospendi il tipo oggetto',10,'img/icons/zzz2.gif','img/icons/zzz2.gif');
insert into ICONE_AZIONI values ('ME_MODIFICA','Modifica il tipo ME',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('ME_CANCELLA','Cancella il tipo ME',10,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('VEDI','Vedi',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('MODIFICA','Modifica',20,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('CANCELLA','Cancella',30,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ATT_VEDI','vedi indicatore',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ATT_MODIFICA','modifica indicatore',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ATT_CANCELLA','cancella indicatore',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ATT_CREA','crea indicatore',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_DISP_VEDI','vedi indicatore',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_DISP_MODIFICA','modifica indicatore',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_DISP_CANCELLA','cancella indicatore',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_DISP_CREA','crea indicatore',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_TI_VEDI','vedi tipologia di intervento',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('QUERY_MODIFICA','Modifica Query',10,'img/icons/modifica2.gif','img/icons/modifica2.gif');
insert into ICONE_AZIONI values ('VEDI_STORICO_ATTIVITA','Visualizzazione Storico Attivita',40,'img/icons/piu.gif','img/icons/piu.gif');
insert into ICONE_AZIONI values ('ATTIVITA_RELATIVE','Elenco Attivita',20,'img/icons/piu.gif','img/icons/piu.gif');
insert into ICONE_AZIONI values ('DOCUMENTI_ELIMINA','Elimina il documento',50,'img/icons/cancella2.gif','img/icons/cancella2.gif');
insert into ICONE_AZIONI values ('VEDI_STORICO_SISTEMA','Visualizzazione Storico Sistema',40,'img/icons/storico.gif','img/icons/storico.gif');
insert into ICONE_AZIONI values ('ATTIVITA_REPORT','Prepara Report',100,'img/icons/printer.gif','img/icons/printer.gif');
insert into ICONE_AZIONI values ('ATTIVITA_CREA_FIGLIO','Crea figlio',40,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('PARSER_CREA','Crea Parser',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('PARSER_EDIT','Modifica Parser',10,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('PARSER_CANCELLA','Cancella Parser',10,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('PARSERPLUS_CREA','Crea Parser Plus',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_TI_MODIFICA','modifica tipologia di intervento',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_TI_CANCELLA','cancella tipologia di intervento',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_TI_CREA','crea tipologia di intervento',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_IP_VEDI','vedi influenza sulle performance',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_IP_MODIFICA','modifica influenza sulle performance',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_IP_CANCELLA','cancella influenza sulle performance',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_IP_CREA','crea influenza sulle performance',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_CLASSI_VEDI','vedi classe ME',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_CLASSI_MODIFICA','modifica classe ME',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_CLASSI_CANCELLA','cancella classe ME',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_CLASSI_CANCELLA_CRIT','verifica impedimenti',40,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_CLASSI_CREA','crea classe ME',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_ENTI_VEDI','vedi ente',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ENTI_MODIFICA','modifica ente',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ENTI_CANCELLA','cancella ente',30,'img/icons/cancella2.gif','img/icons/cancella2_crit2.gif');
insert into ICONE_AZIONI values ('SLA_ENTI_CREA','crea ente',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SLA_INDIC_ATT_VEDI','visualizza tutti gli oggetti',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_INDIC_ATT_VEDI_FS','visualizza oggetti Fuori Soglia',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_INDIC_DISP_VEDI','visualizza tutti gli oggetti',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SLA_INDIC_DISP_VEDI_FS','visualizza oggetti Fuori Soglia',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_UT_VEDI','vedi utente',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_UT_MODIFICA','modifica utente',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_UT_SOSPENDI','sospendi utente',30,'img/icons/zzz2.gif','img/icons/zzz2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_UT_RIPRISTINA','ripristina utente',40,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_UT_CREA','crea utente',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_GR_VEDI','vedi gruppo',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_GR_MODIFICA','modifica gruppo',20,'img/icons/modifica2.gif','img/icons/modifica2_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_GR_SOSPENDI','sospendi gruppo',30,'img/icons/zzz2.gif','img/icons/zzz2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_GR_RIPRISTINA','ripristina gruppo',40,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_GR_CREA','crea gruppo',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_SES_VEDI','Ispeziona le pagine visitate da questo utente',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('REQUEST_PARSE','Parse automatico',90,'img/icons/parse2.gif','img/icons/parse2.gif');
insert into ICONE_AZIONI values ('IMPORT_CREA','Crea una procedura di import',20,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('IMPORT_MODIFICA','Modifica l''import',20,'img/icons/modifica2.gif','img/icons/modifica2.gif');
insert into ICONE_AZIONI values ('IMPORT_ESEGUI','Esegui l''import',10,'img/icons/import2.gif','img/icons/import2.gif');
insert into ICONE_AZIONI values ('IMPORT_CANCELLA','Cancella la procedura di import',30,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('CREA','Inserisci',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('SETTAGGI_CANCELLA','Cancella',10,'img/icons/cestino2.gif','img/icons/cestino2.gif');
insert into ICONE_AZIONI values ('REQUEST_ASSEGNA','Assegna request',60,'img/icons/task2.gif','img/icons/task2.gif');
insert into ICONE_AZIONI values ('REQUEST_CHIUDI_CON_MAIL','Chiudi request con mail',80,'img/icons/mail_send2.gif','img/icons/mail_send2.gif');
insert into ICONE_AZIONI values ('REQUEST_DEASSEGNA','Deassegna request',70,'img/icons/frecciaup2.gif','img/icons/frecciaup2.gif');
insert into ICONE_AZIONI values ('TASK_CHIUDI','Chiudi task',65,'img/icons/chiudi2.gif','img/icons/chiudi2.gif');
insert into ICONE_AZIONI values ('SESSIONE_CANCELLA','Azzera il contatore delle sessioni',30,'img/icons/unlock_user2.gif','img/icons/unlock_user_crit2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_VEDI','Vedi attivita',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('TASK_CREA','Crea Task',60,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_MODIFICA','Modifica attivita',20,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_SLA','Compila SLA',30,'img/icons/sla2.gif','img/icons/sla_crit2.gif');
insert into ICONE_AZIONI values ('REQUEST_VEDI','Vedi request',40,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('REQUEST_ARCHIVIA','Archivia request',50,'img/icons/folder2.gif','img/icons/folder_cri2.gif');
insert into ICONE_AZIONI values ('INPUT_CANCELLA','Cancella configurazione input',60,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('INPUT_MODIFICA','Modifica nome configurazione input',55,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_SOSPENDI','Sospendi l''attivita',58,'img/icons/zzz2.gif','img/icons/zzz_crit2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_RIPRISTINA','Ripristina l''attivita',60,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('STATO_SOSPENDI','Sospendi lo stato',50,'img/icons/zzz2.gif','img/icons/zzz_crit2.gif');
insert into ICONE_AZIONI values ('STATO_RIPRISTINA','Ripristina lo stato',60,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('TDTA_SOSPENDI','Sospendi il tipo dato tecnico attivita',40,'img/icons/zzz2.gif','img/icons/zzz_crit2.gif');
insert into ICONE_AZIONI values ('TDTA_RIPRISTINA','Ripristina il tipo dato tecnico attivita',50,'img/icons/sveglia2.gif','img/icons/sveglia_crit2.gif');
insert into ICONE_AZIONI values ('TDTA_CANCELLA','Cancella il tipo dato tecnico attivita',60,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('DTA_MODIFICA','Modifica il dato tecnico attivita',40,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('DTA_CANCELLA','Cancella il dato tecnico',50,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('SOTTOATTIVITA_VEDI','Visualizza sottoattivita',30,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('PA_VEDI','Vedi le transazioni legate a questo tipo attivita''',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('PA_MODIFICA','Modifica le transazioni legate a questo tipo attivita''',20,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('PA_CANCELLA','Cancella le transazioni legate a questo tipo attivita''',30,'img/icons/cancella2.gif','img/icons/cancella_crit2.gif');
insert into ICONE_AZIONI values ('PA_CREA','Crea un diagramma di flusso per l''attivita''',40,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('TA_CREA','Crea un nuovo tipo attivita',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('TDTA_CREA','Crea un nuovo tipo dato tecnico attivita',20,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('ATTIVITA_CREA','Crea una nuova attivita',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('INDICATORI_VEDI','Vedi indicatori',10,'img/icons/vedi2.gif','img/icons/vedi2.gif');
insert into ICONE_AZIONI values ('SISTEMA_VEDI','Vedi il sistema',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('TIPI_SISTEMA_VEDI','Vedi i tipi sistema',10,'img/icons/vedi2.gif','img/icons/vedi2.gif');
insert into ICONE_AZIONI values ('TDTA_VEDI','Vedi i tipi dati tecnici',10,'img/icons/vedi2.gif','img/icons/vedi2.gif');
insert into ICONE_AZIONI values ('STATO_MODIFICA','Modifica lo stato',10,'img/icons/modifica2.gif','img/icons/modifica_crit2.gif');
insert into ICONE_AZIONI values ('ACTION_CREA','Crea una nuova azione',10,'img/icons/nuovo2.gif','img/icons/nuovo2.gif');
insert into ICONE_AZIONI values ('ACTION_VEDI','Vedi l''azione',10,'img/icons/vedi2.gif','img/icons/vedi_crit2.gif');
insert into ICONE_AZIONI values ('ACTION_SOSPENDI','Sospendi l''azione',10,'img/icons/zzz2.gif','img/icons/zzz2.gif');

UPDATE ICONE_AZIONI set TIPO_AZIONE='ACTION_RIPRISTINA',DESCRIZIONE='Ripristina l''azione',ORDINE=10,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia2.gif' where TIPO_AZIONE= 'ACTION_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ACTION_MODIFICA',DESCRIZIONE='Modifica l''azione',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'ACTION_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SISTEMA_MODIFICA',DESCRIZIONE='Modifica il sistema',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'SISTEMA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPI_SOTTOATTIVITA_CREA',DESCRIZIONE='Crea un nuovo tipo sottoattivita',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'TIPI_SOTTOATTIVITA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPI_SOTTOATTIVITA_CANCELLA',DESCRIZIONE='Cancella il tipo sottattivita',ORDINE=10,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'TIPI_SOTTOATTIVITA_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPI_SOTTOATTIVITA_MODIFICA',DESCRIZIONE='Modifica il tipo sottoattivita',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'TIPI_SOTTOATTIVITA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_MODIFICA',DESCRIZIONE='Modifica il tipo dato tecnico attivita',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'TDTA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='STATO_CREA',DESCRIZIONE='Crea uno stato',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'STATO_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='NOTIFICA_FORZA',DESCRIZIONE='Forza la consegna della notifica',ORDINE=10,ICONA_DEFAULT='img/icons/forza2.gif',ICONA_CRIT_DEFAULT='img/icons/forza_crit2.gif' where TIPO_AZIONE= 'NOTIFICA_FORZA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='NOTIFICA_ARCHIVIA',DESCRIZIONE='Archivia la notifica',ORDINE=10,ICONA_DEFAULT='img/icons/folder2.gif',ICONA_CRIT_DEFAULT='img/icons/folder_crit2.gif' where TIPO_AZIONE= 'NOTIFICA_ARCHIVIA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='MANUTENZIONE_MODIFICA',DESCRIZIONE='Modifica degli oggetti a questa attivita manutentiva',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'MANUTENZIONE_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='MANUTENZIONE_CANCELLA',DESCRIZIONE='Cancella degli oggetti dall''attivita manutentiva',ORDINE=10,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'MANUTENZIONE_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='EVENTO_RICONOSCIMENTO',DESCRIZIONE='Riconosci l''evento',ORDINE=10,ICONA_DEFAULT='img/icons/prendi2.gif',ICONA_CRIT_DEFAULT='img/icons/prendi_crit2.gif' where TIPO_AZIONE= 'EVENTO_RICONOSCIMENTO';
UPDATE ICONE_AZIONI set TIPO_AZIONE='EVENTO_ARCHIVIA',DESCRIZIONE='Archivia l''evento',ORDINE=10,ICONA_DEFAULT='img/icons/folder2.gif',ICONA_CRIT_DEFAULT='img/icons/folder_crit2.gif' where TIPO_AZIONE= 'EVENTO_ARCHIVIA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='CATEGORIA_MODIFICA',DESCRIZIONE='Modifica la categoria',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'CATEGORIA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='CATEGORIA_CREA',DESCRIZIONE='Crea una categoria',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'CATEGORIA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='CATEGORIA_CANCELLA',DESCRIZIONE='Cancella una categoria',ORDINE=10,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'CATEGORIA_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SERVICE_ITEM_VEDI',DESCRIZIONE='Vedi il Service Item',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SERVICE_ITEM_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SERVICE_ITEM_RIPRISTINA',DESCRIZIONE='Ripristina il Service Item',ORDINE=10,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'SERVICE_ITEM_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SERVICE_ITEM_SOSPENDI',DESCRIZIONE='Sospendi il Service Item',ORDINE=10,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'SERVICE_ITEM_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SERVICE_ITEM_MODIFICA',DESCRIZIONE='Modifica il Service Item',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'SERVICE_ITEM_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DATI_TECNICI_VEDI',DESCRIZIONE='Vedi i dati tecnici',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'DATI_TECNICI_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DATI_TECNICI_MODIFICA',DESCRIZIONE='Modifica i dati tecnici',ORDINE=10,ICONA_DEFAULT='img/icons/modifica_bau2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'DATI_TECNICI_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DATI_TECNICI_RIPRISTINA',DESCRIZIONE='Ripristina il dato tecnico',ORDINE=10,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'DATI_TECNICI_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DATI_TECNICI_SOSPENDI',DESCRIZIONE='Sospendi il dato tecnico',ORDINE=10,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'DATI_TECNICI_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPO_OGGETTO_MODIFICA',DESCRIZIONE='Modifica il tipo oggetto',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'TIPO_OGGETTO_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPO_OGGETTO_RIPRISTINA',DESCRIZIONE='Ripistina il tipo oggetto',ORDINE=10,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'TIPO_OGGETTO_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPO_OGGETTO_SOSPENDI',DESCRIZIONE='Sospendi il tipo oggetto',ORDINE=10,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'TIPO_OGGETTO_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ME_MODIFICA',DESCRIZIONE='Modifica il tipo ME',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'ME_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ME_CANCELLA',DESCRIZIONE='Cancella il tipo ME',ORDINE=10,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'ME_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='VEDI',DESCRIZIONE='Vedi',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='MODIFICA',DESCRIZIONE='Modifica',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='CANCELLA',DESCRIZIONE='Cancella',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ATT_VEDI',DESCRIZIONE='vedi indicatore',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_ATT_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ATT_MODIFICA',DESCRIZIONE='modifica indicatore',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_ATT_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ATT_CANCELLA',DESCRIZIONE='cancella indicatore',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_ATT_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ATT_CREA',DESCRIZIONE='crea indicatore',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_ATT_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_DISP_VEDI',DESCRIZIONE='vedi indicatore',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_DISP_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_DISP_MODIFICA',DESCRIZIONE='modifica indicatore',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_DISP_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_DISP_CANCELLA',DESCRIZIONE='cancella indicatore',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_DISP_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_DISP_CREA',DESCRIZIONE='crea indicatore',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_DISP_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_TI_VEDI',DESCRIZIONE='vedi tipologia di intervento',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_TI_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='QUERY_MODIFICA',DESCRIZIONE='Modifica Query',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2.gif' where TIPO_AZIONE= 'QUERY_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='VEDI_STORICO_ATTIVITA',DESCRIZIONE='Visualizzazione Storico Attivita',ORDINE=40,ICONA_DEFAULT='img/icons/piu.gif',ICONA_CRIT_DEFAULT='img/icons/piu.gif' where TIPO_AZIONE= 'VEDI_STORICO_ATTIVITA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_RELATIVE',DESCRIZIONE='Elenco Attivita',ORDINE=20,ICONA_DEFAULT='img/icons/piu.gif',ICONA_CRIT_DEFAULT='img/icons/piu.gif' where TIPO_AZIONE= 'ATTIVITA_RELATIVE';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DOCUMENTI_ELIMINA',DESCRIZIONE='Elimina il documento',ORDINE=50,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2.gif' where TIPO_AZIONE= 'DOCUMENTI_ELIMINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='VEDI_STORICO_SISTEMA',DESCRIZIONE='Visualizzazione Storico Sistema',ORDINE=40,ICONA_DEFAULT='img/icons/storico.gif',ICONA_CRIT_DEFAULT='img/icons/storico.gif' where TIPO_AZIONE= 'VEDI_STORICO_SISTEMA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_REPORT',DESCRIZIONE='Prepara Report',ORDINE=100,ICONA_DEFAULT='img/icons/printer.gif',ICONA_CRIT_DEFAULT='img/icons/printer.gif' where TIPO_AZIONE= 'ATTIVITA_REPORT';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_CREA_FIGLIO',DESCRIZIONE='Crea figlio',ORDINE=40,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'ATTIVITA_CREA_FIGLIO';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PARSER_CREA',DESCRIZIONE='Crea Parser',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'PARSER_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PARSER_EDIT',DESCRIZIONE='Modifica Parser',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'PARSER_EDIT';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PARSER_CANCELLA',DESCRIZIONE='Cancella Parser',ORDINE=10,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'PARSER_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PARSERPLUS_CREA',DESCRIZIONE='Crea Parser Plus',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'PARSERPLUS_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_TI_MODIFICA',DESCRIZIONE='modifica tipologia di intervento',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_TI_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_TI_CANCELLA',DESCRIZIONE='cancella tipologia di intervento',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_TI_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_TI_CREA',DESCRIZIONE='crea tipologia di intervento',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_TI_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_IP_VEDI',DESCRIZIONE='vedi influenza sulle performance',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_IP_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_IP_MODIFICA',DESCRIZIONE='modifica influenza sulle performance',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_IP_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_IP_CANCELLA',DESCRIZIONE='cancella influenza sulle performance',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_IP_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_IP_CREA',DESCRIZIONE='crea influenza sulle performance',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_IP_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_CLASSI_VEDI',DESCRIZIONE='vedi classe ME',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_CLASSI_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_CLASSI_MODIFICA',DESCRIZIONE='modifica classe ME',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_CLASSI_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_CLASSI_CANCELLA',DESCRIZIONE='cancella classe ME',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_CLASSI_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_CLASSI_CANCELLA_CRIT',DESCRIZIONE='verifica impedimenti',ORDINE=40,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_CLASSI_CANCELLA_CRIT';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_CLASSI_CREA',DESCRIZIONE='crea classe ME',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_CLASSI_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ENTI_VEDI',DESCRIZIONE='vedi ente',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_ENTI_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ENTI_MODIFICA',DESCRIZIONE='modifica ente',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SLA_ENTI_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ENTI_CANCELLA',DESCRIZIONE='cancella ente',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella2_crit2.gif' where TIPO_AZIONE= 'SLA_ENTI_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_ENTI_CREA',DESCRIZIONE='crea ente',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SLA_ENTI_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_INDIC_ATT_VEDI',DESCRIZIONE='visualizza tutti gli oggetti',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_INDIC_ATT_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_INDIC_ATT_VEDI_FS',DESCRIZIONE='visualizza oggetti Fuori Soglia',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_INDIC_ATT_VEDI_FS';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_INDIC_DISP_VEDI',DESCRIZIONE='visualizza tutti gli oggetti',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_INDIC_DISP_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SLA_INDIC_DISP_VEDI_FS',DESCRIZIONE='visualizza oggetti Fuori Soglia',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SLA_INDIC_DISP_VEDI_FS';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_UT_VEDI',DESCRIZIONE='vedi utente',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_UT_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_UT_MODIFICA',DESCRIZIONE='modifica utente',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_UT_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_UT_SOSPENDI',DESCRIZIONE='sospendi utente',ORDINE=30,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'SETTAGGI_UT_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_UT_RIPRISTINA',DESCRIZIONE='ripristina utente',ORDINE=40,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_UT_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_UT_CREA',DESCRIZIONE='crea utente',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SETTAGGI_UT_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_GR_VEDI',DESCRIZIONE='vedi gruppo',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_GR_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_GR_MODIFICA',DESCRIZIONE='modifica gruppo',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_GR_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_GR_SOSPENDI',DESCRIZIONE='sospendi gruppo',ORDINE=30,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'SETTAGGI_GR_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_GR_RIPRISTINA',DESCRIZIONE='ripristina gruppo',ORDINE=40,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_GR_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_GR_CREA',DESCRIZIONE='crea gruppo',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'SETTAGGI_GR_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_SES_VEDI',DESCRIZIONE='Ispeziona le pagine visitate da questo utente',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SETTAGGI_SES_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_PARSE',DESCRIZIONE='Parse automatico',ORDINE=90,ICONA_DEFAULT='img/icons/parse2.gif',ICONA_CRIT_DEFAULT='img/icons/parse2.gif' where TIPO_AZIONE= 'REQUEST_PARSE';
UPDATE ICONE_AZIONI set TIPO_AZIONE='IMPORT_CREA',DESCRIZIONE='Crea una procedura di import',ORDINE=20,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'IMPORT_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='IMPORT_MODIFICA',DESCRIZIONE='Modifica l''import',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica2.gif' where TIPO_AZIONE= 'IMPORT_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='IMPORT_ESEGUI',DESCRIZIONE='Esegui l''import',ORDINE=10,ICONA_DEFAULT='img/icons/import2.gif',ICONA_CRIT_DEFAULT='img/icons/import2.gif' where TIPO_AZIONE= 'IMPORT_ESEGUI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='IMPORT_CANCELLA',DESCRIZIONE='Cancella la procedura di import',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'IMPORT_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='CREA',DESCRIZIONE='Inserisci',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SETTAGGI_CANCELLA',DESCRIZIONE='Cancella',ORDINE=10,ICONA_DEFAULT='img/icons/cestino2.gif',ICONA_CRIT_DEFAULT='img/icons/cestino2.gif' where TIPO_AZIONE= 'SETTAGGI_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_ASSEGNA',DESCRIZIONE='Assegna request',ORDINE=60,ICONA_DEFAULT='img/icons/task2.gif',ICONA_CRIT_DEFAULT='img/icons/task2.gif' where TIPO_AZIONE= 'REQUEST_ASSEGNA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_CHIUDI_CON_MAIL',DESCRIZIONE='Chiudi request con mail',ORDINE=80,ICONA_DEFAULT='img/icons/mail_send2.gif',ICONA_CRIT_DEFAULT='img/icons/mail_send2.gif' where TIPO_AZIONE= 'REQUEST_CHIUDI_CON_MAIL';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_DEASSEGNA',DESCRIZIONE='Deassegna request',ORDINE=70,ICONA_DEFAULT='img/icons/frecciaup2.gif',ICONA_CRIT_DEFAULT='img/icons/frecciaup2.gif' where TIPO_AZIONE= 'REQUEST_DEASSEGNA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TASK_CHIUDI',DESCRIZIONE='Chiudi task',ORDINE=65,ICONA_DEFAULT='img/icons/chiudi2.gif',ICONA_CRIT_DEFAULT='img/icons/chiudi2.gif' where TIPO_AZIONE= 'TASK_CHIUDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SESSIONE_CANCELLA',DESCRIZIONE='Azzera il contatore delle sessioni',ORDINE=30,ICONA_DEFAULT='img/icons/unlock_user2.gif',ICONA_CRIT_DEFAULT='img/icons/unlock_user_crit2.gif' where TIPO_AZIONE= 'SESSIONE_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_VEDI',DESCRIZIONE='Vedi attivita',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'ATTIVITA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TASK_CREA',DESCRIZIONE='Crea Task',ORDINE=60,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'TASK_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_MODIFICA',DESCRIZIONE='Modifica attivita',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'ATTIVITA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_SLA',DESCRIZIONE='Compila SLA',ORDINE=30,ICONA_DEFAULT='img/icons/sla2.gif',ICONA_CRIT_DEFAULT='img/icons/sla_crit2.gif' where TIPO_AZIONE= 'ATTIVITA_SLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_VEDI',DESCRIZIONE='Vedi request',ORDINE=40,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'REQUEST_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='REQUEST_ARCHIVIA',DESCRIZIONE='Archivia request',ORDINE=50,ICONA_DEFAULT='img/icons/folder2.gif',ICONA_CRIT_DEFAULT='img/icons/folder_cri2.gif' where TIPO_AZIONE= 'REQUEST_ARCHIVIA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='INPUT_CANCELLA',DESCRIZIONE='Cancella configurazione input',ORDINE=60,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'INPUT_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='INPUT_MODIFICA',DESCRIZIONE='Modifica nome configurazione input',ORDINE=55,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'INPUT_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_SOSPENDI',DESCRIZIONE='Sospendi l''attivita',ORDINE=58,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz_crit2.gif' where TIPO_AZIONE= 'ATTIVITA_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_RIPRISTINA',DESCRIZIONE='Ripristina l''attivita',ORDINE=60,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'ATTIVITA_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='STATO_SOSPENDI',DESCRIZIONE='Sospendi lo stato',ORDINE=50,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz_crit2.gif' where TIPO_AZIONE= 'STATO_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='STATO_RIPRISTINA',DESCRIZIONE='Ripristina lo stato',ORDINE=60,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'STATO_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_SOSPENDI',DESCRIZIONE='Sospendi il tipo dato tecnico attivita',ORDINE=40,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz_crit2.gif' where TIPO_AZIONE= 'TDTA_SOSPENDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_RIPRISTINA',DESCRIZIONE='Ripristina il tipo dato tecnico attivita',ORDINE=50,ICONA_DEFAULT='img/icons/sveglia2.gif',ICONA_CRIT_DEFAULT='img/icons/sveglia_crit2.gif' where TIPO_AZIONE= 'TDTA_RIPRISTINA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_CANCELLA',DESCRIZIONE='Cancella il tipo dato tecnico attivita',ORDINE=60,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'TDTA_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DTA_MODIFICA',DESCRIZIONE='Modifica il dato tecnico attivita',ORDINE=40,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'DTA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='DTA_CANCELLA',DESCRIZIONE='Cancella il dato tecnico',ORDINE=50,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'DTA_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SOTTOATTIVITA_VEDI',DESCRIZIONE='Visualizza sottoattivita',ORDINE=30,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SOTTOATTIVITA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PA_VEDI',DESCRIZIONE='Vedi le transazioni legate a questo tipo attivita''',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'PA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PA_MODIFICA',DESCRIZIONE='Modifica le transazioni legate a questo tipo attivita''',ORDINE=20,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'PA_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PA_CANCELLA',DESCRIZIONE='Cancella le transazioni legate a questo tipo attivita''',ORDINE=30,ICONA_DEFAULT='img/icons/cancella2.gif',ICONA_CRIT_DEFAULT='img/icons/cancella_crit2.gif' where TIPO_AZIONE= 'PA_CANCELLA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='PA_CREA',DESCRIZIONE='Crea un diagramma di flusso per l''attivita''',ORDINE=40,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'PA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TA_CREA',DESCRIZIONE='Crea un nuovo tipo attivita',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'TA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_CREA',DESCRIZIONE='Crea un nuovo tipo dato tecnico attivita',ORDINE=20,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'TDTA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ATTIVITA_CREA',DESCRIZIONE='Crea una nuova attivita',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'ATTIVITA_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='INDICATORI_VEDI',DESCRIZIONE='Vedi indicatori',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi2.gif' where TIPO_AZIONE= 'INDICATORI_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='SISTEMA_VEDI',DESCRIZIONE='Vedi il sistema',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'SISTEMA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TIPI_SISTEMA_VEDI',DESCRIZIONE='Vedi i tipi sistema',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi2.gif' where TIPO_AZIONE= 'TIPI_SISTEMA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='TDTA_VEDI',DESCRIZIONE='Vedi i tipi dati tecnici',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi2.gif' where TIPO_AZIONE= 'TDTA_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='STATO_MODIFICA',DESCRIZIONE='Modifica lo stato',ORDINE=10,ICONA_DEFAULT='img/icons/modifica2.gif',ICONA_CRIT_DEFAULT='img/icons/modifica_crit2.gif' where TIPO_AZIONE= 'STATO_MODIFICA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ACTION_CREA',DESCRIZIONE='Crea una nuova azione',ORDINE=10,ICONA_DEFAULT='img/icons/nuovo2.gif',ICONA_CRIT_DEFAULT='img/icons/nuovo2.gif' where TIPO_AZIONE= 'ACTION_CREA';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ACTION_VEDI',DESCRIZIONE='Vedi l''azione',ORDINE=10,ICONA_DEFAULT='img/icons/vedi2.gif',ICONA_CRIT_DEFAULT='img/icons/vedi_crit2.gif' where TIPO_AZIONE= 'ACTION_VEDI';
UPDATE ICONE_AZIONI set TIPO_AZIONE='ACTION_SOSPENDI',DESCRIZIONE='Sospendi l''azione',ORDINE=10,ICONA_DEFAULT='img/icons/zzz2.gif',ICONA_CRIT_DEFAULT='img/icons/zzz2.gif' where TIPO_AZIONE= 'ACTION_SOSPENDI';

-- PERMISSION_ICONE

delete PERMISSION_ICONE;
insert into PERMISSION_icone values ('ATTIVITA_VEDI',NULL,'*');
insert into PERMISSION_icone values ('ATTIVITA_SLA','GRUPPO',(select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into PERMISSION_icone values ('SESSIONE_CANCELLA','GRUPPO',(select ID_GRUPPO from     GRUPPI g  where        g.NOME = 'ROOT'));
insert into PERMISSION_icone values ('ATTIVITA_MODIFICA',NULL,'*');

-- OPERATORI

insert into operatori values(1,'ROOT','f7b2993185f755d2212840328001bb2f',(select id_gruppo from gruppi where nome='ROOT'),'ROOT',NULL,NULL,NULL,NULL,'ROOT',NULL,NULL,-1,NULL,NULL,'helvetica',0,0,NULL,NULL,NULL);

-- QUERY_MODULI

insert into QUERY_MODULI values (7,1,'VALORI INSERITI O MODIFICATI DA TUTTI GLI IMPORT',' select  sis.descrizione,
                                ssis.descrizione,
                                tdt.descrizione,
                                dt.descrizione,
                                op.cognome_operatore||''
''||op.nome_operatore,
                                to_char(sisi.data,''YYYY/MM/DD
HH24:MI:SS''),
                                sisi.ind_ip
                from    storico_import_si sisi,
                                operatori op,
                                dati_tecnici dt,
                                tipi_dati_tecnici tdt,
                                sistemi sis,
                                sottosistemi ssis
                where   sisi.id_operatore=op.id_operatore
                and     dt.id_storico_import=sisi.id_storico
                and     dt.id_tipo_dato_tecnico=tdt.id_tipo_dato_tecnico
                and     dt.id_sistema=sis.id_sistema
                and     dt.id_sottosistema=ssis.id_sottosistema(+)
        ',7,NULL,'SISTEMA;SOTTOSISTEMA;DATO TECNICO;VALORE;OPERATORE;DATA;INDIRIZZO IP STAZIONE',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);

insert into QUERY_MODULI values (8,1,'VALORI INSERITI O MODIFICATI DALL''ULTIMO IMPORT',' select  sis.descrizione,
                                ssis.descrizione,
                                tdt.descrizione,
                                dt.descrizione,
                                op.cognome_operatore||''
''||op.nome_operatore,
                                to_char(sisi.data,''YYYY/MM/DD
HH24:MI:SS''),
                                sisi.ind_ip
                from    storico_import_si sisi,
                                operatori op,
                                dati_tecnici dt,
                                tipi_dati_tecnici tdt,
                                sistemi sis,
                                sottosistemi ssis
                where   sisi.id_operatore=op.id_operatore
                and     dt.id_storico_import=sisi.id_storico
                and     dt.id_tipo_dato_tecnico=tdt.id_tipo_dato_tecnico
                and     dt.id_sistema=sis.id_sistema
                and     dt.id_sottosistema=ssis.id_sottosistema(+)
                and             sisi.id_storico in (select
max(id_storico) from storico_import_si)
        ',7,NULL,'SISTEMA;SOTTOSISTEMA;DATO TECNICO;VALORE;OPERATORE;DATA;INDIRIZZO IP STAZIONE',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);


UPDATE QUERY_MODULI set ID_QUERY=7,ID_MODULO=1,DESCRIZIONE='VALORI INSERITI O MODIFICATI DA TUTTI GLI IMPORT',WHAT=' select  sis.descrizione,
                                ssis.descrizione,
                                tdt.descrizione,
                                dt.descrizione,
                                op.cognome_operatore||''
''||op.nome_operatore,
                                to_char(sisi.data,''YYYY/MM/DD
HH24:MI:SS''),
                                sisi.ind_ip
                from    storico_import_si sisi,
                                operatori op,
                                dati_tecnici dt,
                                tipi_dati_tecnici tdt,
                                sistemi sis,
                                sottosistemi ssis
                where   sisi.id_operatore=op.id_operatore
                and     dt.id_storico_import=sisi.id_storico
                and     dt.id_tipo_dato_tecnico=tdt.id_tipo_dato_tecnico
                and     dt.id_sistema=sis.id_sistema
                and     dt.id_sottosistema=ssis.id_sottosistema(+)
        ',NUMERO_COLONNE=7,COL_SEP=NULL,TESTATE='SISTEMA;SOTTOSISTEMA;DATO TECNICO;VALORE;OPERATORE;DATA;INDIRIZZO IP STAZIONE',FORZA_TIPO_COLONNA=NULL,WHAT1=NULL,WHAT2=NULL,WHAT3=NULL,WHAT_PERL1=NULL,WHAT_PERL2=NULL,WHAT_PERL3=NULL,REPLICA=NULL,JOB=NULL,NOTE=NULL where ID_QUERY= 7;

UPDATE QUERY_MODULI set ID_QUERY=8,ID_MODULO=1,DESCRIZIONE='VALORI INSERITI O MODIFICATI DALL''ULTIMO IMPORT',WHAT=' select  sis.descrizione,
                                ssis.descrizione,
                                tdt.descrizione,
                                dt.descrizione,
                                op.cognome_operatore||''
''||op.nome_operatore,
                                to_char(sisi.data,''YYYY/MM/DD
HH24:MI:SS''),
                                sisi.ind_ip
                from    storico_import_si sisi,
                                operatori op,
                                dati_tecnici dt,
                                tipi_dati_tecnici tdt,
                                sistemi sis,
                                sottosistemi ssis
                where   sisi.id_operatore=op.id_operatore
                and     dt.id_storico_import=sisi.id_storico
                and     dt.id_tipo_dato_tecnico=tdt.id_tipo_dato_tecnico
                and     dt.id_sistema=sis.id_sistema
                and     dt.id_sottosistema=ssis.id_sottosistema(+)
                and             sisi.id_storico in (select
max(id_storico) from storico_import_si)
        ',NUMERO_COLONNE=7,COL_SEP=NULL,TESTATE='SISTEMA;SOTTOSISTEMA;DATO TECNICO;VALORE;OPERATORE;DATA;INDIRIZZO IP STAZIONE',FORZA_TIPO_COLONNA=NULL,WHAT1=NULL,WHAT2=NULL,WHAT3=NULL,WHAT_PERL1=NULL,WHAT_PERL2=NULL,WHAT_PERL3=NULL,REPLICA=NULL,JOB=NULL,NOTE=NULL where ID_QUERY= 8;


-- PERMISSION_QUERY



-- PASSWORD

insert into PASSWORD values (1,'minlen','6',NULL);
insert into PASSWORD values (2,'upchar',NULL,NULL);
insert into PASSWORD values (3,'numchar',NULL,NULL);
insert into PASSWORD values (4,'specchar',NULL,NULL);
insert into PASSWORD values (5,'expire','0',NULL);
insert into PASSWORD values (6,'force_attivo','1',NULL);
insert into PASSWORD values (7,'specials','_-.+*$?^%&@#|',NULL);

-- CONFIG_ART

insert into CONFIG_ART values (34,'ATTIVITA.EMAIL_FROM','"A.R.T. per mano di $SM" <$SE>','Stringa di formattazione del campo FROM nelle EMAIL spedite mediante una assegnazione di attivita''. I parametri utilizzabili saranno $CA: Codice allarme, $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IA: ID attivita'', $UR: URL di ART, $AU: $TA_xyz: Tipo dato tecnico attivita'' con ID=xyz',NULL,NULL,NULL,'"A.R.T. per mano di $SM" <$SE>');
insert into CONFIG_ART values (35,'ATTIVITA.EMAIL_SUBJECT','$CAApertura ticket n. $IA - ore $OR del $DT','Stringa di formattazione del campo SUBJECT nelle EMAIL spedite mediante una assegnazione di attivita''. I parametri utilizzabili saranno: $CA: Codice allarme, $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IA: ID attivita'', $UR: URL di ART, $AU: $TA_xyz: Tipo dato tecnico attivita'' con ID=xyz',NULL,NULL,NULL,'$CAApertura ticket n. $IA - ore $OR del $DT');
insert into CONFIG_ART values (36,'ATTIVITA.EMAIL_BODY','Ti e'' stata girata l''attivita'' n. $IA; puoi vederla cliccando nel URL di seguito indicato: $UR/index.html?id_p=$IA\n\n$SA','Stringa di formattazione del campo FROM nelle EMAIL spedite mediante una assegnazione di attivita. I parametri utilizzabili saranno $CA: Codice allarme, $DT: data, $OR: ora, $ME Sistema, $SM: Smistatore, $SE: Email smistatore, $IA: ID attivita, $UR: URL di ART, $AU: $TA_xyz: Tipo dato tecnico attivita con ID=xyz',NULL,NULL,NULL,'Ti e'' stata girata l''attivita'' n. $IA; puoi vederla cliccando nel URL di seguito indicato: $UR/index.html?id_p=$IA');
insert into CONFIG_ART values (37,'ATTIVITA.NOTIFICA_PER_STEP_FROM','art@nome_cliente.it','Indirizzo EMAIL del mittente che notifichera'' lo stato di apertura o avanzamento di una attivita'' al destinatario specificato nella voce ATTIVITA.PER_STEP_DESTINATARIO',NULL,NULL,NULL,'art@nome_cliente.it');
insert into CONFIG_ART values (38,'ATTIVITA.NOTIFICA_PER_STEP_DESTINATARIO','<EMAIL> <EMAIL>','Indirizzi EMAIL (separati da spazio) degli utenti che riceveranno la notifica dello stato di avanzamento di una attivita'' nel momento in cui avvenga un passaggio di stato definito',NULL,NULL,NULL,'nome_destinatario@nome_cliente.it');
insert into CONFIG_ART values (39,'ART.TRACCIA_SESSIONI','nome_destinatario@nome_cliente.it','Se non null abilita la traccia delle sessioni',NULL,NULL,NULL,'nome_destinatario@nome_cliente.it');
insert into CONFIG_ART values (40,'SUONO.EVENTI','S','Se diverso da stringa nulla abilita i suoni',NULL,NULL,NULL,'S');
insert into CONFIG_ART values (41,'ART.TIMEOUT_SESSIONI','30','Minuti di inattivita'' dopo i quali la sessione viene terminata (il nuovo valore avra'' effetto dal prossimo login)',NULL,NULL,NULL,'30');
insert into CONFIG_ART values (42,'ART.ATTIVITA_TIMEOUT_LOCK','3','Tempo di lock (in minuti) per attivita''',NULL,NULL,NULL,'3');
insert into CONFIG_ART values (43,'ATTIVITA.NOTIFICA_PER_STEP_BODY','L''operatore $SM ha aggiunto uno step per l''attivita'' $IA portandola nello stato $ST\n\nQuesta e'' la descrizione:\n---------------\n$DA\n---------------\n','Campo BODY nelle email per notifica STEP delle attivita''; $SA: Scheda attivita''; $ST: Stato Attivita''; $DA: Descrizione Attivita''; $DT: data; $OR: ora; $SM: Operatore; $SE; Email Operatore; $IA: ID attivita''; $FE: firma email',NULL,NULL,NULL,'L''operatore $SM ha aggiunto uno step per l''attivita'' $IA portandola nello stato $ST\n\nQuesta e'' la descrizione:\n---------------\n$DA\n---------------\n');
insert into CONFIG_ART values (44,'ATTIVITA.NOTIFICA_PER_STEP_BODY_MITT_REQ','L''operatore $SM ha aggiunto uno step per l''attivita'' $IA portandola nello stato $ST\n\nQuesta e'' la descrizione:\n---------------\n$DA\n---------------\n','Campo BODY nelle email (settabili da Gest. Stati) per notifica STEP delle attivita'' al mittente della Request; $SA: Scheda attivita''; $ST: Stato Attivita''; $DA: Descrizione Attivita''; $DT: data; $OR: ora; $SM: Operatore; $SE; Email Operatore; $IA: ID attivita''; $FE: firma email',NULL,NULL,NULL,'L''operatore $SM ha aggiunto uno step per l''attivita'' $IA portandola nello stato $ST\n\nQuesta e'' la descrizione:\n---------------\n$DA\n---------------\n');
insert into CONFIG_ART values (45,'REQUEST.DOMINIO',NULL,'Dominio degli account request (senza @)',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (46,'REQUEST.DEST_RICEZIONE',NULL,'sintassi:<br>
<b><i>
acc1|<EMAIL>*acc2|<EMAIL> <EMAIL>*+ALL+|<EMAIL></i></b><br>
dove:
acc1, acc2 sono le mailbox associate alle request<br>
<EMAIL> indirizzo email che riceve le notifiche
delle request inviate all''account acc1<br>
<EMAIL> <EMAIL> indirizzi email che ricevono
le notifiche inviate ad acc2+<br>
<EMAIL> riceve le notifiche per tutti gli account
impostati.<br>
se come account si imposta: +ALL+ i destinatari specificati riceveranno
le mail di notifica per le request inviate a QUALSIASI account.',NULL,NULL,NULL,NULL);

insert into CONFIG_ART values (47,'REQUEST.SUONO','S','Se diverso da stringa nulla abilita la notifica dell''arrivo di una nuova Request tramite un suono',NULL,NULL,NULL,'S');
insert into CONFIG_ART values (48,'NOTIFICHE.ERRORE',NULL,'Indirizzi E-Mail (Separati da spazio) di chi ricevera'' la notifica di errore',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (49,'ART.APPLICATION_NAME','wpsoworks','Nome applicazione',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (50,'REQUEST.DEST_RICEZIONE_SMS','NUMERI TEL','Numeri di telefono per notifiche SMS (nota: separare i numeri con uno spazio)',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (51,'DOCUMENTI.SISTEMA',NULL,'Tipo Sistema per la gestione della Documentazione',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (52,'ATTIVITA.SMS_BODY','Ti e'' stata assegnata l''attivita'' n. $IA; puoi vederla al seguente URL: $UR/index.html?id_p=$IA\n\n','Campo BODY SMS per notifica STEP delle attivitÃ $IA ID attivitÃ $UR URL; $ME Sistema',NULL,NULL,NULL,'Ti e'' stata assegnata l''attivita'' n. $IA; puoi vederla al seguente URL: $UR/index.html?id_p=$IA\n\n');
insert into CONFIG_ART values (53,'NOTIFICHE.SMS.MITT','ART','Mittente del messaggio SMS',NULL,NULL,NULL,'ART');
insert into CONFIG_ART values (54,'ART.RADIUS_SERVER',NULL,'Indirizzo IP o Dominio risolvibile del Server Radius. Per disabilitare l''autentificazione tramite RADIUS mantenere questo campo vuoto.',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (55,'ART.RADIUS_SECRET',NULL,'Chiave del server Radius nel formato: hostname,chiave (senza spazi). Inserire una coppia hostname,chiave per ogni application server.',NULL,NULL,'S',NULL);
insert into CONFIG_ART values (66,'NOTIFICHE.SMS.PROXY',NULL,'Impostazioni del proxy server (HTTP) per invio degli SMS (es. http://*********:8080/). Lasciare questo campo vuoto se non si desidera utilizzare un proxy. (SMS notifiche apertura) ',NULL,NULL,NULL,NULL);
insert into CONFIG_ART values (67,'NOTIFICHE.SMS.TIMEOUT','20','Timeout in SECONDI per la connessione al server invio SMS notifiche apertura.',NULL,NULL,NULL,'30');
insert into CONFIG_ART values (68,'ART.ANTIVIRUS.BIN','/usr/bin/clamscan','Eseguibile dell''antivirus CLAMAV per il controllo degli allegati.
Il controllo Antivirus si disabilita se non presente.',NULL,NULL,NULL,'/usr/bin/clamscan');

insert into CONFIG_ART values (1000,'REPORT.ESCLUDI_VISUALIZZAZIONE_ID_TA',NULL,'Esclude dalla visualizzazione dei report una lista di tipi attivita (separati da virgole)','S',NULL,NULL,NULL);
insert into CONFIG_ART values (1001,'ART.MAIL.BIN','/bin/mail','Mail client',NULL,NULL,NULL,'/bin/mail');
insert into CONFIG_ART values (1002,'ATTIVITA.RICERCA.SISTEMA','MENU','Tipologia di ricerca sistema su motore di ricerca: per istanze in cui il numero di sistemi e'' troppo alto impostare a TEXT, per l''interfaccia standard (menu a tendina) impostare a MENU.',NULL,NULL,NULL,'MENU');
insert into CONFIG_ART values (1,'NOTIFICHE.EMAIL.FROM','"Notifiche ART"','Messaggio che definira'' il mittente delle mail delle notifiche nel campo "From" del destinatario',NULL,NULL,NULL,'"Notifiche ART"');
insert into CONFIG_ART values (2,'NOTIFICHE.EMAIL.REFRESH','10','Numero di secondi di attesa per la ri-scansione della coda EMAIL (riscontrabile in "Gestione code delle notifiche")',NULL,NULL,NULL,'10');
insert into CONFIG_ART values (3,'NOTIFICHE.POPUP.DOMINIO','Dominio 1','Dominio windows utilizzato per mandare messaggi di tipo popup (non utilizzato per ora)',NULL,NULL,'S','Dominio 1');
insert into CONFIG_ART values (4,'NOTIFICHE.POPUP.DOMINIO','Dominio 2','Dominio windows utilizzato per mandare messaggi di tipo popup (non utilizzato tranne in Infostrada)',NULL,NULL,'S','Dominio 2');
insert into CONFIG_ART values (5,'NOTIFICHE.POPUP.FROM','Notifiche ART','Messaggio che definira'' il mittente dei popup di notifica (non utilizzato per ora)',NULL,NULL,NULL,'Notifiche ART');
insert into CONFIG_ART values (6,'NOTIFICHE.POPUP.REFRESH','60','Numero di secondi di attesa per la ri-scansione della coda POPUP (riscontrabile in "Gestione code delle notifiche" ma non utilizzato per ora)',NULL,NULL,NULL,'60');
insert into CONFIG_ART values (7,'NOTIFICHE.REFRESH','60','Numero di secondi di attesa per la ri-scansione della coda MASTER  (riscontrabile in "Gestione code delle notifiche")',NULL,NULL,NULL,'60');
insert into CONFIG_ART values (8,'NOTIFICHE.SMS.INSORTO','INS','Stringa da utilizzare in caso si voglia segnalare un INSORGENZA allarme quando si spedisce da ART un SMS mediante un dispositivo GSM attaccato alla macchina dove risiede ART',NULL,NULL,NULL,'INS');
insert into CONFIG_ART values (9,'NOTIFICHE.SMS.RIENTRATO','RIE','Stringa da utilizzare in caso si voglia segnalare un RIENTRO allarme quando si spedisce da ART un SMS mediante un dispositivo GSM attaccato alla macchina dove risiede ART',NULL,NULL,NULL,'RIE');
insert into CONFIG_ART values (10,'NOTIFICHE.EMAIL.INSORTO','INSORTO','Stringa da utilizzare in caso si voglia segnalare una INSORGENZA allarme quando si spedisce da ART una EMAIL',NULL,NULL,NULL,'INSORTO');
insert into CONFIG_ART values (11,'NOTIFICHE.EMAIL.RIENTRATO','RIENTRATO','Stringa da utilizzare in caso si voglia segnalare un RIENTRO allarme quando si spedisce da ART una EMAIL',NULL,NULL,NULL,'RIENTRATO');
insert into CONFIG_ART values (12,'NOTIFICHE.POPUP.INSORTO','INSORTO','Stringa da utilizzare in caso si voglia segnalare una INSORGENZA allarme quando si spedisce da ART un POPUP',NULL,NULL,NULL,'INSORTO');
insert into CONFIG_ART values (13,'NOTIFICHE.POPUP.RIENTRATO','RIENTRATO','Stringa da utilizzare in caso si voglia segnalare un RIENTRO allarme quando si spedisce da ART un POPUP',NULL,NULL,NULL,'RIENTRATO');
insert into CONFIG_ART values (14,'NOTIFICHE.SMS.FORMATO_MSG','$DT: allarme $IR sul nodo $ND ($OB) subsys:$SS $DE','Stringa di formattazione del messaggio da spedire via SMS mediante un dispositivo GSM attaccato alla macchina dove risiede ART. I parametri utilizzabili saranno: $DT=data, $IR=insorto o rientrato, $ND=nodo, $OB=oggetto, $SS=sottosistema, $DE=descrizione',NULL,NULL,NULL,'$DT: allarme $IR sul nodo $ND ($OB) subsys:$SS $DE');
insert into CONFIG_ART values (15,'NOTIFICHE.EMAIL.FORMATO_MSG','$ND $SS $IR $DT $DE','Stringa di formattazione del messaggio da spedire via EMAIL. I parametri utilizzabili saranno: $DT=data, $IR=insorto o rientrato, $ND=nodo, $OB=oggetto, $SS=sottosistema, $DE=descrizione',NULL,NULL,NULL,'$ND $SS $IR $DT $DE');
insert into CONFIG_ART values (16,'NOTIFICHE.POPUP.FORMATO_MSG','$DT $IR $ND $OB $SS $DE','Stringa di formattazione del messaggio da spedire via POPUP. I parametri utilizzabili saranno: $DT=data, $IR=insorto o rientrato, $ND=nodo, $OB=oggetto, $SS=sottosistema, $DE=descrizione',NULL,NULL,NULL,'$DT $IR $ND $OB $SS $DE');
insert into CONFIG_ART values (17,'NOTIFICHE.STATO_CODA','ELIMINA','Valore che definisce lo stato in cui sta la CODA delle NOTIFICHE MASTER in quel momento. I valori possibili saranno: ATTIVO=normale attivita''; SOSPESO=coda non processata; ELIMINA=i dati inseriti vengono scartati','S',NULL,NULL,'ELIMINA');
insert into CONFIG_ART values (18,'NOTIFICHE.STATO_CODA.EMAIL','ELIMINA','Valore che definisce lo stato in cui sta la CODA delle NOTIFICHE EMAIL in quel momento. I valori possibili saranno: ATTIVO=normale attivita''; SOSPESO=coda non processata; ELIMINA=i dati inseriti vengono scartati','S',NULL,NULL,'ELIMINA');
insert into CONFIG_ART values (19,'NOTIFICHE.STATO_CODA.SMS','ELIMINA','Valore che definisce lo stato in cui sta la CODA delle NOTIFICHE SMS in quel momento. I valori possibili saranno: ATTIVO=normale attivita''; SOSPESO=coda non processata; ELIMINA=i dati inseriti vengono scartati','S',NULL,NULL,'ELIMINA');
insert into CONFIG_ART values (20,'NOTIFICHE.STATO_CODA.POPUP','ELIMINA','Valore che definisce lo stato in cui sta la CODA delle NOTIFICHE  POPUP in quel momento. I valori possibili saranno: ATTIVO=normale attivita''; SOSPESO=coda non processata; ELIMINA=i dati inseriti vengono scartati','S',NULL,NULL,'ELIMINA');
insert into CONFIG_ART values (21,'REQUEST.EMAIL','art','Definisce la casella di ricezione delle request da parte di ART (a fronte di una configurazione da parte del IT)','S',NULL,'S','art');
insert into CONFIG_ART values (22,'REQUEST.EMAIL_SUBJECT','Richiesta via email n. $IR','Campo SUBJECT mandato al richiedente; $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IR: identificativo della richiesta, $SF: firma smistatore, $DN nome destinatario','S',NULL,NULL,'Richiesta via email n. $IR');
insert into CONFIG_ART values (23,'REQUEST.EMAIL_BODY','L''operatore $SM ha girato la richiesta $IR a $DN','Campo BODY nelle email mandate al richiedente; $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IR: identificativo della richiesta, $SF: firma smistatore, $DN nome destinatario','S',NULL,NULL,'L''operatore $SM ha girato la richiesta $IR a $DN');
insert into CONFIG_ART values (24,'REQUEST.EMAIL_SUBJECT_SMISTAMENTO','Richiesta via email n. $IR','Campo SUBJECT mandato al destinatario; $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IR: identificativo della richiesta, $SF: firma smistatore, $DN nome destinatario','S',NULL,NULL,'Richiesta via email n. $IR');
insert into CONFIG_ART values (25,'REQUEST.EMAIL_BODY_SMISTAMENTO','L''operatore $SM ha girato la richiesta $IR a $DN','Campo BODY nelle email mandate al destinatario; $DT: data, $OR: ora, $SM: Smistatore, $SE: Email smistatore, $IR: identificativo della richiesta, $SF: firma smistatore, $DN nome destinatario, $TR testo request','S',NULL,NULL,'L''operatore $SM ha girato la richiesta $IR a $DN');
insert into CONFIG_ART values (26,'ART.VERSIONE','5.5.0','Versione di A.R.T. installata dal cliente','S',NULL,NULL,'5.5.0');
insert into CONFIG_ART values (27,'ART.TMPDIR','/tmp/wpsoworks/tmp','Directory temporanea per scarico momentaneo degli ALLEGATI inseriti in ART (deve essere prima configurata la directory di destinazione da parte di un sistemista)',NULL,NULL,NULL,'/fs_art/tmp');
insert into CONFIG_ART values (28,'ART.REPOSITORY','/tmp/wpsoworks','Directory di repository per scarico definitivo degli ALLEGATI inseriti in ART (deve essere prima configurata la directory di destinazione da parte di un sistemista)',NULL,NULL,NULL,'/fs_art/repository');
insert into CONFIG_ART values (29,'ART.EMAIL_REPOS','/tmp/wpsoworks/email','Directory di repository per scarico delle EMAIL spedite da ART (deve essere prima configurata la directory con al interno le sottodirectory 0,1,2,.,9 di destinazione da parte di un sistemista)',NULL,NULL,NULL,'/fs_art/email');
insert into CONFIG_ART values (30,'ART.ART_URL','https://<sito_cliente>/art','Definisce URL di A.R.T. visibile via web e che rimanda alla Home Page iniziale','S',NULL,NULL,'https://<sito_cliente>/art');
insert into CONFIG_ART values (31,'ART.RICONFIGURA',NULL,'Flag per forzare la riconfigurazione dei parametri di default iniziali','S',NULL,NULL,'1');
insert into CONFIG_ART values (32,'BACKUP.DIR_LOG','/fs_art/backup_log','Directory dove scaricare i report del backup (deve essere prima configurata la directory di destinazione da parte di un sistemista)','S',NULL,NULL,'/fs_art/backup_log');
insert into CONFIG_ART values (33,'BACKUP.DIR_ERR','/fs_art/backup_err','Directory dove scaricare gli errori del backup (deve essere prima configurata la directory di destinazione da parte di un sistemista)','S',NULL,NULL,'/fs_art/backup_err');
insert into CONFIG_ART values (1003,'ART.REDI_INDEX','wpso','Indice per il Repository Directory',NULL,NULL,NULL,'wpso');
insert into CONFIG_ART values (1004,'ART.REDI_TYPE_CONTEXT','works','Tipo contesto per il Repository Directory',NULL,NULL,NULL,'core');

-- CLASSI_SISTEMA

insert into CLASSI_SISTEMA values (1,'NON RILEVANTE',0,'NR');

-- CATEGORIE_SISTEMA

insert into CATEGORIE_SISTEMA values (1,'ATTIVO',NULL,NULL,NULL,'ATTIVO');
insert into CATEGORIE_SISTEMA values (2,'SOSPESO',NULL,'Y',NULL,'SOSPESO');
insert into CATEGORIE_SISTEMA values (3,'DISMESSO',NULL,NULL,'Y','DISMESSO');

-- TIPI_OGGETTO

insert into TIPI_OGGETTO values (7,'KART','img/oggetti/tmp-ME-7.jpg','img/icons/sistema_default.jpg',NULL);
insert into TIPI_OGGETTO values (8,'API::TEST','img/icons/sistema_default.jpg','img/icons/sistema_default.jpg',NULL);
insert into TIPI_OGGETTO values (9,'WPSOWORKS','img/icons/sistema_default.jpg','img/icons/sistema_default.jpg',NULL);

-- sistemi

/*  da fare
BEGIN
   OPEN :cur FOR porting.gen_insert ('SISTEMI');
END;
/
--	operatori_gruppi - il gruppo di root deve essere 1
*/

-- TIPI_SISTEMA_TIPI_DATI_TECNICI

delete TIPI_SISTEMA_TIPI_DATI_TECNICI;
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='kart'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='alfa'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='kart'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='beta'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='API::TEST'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='API_TDT_01'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='API::TEST'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='API_TDT_02'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='API::TEST'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='API_TDT_03'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workZoneId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='centralPoint'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='name'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='username'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='permitsAreaId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='polygon'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='WZ'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cityId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cableId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cityId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cadastralCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ring'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfpName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pop'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cableName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='toNetworkElementName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='toNetworkElementId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='potential'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='unitOfMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedFacadeLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedUndergroundLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='fromNetworkElementName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cableType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedAerialLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='estimatedDuration'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='fromNetworkElementId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='startPlannedDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='endPlannedDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='startWorkDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='endWorkDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='teamId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='teamName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneUndergroundLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneAerialLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneFacadeLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneHandmaidLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='recoveryCable'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='infrastructureCheck'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedBreakdown'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='interruptedMinitubes'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='branchesCut'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pilingCheck'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='teamNote'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='assistantNote'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='fromNetworkElementType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='toNetworkElementType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='fromNetworkElementGeoLocation'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='toNetworkElementGeoLocation'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ringId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='popId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workingGroupCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='spentTime'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='onFieldAssistant'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='poseTypeChanges'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneToStockLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneFromStockLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedToStockLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedFromStockLength'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='poseTypeChangesV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneUndergroundLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneHandmaidLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneAerialLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneFacadeLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneToStockLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CAVO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='doneFromStockLengthV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cityId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cadastralCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='country'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ring'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='centralId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='city'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfpName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='province'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfpId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pop'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='PROJECT'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='warnings'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SQUADRA'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SQUADRA'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SQUADRA'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='teamId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SQUADRA'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='teamName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SQUADRA'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='onFieldAssistant'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SUBAPPALTO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SUBAPPALTO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SUBAPPALTO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='subContractCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='SUBAPPALTO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='subContractName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cityId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cadastralCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ring'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfpName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pop'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='estimatedDuration'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='recoveryCable'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='infrastructureCheck'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedBreakdown'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='interruptedMinitubes'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='branchesCut'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pilingCheck'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ringId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='popId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workingGroupCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='spentTime'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='latitude'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='longitude'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cable144foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cable192foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cable24foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cable48foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cable96foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cableOver192foConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='foJunction'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfpMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfsMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ptaMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pteMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='splitterPermutations'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='splitter116Placing'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='splitter14Placing'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='terminations'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='continuousCablesConnection'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='fibersPlacedJunction'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='prewiredPFS'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pfsPosition'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='junctionType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='junctionSite'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ptaSite'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pteSite'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='lackOfMaterial'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='notAccessibleCockpit'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='emptyingCockpit'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='onFieldAssistant'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='plannedSplicesAndTermChanges'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementFulfilled'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NODO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementIsDone'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='permitsAreaId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cityId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cadastralCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ring'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pop'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='estimatedDuration'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ringId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='popId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workingGroupCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='CANTIERE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='spentTime'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='permitsAreaId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ring'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='unitOfMeasure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='estimatedDuration'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ringId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='subCategory'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='category'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='MACROLAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='toAssignQuantity'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cablingType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='status'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCableId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCableName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCablePotential'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCablePotentialPlanned'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCableId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCableName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCablePotential'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCablePotentialPlanned'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='terminatedFibers'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='accountingQuantity'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='totalAccountingQuantityV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='TERMINAZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='totalAccountingQuantity'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkElementId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cablingType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='status'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCableId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCableName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCablePotential'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='inputCablePotentialPlanned'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCableId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCableName'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCablePotential'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='outputCablePotentialPlanned'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='splicedFibers'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='accountingQuantity'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='totalAccountingQuantityV'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='GIUNZIONE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='totalAccountingQuantity'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='centralId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workingGroupCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='messageId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='propositionNumber'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='propositionDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='propositionContractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workOrderId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerWBE'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerWBEDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='businessProject'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='businessProjectDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkStatus'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='operation'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='operationDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestorContext'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='technicalSite'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='technicalSiteDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='goodsRecipient'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='enhancementFactorDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerWorkingArea'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerTechnicalAssistant'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerTechnicalAssistantDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerProjectType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerProjectTypeDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='buyer'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='buyerDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='supplier'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='supplierDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='SAPDivision'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='SAPDivisionDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='goodsSupplier'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='goodsSupplierDesc'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='forecastStartDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='forecastEndDate'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='servicesTotalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='supplierGoodsTotalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerGoodsTotalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='MOSTotalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='resourcesTotalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='totalAmount'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='propositionCurrency'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='canOpenWorks'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='worksOnGoing'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='worksTotal'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='worksClosedOK'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='worksClosedKO'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='publicPermitsOnGoing'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='publicPermitsTotal'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='publicPermitsClosedOK'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='publicPermitsClosedKO'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='privatePermitsOnGoing'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='privatePermitsTotal'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='privatePermitsClosedOK'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='privatePermitsClosedKO'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='canOpenPermits'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='operationalContext'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='summaryNetwork'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='isoReference'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='NETWORK'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='ameliaId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='projectId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='requestType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workingGroupCode'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='spentTime'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='networkId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='updateDatabase'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='test'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='junction'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='civil'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='cableLaying'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='survey'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='onFieldIntegrationDisabled'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='workType'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='assetId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='updateDatabaseF1'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='updateDatabaseF2'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='opticalConnectionOSU'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='opticalConnectionOLT'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='restoration'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='design'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='externalSequence'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='patchCord'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='maintenanceId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='pathSurvey'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='installationPlaceSurvey'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='installationReview'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='worksManning'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='measurements'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='quarterSummary'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='defectWithDisservice'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='defectWithoutDisservice'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='LAVORO'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='infrastructure'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='ROE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='contractId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='ROE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='customerId'));
INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values((select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='ROE'),(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='roeId'));
-- FINE GLOBALE - ESEGUO commit

commit;
spool off
quit
