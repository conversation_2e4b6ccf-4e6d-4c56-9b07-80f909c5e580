set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE VIEW V_TIM_WORKS AS 
  select a.id
      ,a.descrizione
      ,a.stato_corrente
      ,a.data_Creazione
      ,a.data_ult_varstat data_variazione
      ,a.operatore_ult_varstat ultimo_operatore
      ,a.operatore_corrente
      ,pt."ID_ATTIVITA",pt."contractId",pt."customerId",pt."projectId",pt."address",pt."endPlannedDate",pt."endWorkDate",pt."estimatedDuration",pt."slaEnd",pt."slaStart",pt."startPlannedDate",pt."maker",pt."startWorkDate",pt."subContractStartWorkDate",pt."subContractEndWorkDate",pt."accountingDone",pt."pop",pt."onFieldIntegrationDisabled",pt."popId",pt."survey",pt."ring",pt."cableLaying",pt."junction",pt."ringId",pt."civil",pt."updateDatabase",pt."test",pt."subContractCode",pt."subContractName",pt."flagFIR",pt."teamId",pt."updateDatabaseF2",pt."teamName",pt."opticalConnectionOLT",pt."opticalConnectionOSU",pt."updateDatabaseF1",pt."restoration",pt."cadastralCode",pt."design",pt."patchCord",pt."spentTime",pt."workingGroupCode",pt."onFieldAssistant",pt."__DTC_CMI",pt."__DTC_DCO",pt."accountingNote",pt."__DTC_DOF",pt."accountingUser",pt."__DTC_VAR",pt."validationNote",pt."__DTC_WKI",pt."__DTC_FIR"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 206 and dt.id_sistema = a.id_sistema and rownum < 2) "contractId"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 207 and dt.id_sistema = a.id_sistema and rownum < 2) "customerId"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 212 and dt.id_sistema = a.id_sistema and rownum < 2) "projectId"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 249 and dt.id_sistema = a.id_sistema and rownum < 2) "requestType"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 321 and dt.id_sistema = a.id_sistema and rownum < 2) "workingGroupCode"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 402 and dt.id_sistema = a.id_sistema and rownum < 2) "spentTime"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1817 and dt.id_sistema = a.id_sistema and rownum < 2) "networkId"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1901 and dt.id_sistema = a.id_sistema and rownum < 2) "Aggiornamento Banca Dati"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1891 and dt.id_sistema = a.id_sistema and rownum < 2) "Collaudo"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1802 and dt.id_sistema = a.id_sistema and rownum < 2) "Giunzione"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1754 and dt.id_sistema = a.id_sistema and rownum < 2) "Lavori Civili"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1737 and dt.id_sistema = a.id_sistema and rownum < 2) "Posa Cavo"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1883 and dt.id_sistema = a.id_sistema and rownum < 2) "Sopralluogo"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1821 and dt.id_sistema = a.id_sistema and rownum < 2) "onFieldIntegrationDisabled"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 1906 and dt.id_sistema = a.id_sistema and rownum < 2) "workType"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2111 and dt.id_sistema = a.id_sistema and rownum < 2) "assetId"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2298 and dt.id_sistema = a.id_sistema and rownum < 2) "updateDatabaseF1"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2299 and dt.id_sistema = a.id_sistema and rownum < 2) "updateDatabaseF2"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2209 and dt.id_sistema = a.id_sistema and rownum < 2) "opticalConnectionOSU"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2208 and dt.id_sistema = a.id_sistema and rownum < 2) "opticalConnectionOLT"
     --,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2258 and dt.id_sistema = a.id_sistema and rownum < 2) "restoration"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2355 and dt.id_sistema = a.id_sistema and rownum < 2) "Progettazione"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 2579 and dt.id_sistema = a.id_sistema and rownum < 2) "externalSequence"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = 3465 and dt.id_sistema = a.id_sistema and rownum < 2) "Bretellaggio"
from works_art.pt_lavoro pt
    inner join works_art.v_attivita a on a.id = pt.id_attivita
where pt."customerId" = 'TIM'
order by id desc;

grant select on v_tim_works to client_core_rpt;

show errors

quit
