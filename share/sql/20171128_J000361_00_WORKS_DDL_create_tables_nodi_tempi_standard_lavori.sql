set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop table NODI_POSA_MECCANICA;
-- drop table NODI_ATTESTAZIONE;
-- drop table NODI_TIPO_CABLAGGIO;
-- drop table NODI_CAVO_SPILLATO;
-- drop table NODI_TEMPO_TOTALE;


CREATE TABLE CONF_NODI_POSA_MECCANICA
(
    "networkElementType" VARCHAR2(4000) NOT NULL 
  , "manHours" FLOAT NOT NULL 
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CONF_NODI_POSA_MECCANICA ADD CONSTRAINT CONF_NODI_POSA_MECCANICA_UK1 UNIQUE ("networkElementType","disableDate") ENABLE;

COMMENT ON TABLE CONF_NODI_POSA_MECCANICA IS 'Tempi standard lavori stimati per posa meccanica elemento di rete';

COMMENT ON COLUMN CONF_NODI_POSA_MECCANICA."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_POSA_MECCANICA."manHours" IS 'Ore/uomo stimate per l''intervento';
COMMENT ON COLUMN CONF_NODI_POSA_MECCANICA."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_POSA_MECCANICA."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';

CREATE TABLE CONF_NODI_ATTESTAZIONE
(
    "networkElementType" VARCHAR2(4000) NOT NULL 
  , "minCablesNumbers" NUMBER(10) default 0 NOT NULL 
  , "maxCablesNumbers" NUMBER(10) default 9999999999 NOT NULL 
  , "manHours" FLOAT NOT NULL 
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CONF_NODI_ATTESTAZIONE ADD CONSTRAINT CONF_NODI_ATTESTAZIONE_UK1 UNIQUE ("networkElementType","minCablesNumbers","disableDate") ENABLE;
ALTER TABLE CONF_NODI_ATTESTAZIONE ADD CONSTRAINT CONF_NODI_ATTESTAZIONE_UK2 UNIQUE ("networkElementType","maxCablesNumbers","disableDate") ENABLE;

COMMENT ON TABLE CONF_NODI_ATTESTAZIONE IS 'Tempi standard lavori stimati per attestazione elemento di rete';

COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."minCablesNumbers" IS 'Numero minimo di cavi per la voce di configurazione';
COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."maxCablesNumbers" IS 'Numero massimo di cavi per la voce di configurazione';
COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."manHours" IS 'Ore/uomo stimate per l''intervento';
COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_ATTESTAZIONE."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';

CREATE TABLE CONF_NODI_TIPO_CABLAGGIO
(
    "networkElementType" VARCHAR2(4000) NOT NULL 
  , "cablingType" VARCHAR2(1000) NOT NULL --per permettere l'indice
  , "manHours" FLOAT NOT NULL 
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CONF_NODI_TIPO_CABLAGGIO ADD CONSTRAINT CONF_NODI_TIPO_CABLAGGIO_UK1 UNIQUE ("networkElementType","cablingType","disableDate") ENABLE;

COMMENT ON TABLE CONF_NODI_TIPO_CABLAGGIO IS 'Tempi standard lavori stimati per il cablaggio';

COMMENT ON COLUMN CONF_NODI_TIPO_CABLAGGIO."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_TIPO_CABLAGGIO."manHours" IS 'Ore/uomo stimate per l''intervento';
COMMENT ON COLUMN CONF_NODI_TIPO_CABLAGGIO."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_TIPO_CABLAGGIO."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';

CREATE TABLE CONF_NODI_CAVO_SPILLATO
(
    "networkElementType" VARCHAR2(4000) NOT NULL 
  , "manHours" FLOAT NOT NULL 
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CONF_NODI_CAVO_SPILLATO ADD CONSTRAINT CONF_NODI_CAVO_SPILLATO_UK1 UNIQUE ("networkElementType","disableDate") ENABLE;

COMMENT ON TABLE CONF_NODI_CAVO_SPILLATO IS 'Tempi standard lavori stimati per lo spillamento';

COMMENT ON COLUMN CONF_NODI_CAVO_SPILLATO."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_CAVO_SPILLATO."manHours" IS 'Ore/uomo stimate per l''intervento';
COMMENT ON COLUMN CONF_NODI_CAVO_SPILLATO."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_CAVO_SPILLATO."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';

CREATE TABLE CONF_NODI_TEMPO_TOTALE
(
    "networkElementType" VARCHAR2(4000) NOT NULL 
  , "manHours" FLOAT NOT NULL 
  , "insertDate" DATE NOT NULL
  , "disableDate" DATE
);

ALTER TABLE CONF_NODI_TEMPO_TOTALE ADD CONSTRAINT CONF_NODI_TEMPO_TOTALE_UK1 UNIQUE ("networkElementType","disableDate") ENABLE;

COMMENT ON TABLE CONF_NODI_TEMPO_TOTALE IS 'Tempi standard lavori stimati per l''intervento completo';

COMMENT ON COLUMN CONF_NODI_TEMPO_TOTALE."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_TEMPO_TOTALE."manHours" IS 'Ore/uomo stimate per l''intervento';
COMMENT ON COLUMN CONF_NODI_TEMPO_TOTALE."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_TEMPO_TOTALE."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';

grant select on CONF_NODI_POSA_MECCANICA to works_art;
grant select on CONF_NODI_ATTESTAZIONE to works_art;
grant select on CONF_NODI_TIPO_CABLAGGIO to works_art;
grant select on CONF_NODI_CAVO_SPILLATO to works_art;
grant select on CONF_NODI_TEMPO_TOTALE to works_art;

quit
