set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- drop TABLE NODI;

CREATE TABLE NODI
(
    "networkElementId" NUMBER NOT NULL
  , "networkElementName" VARCHAR2(4000) NOT NULL
  , "networkElementType" VARCHAR2(4000) NOT NULL
  , "networkElementGeoLocation" VARCHAR2(4000)
  , "cablingType" VARCHAR2(4000) NOT NULL
  , "networkElementIsDone" NUMBER(1) NOT NULL
  , "infrastructureExists" NUMBER(1) NOT NULL
  , "infrastructureIsDone" NUMBER(1)
  , "inputCableId" NUMBER
  , "inputCableName" VARCHAR2(4000)
  , "inputCablePotential" NUMBER
  , "inputCablePotentialPlanned" NUMBER
  , "outputCableId" NUMBER
  , "outputCableName" VARCHAR2(4000)
  , "outputCablePotential" NUMBER
  , "outputCablePotentialPlanned" NUMBER
  , "status" VARCHAR2(4000) NOT NULL
  , "splicedFibers" NUMBER
  , "terminatedFibers" NUMBER
  , "estimatedDuration" FLOAT not null
  , "runId" NUMBER not null
  , "historicizingRunId" NUMBER
);

ALTER TABLE NODI
ADD CONSTRAINT NODI_FK1 FOREIGN KEY
(
  "runId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

ALTER TABLE NODI
ADD CONSTRAINT NODI_FK2 FOREIGN KEY
(
  "historicizingRunId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

CREATE INDEX NODI_IDX01 ON NODI ("runId" DESC);
CREATE INDEX NODI_IDX02 ON NODI ("historicizingRunId");

ALTER TABLE NODI
ADD CONSTRAINT NODI_CHK1 CHECK 
("networkElementIsDone" in (1,0))
ENABLE;

ALTER TABLE NODI
ADD CONSTRAINT NODI_CHK2 CHECK 
("infrastructureExists" in (1,0))
ENABLE;

ALTER TABLE NODI
ADD CONSTRAINT NODI_CHK3 CHECK 
("infrastructureIsDone" in (1,0))
ENABLE;

-- ALTER TABLE NODI
-- ADD CONSTRAINT NODI_CHK4 CHECK 
-- ("cablingType" in ('Splicing','Termination'))
-- ENABLE;

-- ALTER TABLE NODI
-- ADD CONSTRAINT NODI_CHK5 CHECK 
-- ("status" in ('Planned','Workable','Done'))
-- ENABLE;

COMMENT ON TABLE NODI IS 'Elenco dei nodi';

COMMENT ON COLUMN NODI."networkElementId" IS 'Progressivo univoco del delimitatore di rete in SINFO';
COMMENT ON COLUMN NODI."networkElementName" IS 'Identificativo con progressivo numerico dell''elemento di rete da "giuntare" (PFP10, PTE44, PTA13, ecc.)';
COMMENT ON COLUMN NODI."networkElementType" IS 'Tipo di delimitatore di rete in SINFO (PFP, PFS, PTE, PTA, PD, GL, ...)';
COMMENT ON COLUMN NODI."networkElementGeoLocation" IS 'Coordinate georeferenziali dell''elemento di infrastruttura reale/virtuale che contiene il delimitatore di rete';
COMMENT ON COLUMN NODI."cablingType" IS 'Tipologia cablaggio (Splicing, Termination)';
COMMENT ON COLUMN NODI."networkElementIsDone" IS 'Indica se l''elemento di rete è stato posato/realizzato';
COMMENT ON COLUMN NODI."infrastructureExists" IS 'Indica se è presente un contenitore';
COMMENT ON COLUMN NODI."infrastructureIsDone" IS 'Indica se il contenitore è stato realizzato: valorizzato solo se ''infrastructureExists=1''';
COMMENT ON COLUMN NODI."inputCableId" IS 'Identificativo SiNFO del cavo in input';
COMMENT ON COLUMN NODI."inputCableName" IS 'Identificativo mnemonico (nome) del cavo in input';
COMMENT ON COLUMN NODI."inputCablePotential" IS 'Potenzialità (massima) del cavo in input';
COMMENT ON COLUMN NODI."inputCablePotentialPlanned" IS 'Numerosità delle fibre progettate del cavo in input. valorizzato solo se ''status'' NON vale ''Done''';
COMMENT ON COLUMN NODI."outputCableId" IS 'Identificativo SiNFO del cavo in output';
COMMENT ON COLUMN NODI."outputCableName" IS 'Identificativo mnemonico (nome) del cavo in output';
COMMENT ON COLUMN NODI."outputCablePotential" IS 'Potenzialità (massima) del cavo in output';
COMMENT ON COLUMN NODI."outputCablePotentialPlanned" IS 'Numerosità delle fibre progettate del cavo in output: valorizzato solo se ''status'' NON vale ''Done''';
COMMENT ON COLUMN NODI."status" IS 'Indica lo stato SiNFO della giunzione (Planned, Workable, Done)';
COMMENT ON COLUMN NODI."splicedFibers" IS 'Numerosità delle fibre giuntate: valorizzato solo se ''status=Done''';
COMMENT ON COLUMN NODI."terminatedFibers" IS 'Numerosità delle fibre terminate: valorizzato solo se ''status=Done''';
COMMENT ON COLUMN NODI."estimatedDuration" IS 'Durata stimata della giunzione in minuti';
COMMENT ON COLUMN NODI."runId" IS 'Id del campionamento';
COMMENT ON COLUMN NODI."historicizingRunId" IS 'Id del campionamento che ha sostituito quello indicato nella riga, se null si tratta del campionamento più recente';

quit
