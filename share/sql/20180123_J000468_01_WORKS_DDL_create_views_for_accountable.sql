set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_CAVI_LAVORI_DETTAGLIO AS 
  select
   pfp."customerId"
  ,pfp."contractId"
  ,pfp."pop"
  ,pfp."ring"
  ,pfp."projectId"
  ,pfp."pfpId"
  ,pfp."pfpName"
  ,cables."cableId"
  ,cables."cableName"
  ,cables."status"
  ,cables."fromNetworkElementName"
  ,cables."fromNetworkElementType"
  ,cables."toNetworkElementName"
  ,cables."toNetworkElementType"
  ,cables."cableType"
  ,cables."potential"
  ,cables."unitOfMeasure"
  ,cables."fromNetworkElementId"
  ,cables."fromNetworkElementGeoLocation"
  ,cables."toNetworkElementId"
  ,cables."toNetworkElementGeoLocation"
  ,cables."UI"
  ,cables."horizontalLength"
  ,cables."verticalLength"
  ,cables."fromStockLength"
  ,cables."toStockLength"
  ,cables."plannedUndergroundLength"
  ,cables."plannedAerialLength"
  ,cables."plannedFacadeLength"
  , case when cables."doneUndergroundLength" is null and cables."doneHandmaidLength" is null then null
    else nvl(cables."doneUndergroundLength",0) + nvl(cables."doneHandmaidLength",0)
    end "doneUndergroundLength"
  ,cables."doneAerialLength"
  ,cables."doneFacadeLength"
  ,cables."estimatedDuration"
  ,lav."id" "activityId"
  ,lav."status" "activityStatus"
  ,lav."maker" "activityMaker"
  ,lav."teamId" "activityTeamId"
  ,lav."startPlannedDate" "activityStartPlannedDate"
  ,lav."startWorkDate" "activityStartWorkDate"
  ,lav."endPlannedDate" "activityEndPlannedDate"
  ,lav."estimatedDuration" "activityEstimatedDuration"
  ,lav."endWorkDate" "activityEndWorkDate"
  ,lav."spentTime" "activitySpentTime"
  ,lav."subContractCode" "activitySubContractCode"
  ,lav."techninalAssistantsAssignee" "activityTechAssAssignee"
  ,lav."asBuiltStatus" "activityAsBuiltStatus"
  ,case
    when cables."status" in ('Planned') and lav."status" is null then 'notWorkable'
    when cables."status" in ('Workable') and lav."status" is null then 'workable'
    when cables."status" in ('Done') then 'sinfoUpdated' -- NB: se l'attivita non esiste sarebbe un warning...
    when cables."status" in ('Accountable') then 'accountable' -- NB: se l'attivita non esiste sarebbe un warning...
    when
         (lav."status" in ('APERTA','NUOVO','INOLTRATO_SIRTI'))
      or (lav."status" = 'IN_LAVORAZIONE_SIRTI' and (lav."teamId" is null or lav."startPlannedDate" is null))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO' and lav."subContractCode" is null) -- NB: solo per garantire la fase di transizione
    then 'notPlanned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is null)
    then 'planned'
    when
         (lav."status" = 'IN_LAVORAZIONE_SIRTI' and ((lav."teamId" is not null and lav."startPlannedDate" is not null and lav."startWorkDate" is not null)))
      or (lav."status" = 'IN_LAVORAZIONE_SUBAPPALTO') -- NB: volutamente viene ignorato il valore della lav."startPlannedDate"
    then 'workInProgress'
    when lav."status" = 'ATTESA_CARICAMENTO_AS_BUILT' then 'workEnded'
    when lav."status" = 'ATTESA_GESTIONE_AS_BUILT' then 'asBuiltNotWorked'
    when lav."status" in ('ESPLETATO','QUARANTENA') and cables."status" = 'Workable' then 'asBuiltWorked'
    when lav."status" in ('ANOMALIA','ASSURANCE_ANNULLAMENTO') then 'assurance'
    when lav."status" in ('ANNULLATO','ATTESA_ANNULLAMENTO') then 'cancelled'
    else null
  end "virtualStatus"
from
  v_cavi cables
    join v_progetti pfp on cables."customerId" = pfp."customerId" and cables."contractId" = pfp."contractId" and cables."projectId" = pfp."projectId"
    left join v_cavi_lavori lav on lav."cableId" = cables."cableId" and lav."customerId" = pfp."customerId" and lav."contractId" = pfp."contractId" and lav."projectId" =  pfp."projectId"
;


CREATE OR REPLACE VIEW V_CAVI_LAVORI_RIEPILOGO AS 
  select
   "projectId"
  ,"pfpId"
  ,"pfpName"
  ,"fromNetworkElementId"
  ,"fromNetworkElementName"
  ,"fromNetworkElementType"
  ,"fromNetworkElementGeoLocation"
  ,count(1) "total"
  ,decode(sum(case when "virtualStatus" = 'notWorkable' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'notWorkable' then 1 else 0 end)) "notWorkable"
  ,decode(sum(case when "virtualStatus" = 'workable' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workable' then 1 else 0 end)) "workable"
  ,decode(sum(case when "virtualStatus" = 'notPlanned' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'notPlanned' then 1 else 0 end)) "notPlanned"
  ,decode(sum(case when "virtualStatus" = 'planned' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'planned' then 1 else 0 end)) "planned"
  ,decode(sum(case when "virtualStatus" = 'workInProgress' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workInProgress' then 1 else 0 end)) "workInProgress"
  ,decode(sum(case when "virtualStatus" = 'workEnded' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'workEnded' then 1 else 0 end)) "workEnded"
  ,decode(sum(case when "virtualStatus" = 'asBuiltNotWorked' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'asBuiltNotWorked' then 1 else 0 end)) "asBuiltNotWorked"
  ,decode(sum(case when "virtualStatus" = 'asBuiltWorked' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'asBuiltWorked' then 1 else 0 end)) "asBuiltWorked"
  ,decode(sum(case when "virtualStatus" = 'sinfoUpdated' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'sinfoUpdated' then 1 else 0 end)) "sinfoUpdated"
  ,decode(sum(case when "virtualStatus" = 'accountable' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'accountable' then 1 else 0 end)) "accountable"
  ,decode(sum(case when "virtualStatus" = 'assurance' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'assurance' then 1 else 0 end)) "assurance"
  ,decode(sum(case when "virtualStatus" = 'cancelled' then 1 else 0 end), 0, null, sum(case when "virtualStatus" = 'cancelled' then 1 else 0 end)) "cancelled"
from
  v_cavi_lavori_dettaglio
group by
   "projectId"
  ,"pfpId"
  ,"pfpName"
  ,"fromNetworkElementId"
  ,"fromNetworkElementName"
  ,"fromNetworkElementType"
  ,"fromNetworkElementGeoLocation"
;


CREATE OR REPLACE VIEW V_MACROTASKS_SNAPSHOT AS 
  select  mtr."customerId",
    mtr."contractId",
    mtr."projectId",
    mtr."sinfoLastModified",
    mt."permitsAreaId",
    mt."type",
    mt."category",
    mt."subCategory",
    mt."unitOfMeasure",
    mt."toDoQuantity",
    mt."doneQuantity" - mt."accountableQuantity" "doneQuantity",
    mt."accountableQuantity",
    mt."estimatedDuration" "duration"
from  macrotasks mt
  inner join macrotasks_run mtr on mtr."runId" = mt."runId"
where mt."historicizingRunId" is null
;


CREATE OR REPLACE VIEW V_NODI_LAVORI_RIEPILOGO AS
  select
   n."customerId"
  ,n."contractId"
  ,n."pop"
  ,n."ring"
  ,n."projectId"
  ,n."pfpId"
  ,n."pfpName"
  ,n."networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."latitude"
  ,n."longitude"
  ,n."networkElementStatus"
  ,n."networkElementContainerStatus"
  ,n."total"
  ,n."notWorkable"
  ,n."workable"
  ,n."done"
  ,n."accountable"
  ,n."estimatedDuration"
  ,n."warnings"
  ,lav."id" "workId"
  ,lav."workStatus"
  ,lav."maker" "workMaker"
  ,lav."teamId" "workTeamId"
  ,lav."startPlannedDate" "workStartPlannedDate"
  ,lav."startWorkDate" "workStartWorkDate"
  ,lav."endPlannedDate" "workEndPlannedDate"
  ,lav."estimatedDuration" "workEstimatedDuration"
  ,lav."endWorkDate" "workEndWorkDate"
  ,lav."spentTime" "workSpentTime"
  ,lav."subContractCode" "workSubContractCode"
  ,lav."techninalAssistantsAssignee" "workTechAssAssignee"
  ,lav."asBuiltStatus"
from
  works.NODI_RIEPILOGO n
    left join v_nodi_lavori lav on lav."networkElementId" = n."networkElementId" and lav."customerId" = n."customerId" and lav."contractId" = n."contractId" and lav."projectId" = n."projectId"
where 1=1
  and n."historicizingRunId" is null
;

quit
