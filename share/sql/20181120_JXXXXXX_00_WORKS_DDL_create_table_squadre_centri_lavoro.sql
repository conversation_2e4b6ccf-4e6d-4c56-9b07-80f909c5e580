set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

/*
DROP TABLE SQUADRE_CENTRI_LAVORO;
*/

CREATE TABLE SQUADRE_CENTRI_LAVORO 
   (	"workingGroupCode" VARCHAR2(10 CHAR) NOT NULL, 
	"surname" VARCHAR2(100 CHAR) NOT NULL, 
	"name" VARCHAR2(100 CHAR) NOT NULL, 
	"teamId" NUMBER NOT NULL,
	CONSTRAINT SQUADRE_CENTRI_LAVORO_PK PRIMARY KEY ("workingGroupCode", "teamId")
   );

COMMENT ON TABLE SQUADRE_CENTRI_LAVORO IS 'Associazione CENTRI LAVORO vs. centro lavoro';
COMMENT ON COLUMN SQUADRE_CENTRI_LAVORO."workingGroupCode" IS 'Codice CENTRO LAVORO';
COMMENT ON COLUMN SQUADRE_CENTRI_LAVORO."surname" IS 'Cognome';
COMMENT ON COLUMN SQUADRE_CENTRI_LAVORO."name" IS 'Nome';
COMMENT ON COLUMN SQUADRE_CENTRI_LAVORO."teamId" IS 'Identificativo della squadra';

grant select on SQUADRE_CENTRI_LAVORO to works_art;

quit
