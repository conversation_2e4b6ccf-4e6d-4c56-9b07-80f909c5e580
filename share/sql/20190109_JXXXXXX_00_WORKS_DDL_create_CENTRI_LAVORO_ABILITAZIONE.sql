set line 1000
set autocommit off
set echo on
set SER<PERSON><PERSON><PERSON> on  size 1000000
set SERVEROUT off


whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE TABLE CENTRI_LAVORO_ABILITAZIONE
  (	"workingGroupCode" VARCHAR2(10 CHAR) NOT NULL ENABLE, 
    "onFieldIntegrationDisabled" CHAR(1 BYTE), 
	 CONSTRAINT "PK_CENTRI_LAVORO_X_AOL" PRIMARY KEY ("workingGroupCode")
USING INDEX (CREATE UNIQUE INDEX UK_CENTRI_LAVORO_ABILITAZIONE ON CENTRI_LAVORO_ABILITAZIONE ("workingGroupCode"))  ENABLE);

alter table CENTRI_LAVORO_ABILITAZIONE add constraint CENTRI_LAVORO_ABILITAZ_CK01 check("onFieldIntegrationDisabled" in (0,1)) ENABLE;

COMMENT ON COLUMN CENTRI_LAVORO_ABILITAZIONE."workingGroupCode" IS 'Codice CENTRO LAVORO';
COMMENT ON COLUMN CENTRI_LAVORO_ABILITAZIONE."onFieldIntegrationDisabled" IS 'Indica se il centro lavoro ha la gestione integrata con field Integration (OFSC)';
COMMENT ON TABLE CENTRI_LAVORO_ABILITAZIONE  IS 'Associazione CENTRI LAVORO alla field Integration';

grant select on CENTRI_LAVORO_ABILITAZIONE to works_art;

quit
