set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE FORCE VIEW V_NODI_LAVORI AS 
  select
   pt."customerId"
  ,pt."contractId"
  ,pt."projectId"
  ,dt4.valore "networkElementId"
  ,a.id "id"
  ,a.stato_corrente "status"
  ,pt."maker" "maker"
  ,pt."teamId" "teamId"
  ,pt."startPlannedDate" "startPlannedDate"
  ,pt."startWorkDate" "startWorkDate"
  ,pt."endPlannedDate"
  ,pt."estimatedDuration"
  ,pt."endWorkDate"
  ,pt."spentTime"
  ,pt."subContractCode"
  ,case
    when pt."maker" = 'Team' then (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SIRTI'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
    else (
      select vsa.login_operatore From v_Storia_attivita vsa
      where vsa.azione = 'LAVORABILE_DA_SUBAPPALTO'
      and vsa.data_esecuzione = (
        select min (vsa2.data_esecuzione)
        from v_storia_attivita vsa2
        where vsa2.id_attivita = vsa.id_attivita
        and vsa2.azione = vsa.azione
      )
      and vsa.id_Attivita = a.id
    )
   end "techninalAssistantsAssignee"
  ,case
    when a.stato_corrente = 'QUARANTENA' then 'ATTESA_ESPLETAMENTO'
    when a.stato_corrente in ('ATTESA_CARICAMENTO_AS_BUILT','ATTESA_GESTIONE_AS_BUILT') then 'FASE_AS_BUILT'
    else a.stato_corrente
  end "workStatus"
  ,case
    when a.stato_corrente = 'ATTESA_CARICAMENTO_AS_BUILT' then 'ATTESA_CARICAMENTO'
    when a.stato_corrente = 'ATTESA_GESTIONE_AS_BUILT' then 'IN_LAVORAZIONE'
    when a.stato_corrente in ('QUARANTENA','ESPLETATO') then 'LAVORATO'
    else null
  end "asBuiltStatus"
from
  v_sistemi s
    join v_attivita a on a.id_sistema = s.id
    join pt_lavoro pt on pt.id_attivita = a.id
    join v_dt_sistemi dt4 on dt4.id_sistema = s.id and dt4.nome = 'networkElementId'
where 1=1
  and a.nome_tipo_attivita = 'LAVORO'
  and a.id = (select max(a2.id) from v_attivita a2 where a2.id_sistema = s.id)
  and s.tipo = 'NODO'
  and s.data_dismissione is null
  and s.data_sospensione is null
;

CREATE OR REPLACE FORCE VIEW V_NODI_LAVORI_RIEPILOGO AS 
  select
   n."customerId"
  ,n."contractId"
  ,n."pop"
  ,n."ring"
  ,n."projectId"
  ,n."pfpId"
  ,n."pfpName"
  ,n."networkElementId"
  ,n."networkElementName"
  ,n."networkElementType"
  ,n."networkElementGeoLocation"
  ,n."latitude"
  ,n."longitude"
  ,n."networkElementStatus"
  ,n."networkElementContainerStatus"
  ,n."total"
  ,n."notWorkable"
  ,n."workable"
  ,n."done"
  ,n."estimatedDuration"
  ,n."warnings"
  ,lav."id" "workId"
  ,lav."workStatus"
  ,lav."maker" "workMaker"
  ,lav."teamId" "workTeamId"
  ,lav."startPlannedDate" "workStartPlannedDate"
  ,lav."startWorkDate" "workStartWorkDate"
  ,lav."endPlannedDate" "workEndPlannedDate"
  ,lav."estimatedDuration" "workEstimatedDuration"
  ,lav."endWorkDate" "workEndWorkDate"
  ,lav."spentTime" "workSpentTime"
  ,lav."subContractCode" "workSubContractCode"
  ,lav."techninalAssistantsAssignee" "workTechAssAssignee"
  ,lav."asBuiltStatus"
from
  works.NODI_RIEPILOGO n
    left join v_nodi_lavori lav on lav."networkElementId" = n."networkElementId" and lav."customerId" = n."customerId" and lav."contractId" = n."contractId" and lav."projectId" = n."projectId"
where 1=1
  and n."historicizingRunId" is null;

grant select on v_nodi_lavori_riepilogo to works_rpt with grant option;

quit
