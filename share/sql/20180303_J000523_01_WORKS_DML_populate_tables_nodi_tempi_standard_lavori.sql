set line 1000
set autocommit off
set echo on
set SER<PERSON><PERSON><PERSON> on  size    1000000

whenever sqlerror exit 1 rollback;
whenever o<PERSON><PERSON>r  exit 2 rollback;

delete from CONF_NODI_TMP_STD_GIUNZ;
delete from CONF_NODI_TMP_STD_MAX;

insert into CONF_NODI_TMP_STD_MAX values (
  'PFP',
  'DRITTO',
  720,
  sysdate,
  null
);

insert into CONF_NODI_TMP_STD_MAX values (
  'PFP',
  'SPILLATO',
  480,
  sysdate,
  null
);

insert into CONF_NODI_TMP_STD_MAX values (
  'PFS',
  null,
  1200,
  sysdate,
  null
);

insert into CONF_NODI_TMP_STD_MAX values (
  'PD',
  null,
  480,
  sysdate,
  null
);

insert into CONF_NODI_TMP_STD_GIUNZ values (
  'PTE',
  90,
  sysdate,
  null
);

insert into CONF_NODI_TMP_STD_GIUNZ values (
  'PTA',
  90,
  sysdate,
  null
);

update CONF_NODI_POSA_MECCANICA
set "disableDate" = sysdate
where "networkElementType" = 'PTA'
;

insert into CONF_NODI_POSA_MECCANICA values (
  'PTA',
  15,
  sysdate,
  null
);

show errors

commit;

quit
