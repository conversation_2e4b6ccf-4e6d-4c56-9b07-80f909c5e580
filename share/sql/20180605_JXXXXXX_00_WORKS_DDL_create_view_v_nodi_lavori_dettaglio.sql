set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_nodi_lavori_dettaglio as
select v.id id_Attivita
    , s.id is_sistema
    ,dts1.valore "branchesCut"
    ,dts2.valore "cableOver192foConnection"
    ,dts3.valore "cable144foConnection"
    ,dts4.valore "cable192foConnection"
    ,dts5.valore "cable24foConnection"
    ,dts6.valore "cable48foConnection"
    ,dts7.valore "cable96foConnection"
    ,dts8.valore "continuousCablesConnection"
    ,dts9.valore "emptyingCockpit"
    ,dts10.valore "fibersPlacedJunction"
    ,dts11.valore "foJunction"
    ,dts12.valore "infrastructureCheck"
    ,dts13.valore "interruptedMinitubes"
    ,dts14.valore "junctionSite"
    ,dts15.valore "junctionType"
    ,dts16.valore "lackOfMaterial"
    ,dts17.valore "notAccessibleCockpit"
    ,dts18.valore "pfpMeasure"
    ,dts19.valore "pfsMeasure"
    ,dts20.valore "pfsPosition"
    ,dts21.valore "pilingCheck"
    ,dts22.valore "plannedBreakdown"
    ,dts23.valore "prewiredPFS"
    ,dts24.valore "ptaMeasure"
    ,dts25.valore "ptaSite"
    ,dts26.valore "pteMeasure"
    ,dts27.valore "pteSite"
    ,dts28.valore "recoveryCable"
    ,dts29.valore "splitterPermutations"
    ,dts30.valore "splitter116Placing"
    ,dts31.valore "splitter14Placing"
    ,dts32.valore "terminations"
from v_attivita v
    join v_sistemi s on s.id = v.id_sistema
    left join v_dt_sistemi dts1 on dts1.id_sistema = s.id and dts1.nome = 'branchesCut'
    left join v_dt_sistemi dts2 on dts2.id_sistema = s.id and dts2.nome = 'cableOver192foConnection'
    left join v_dt_sistemi dts3 on dts3.id_sistema = s.id and dts3.nome = 'cable144foConnection'
    left join v_dt_sistemi dts4 on dts4.id_sistema = s.id and dts4.nome = 'cable192foConnection'
    left join v_dt_sistemi dts5 on dts5.id_sistema = s.id and dts5.nome = 'cable24foConnection'
    left join v_dt_sistemi dts6 on dts6.id_sistema = s.id and dts6.nome = 'cable48foConnection'
    left join v_dt_sistemi dts7 on dts7.id_sistema = s.id and dts7.nome = 'cable96foConnection'
    left join v_dt_sistemi dts8 on dts8.id_sistema = s.id and dts8.nome = 'continuousCablesConnection'
    left join v_dt_sistemi dts9 on dts9.id_sistema = s.id and dts9.nome = 'emptyingCockpit'
    left join v_dt_sistemi dts10 on dts10.id_sistema = s.id and dts10.nome = 'fibersPlacedJunction'
    left join v_dt_sistemi dts11 on dts11.id_sistema = s.id and dts11.nome = 'foJunction'
    left join v_dt_sistemi dts12 on dts12.id_sistema = s.id and dts12.nome = 'infrastructureCheck'
    left join v_dt_sistemi dts13 on dts13.id_sistema = s.id and dts13.nome = 'interruptedMinitubes'
    left join v_dt_sistemi dts14 on dts14.id_sistema = s.id and dts14.nome = 'junctionSite'
    left join v_dt_sistemi dts15 on dts15.id_sistema = s.id and dts15.nome = 'junctionType'
    left join v_dt_sistemi dts16 on dts16.id_sistema = s.id and dts16.nome = 'lackOfMaterial'
    left join v_dt_sistemi dts17 on dts17.id_sistema = s.id and dts17.nome = 'notAccessibleCockpit'
    left join v_dt_sistemi dts18 on dts18.id_sistema = s.id and dts18.nome = 'pfpMeasure'
    left join v_dt_sistemi dts19 on dts19.id_sistema = s.id and dts19.nome = 'pfsMeasure'
    left join v_dt_sistemi dts20 on dts20.id_sistema = s.id and dts20.nome = 'pfsPosition'
    left join v_dt_sistemi dts21 on dts21.id_sistema = s.id and dts21.nome = 'pilingCheck'
    left join v_dt_sistemi dts22 on dts22.id_sistema = s.id and dts22.nome = 'plannedBreakdown'
    left join v_dt_sistemi dts23 on dts23.id_sistema = s.id and dts23.nome = 'prewiredPFS'
    left join v_dt_sistemi dts24 on dts24.id_sistema = s.id and dts24.nome = 'ptaMeasure'
    left join v_dt_sistemi dts25 on dts25.id_sistema = s.id and dts25.nome = 'ptaSite'
    left join v_dt_sistemi dts26 on dts26.id_sistema = s.id and dts26.nome = 'pteMeasure'
    left join v_dt_sistemi dts27 on dts27.id_sistema = s.id and dts27.nome = 'pteSite'
    left join v_dt_sistemi dts28 on dts28.id_sistema = s.id and dts28.nome = 'recoveryCable'
    left join v_dt_sistemi dts29 on dts29.id_sistema = s.id and dts29.nome = 'splitterPermutations'
    left join v_dt_sistemi dts30 on dts30.id_sistema = s.id and dts30.nome = 'splitter116Placing'
    left join v_dt_sistemi dts31 on dts31.id_sistema = s.id and dts31.nome = 'splitter14Placing'
    left join v_dt_sistemi dts32 on dts32.id_sistema = s.id and dts32.nome = 'terminations'
where v.nome_tipo_attivita = 'LAVORO'
    and s.tipo = 'NODO';

grant select on v_nodi_lavori_dettaglio to works_rpt with grant option;

quit

