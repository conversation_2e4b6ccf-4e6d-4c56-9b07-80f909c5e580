set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

ALTER TABLE CAVI ADD ("horizontalLength" FLOAT);
ALTER TABLE CAVI ADD ("verticalLength" FLOAT);
ALTER TABLE CAVI ADD ("fromStockLength" FLOAT);
ALTER TABLE CAVI ADD ("toStockLength" FLOAT);

COMMENT ON COLUMN CAVI."horizontalLength" IS 'Lunghezza tratta orizzontale';
COMMENT ON COLUMN CAVI."verticalLength" IS 'Lunghezza tratta verticale';
COMMENT ON COLUMN CAVI."fromStockLength" IS 'Lunghezza scorta elemento di origine';
COMMENT ON COLUMN CAVI."toStockLength" IS 'Lunghezza scorta elemento di destinazione';

quit
