set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE VIEW V_TIM_WORKS AS 
  select a.id
      ,a.descrizione
      ,a.stato_corrente
      ,a.data_Creazione
      ,a.data_ult_varstat data_variazione
      ,a.operatore_ult_varstat ultimo_operatore
      ,a.operatore_corrente
      ,pt."ID_ATTIVITA",pt."contractId",pt."customerId",pt."projectId",pt."address",pt."endPlannedDate",pt."endWorkDate",pt."estimatedDuration",pt."slaEnd",pt."slaStart",pt."startPlannedDate",pt."maker",pt."startWorkDate",pt."subContractStartWorkDate",pt."subContractEndWorkDate",pt."accountingDone",pt."pop",pt."onFieldIntegrationDisabled",pt."popId",pt."survey",pt."ring",pt."cableLaying",pt."junction",pt."ringId",pt."civil",pt."updateDatabase",pt."test",pt."subContractCode",pt."subContractName",pt."flagFIR",pt."teamId",pt."updateDatabaseF2",pt."teamName",pt."opticalConnectionOLT",pt."opticalConnectionOSU",pt."updateDatabaseF1",pt."restoration",pt."cadastralCode",pt."design",pt."patchCord",pt."spentTime",pt."workingGroupCode",pt."onFieldAssistant",pt."__DTC_CMI",pt."__DTC_DCO",pt."accountingNote",pt."__DTC_DOF",pt."accountingUser",pt."__DTC_VAR",pt."validationNote",pt."__DTC_WKI",pt."__DTC_FIR",pt."ref00",pt."ref01",pt."laying",pt."testApp",pt."testOTDR",pt."generic"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'requestType') and dt.id_sistema = a.id_sistema and rownum < 2) "requestType"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'networkId') and dt.id_sistema = a.id_sistema and rownum < 2) "networkId"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'updateDatabase') and dt.id_sistema = a.id_sistema and rownum < 2) "Aggiornamento Banca Dati"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'test') and dt.id_sistema = a.id_sistema and rownum < 2) "Collaudo"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'junction') and dt.id_sistema = a.id_sistema and rownum < 2) "Giunzione"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'civil') and dt.id_sistema = a.id_sistema and rownum < 2) "Lavori Civili"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'laying') and dt.id_sistema = a.id_sistema and rownum < 2) "Posa Cavo"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'survey') and dt.id_sistema = a.id_sistema and rownum < 2) "Sopralluogo"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'workType') and dt.id_sistema = a.id_sistema and rownum < 2) "workType"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'assetId') and dt.id_sistema = a.id_sistema and rownum < 2) "assetId"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'design') and dt.id_sistema = a.id_sistema and rownum < 2) "Progettazione"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'externalSequence') and dt.id_sistema = a.id_sistema and rownum < 2) "externalSequence"
     ,(select dt.descrizione from works_art.dati_tecnici dt where dt.id_tipo_dato_tecnico = (select id_tipo_dato_Tecnico from works_art.tipi_dati_Tecnici where nome = 'patchCord') and dt.id_sistema = a.id_sistema and rownum < 2) "Bretellaggio"
from works_art.pt_lavoro pt
    inner join works_art.v_attivita a on a.id = pt.id_attivita
where pt."customerId" = 'TIM'
order by id desc;


GRANT SELECT ON V_TIM_WORKS TO CLIENT_CORE_RPT;
GRANT SELECT ON V_TIM_WORKS TO CLIENT_LEGACY;

show errors

quit
