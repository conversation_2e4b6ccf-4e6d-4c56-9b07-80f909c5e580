set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_CAVI AS 
  select
   s."customerId"
  ,s."contractId"
  ,to_char(s."projectId") "projectId"
  ,c."cableId"
  ,c."cableName"
  ,c."cableType"
  ,c."potential"
  ,c."unitOfMeasure"
  ,c."fromNetworkElementId"
  ,c."fromNetworkElementName"
  ,c."fromNetworkElementType"
  ,c."fromNetworkElementGeoLocation"
  ,c."toNetworkElementId"
  ,c."toNetworkElementName"
  ,c."toNetworkElementType"
  ,c."toNetworkElementGeoLocation"
  ,c."plannedUndergroundLength"
  ,c."plannedAerialLength"
  ,c."plannedFacadeLength"
  ,c."status"
  ,c."doneUndergroundLength"
  ,c."doneAerialLength"
  ,c."doneFacadeLength"
  ,c."doneHandmaidLength"
  ,c."estimatedDuration"
from CAVI c
  join sync_runs s on s."runId" = c."runId" and c."historicizingRunId" is null
;

quit
