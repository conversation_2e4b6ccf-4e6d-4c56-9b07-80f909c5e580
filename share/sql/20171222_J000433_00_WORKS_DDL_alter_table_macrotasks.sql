set line 1000
set autocommit off
set echo on
set SER<PERSON><PERSON><PERSON> on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

ALTER TABLE MACROTASKS DROP COLUMN "workZoneid";

ALTER TABLE MACROTASKS DROP COLUMN "buildingId";

ALTER TABLE MACROTASKS ADD ("accountedQuantity" FLOAT DEFAULT 0 NOT NULL);

COMMENT ON COLUMN MACROTASKS."accountedQuantity" IS 'Quantità contabilizzata';

quit
