set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

drop table CFG_FIELD_SERVICE;
CREATE TABLE CFG_FIELD_SERVICE 
   (	
	"customerId" VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	"contractId" VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	"workType" VARCHAR2(100 BYTE) NOT NULL ENABLE, 
	"TIPO_DATO_TECNICO_ATTIVITA" VARCHAR2(30), 
	"VALORE_TDTA" VARCHAR2(4000 BYTE), 
	"FIELD_SERVICE" VARCHAR2(30 BYTE) NOT NULL ENABLE, 
	"ENABLED" CHAR(1 BYTE) DEFAULT 'Y' NOT NULL ENABLE, 
	 CONSTRAINT "CFG_FIELD_SERVICE_CHK1" CHECK (ENABLED in ('Y', 'N')) ENABLE
   );

create unique index CFG_FIELD_SERVICE_UK01 on CFG_FIELD_SERVICE ("customerId", "contractId", "workType", "TIPO_DATO_TECNICO_ATTIVITA", "VALORE_TDTA", "FIELD_SERVICE");

GRANT SELECT ON CFG_FIELD_SERVICE TO WORKS_ART WITH GRANT OPTION;
;

show errors

commit;

quit

