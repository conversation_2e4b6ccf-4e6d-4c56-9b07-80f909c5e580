set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE TABLE CONF_NODI_TMP_STD_MAX (
  "networkElementType" VARCHAR2(4000 BYTE) NOT NULL ENABLE, 
  "networkElementSubType" VARCHAR2(255 BYTE), 
  "manMinutes" FLOAT(126) NOT NULL ENABLE, 
  "insertDate" DATE NOT NULL ENABLE, 
  "disableDate" DATE, 
  CONSTRAINT CONF_NODI_TMP_STD_MAX_UK1 UNIQUE ("networkElementType", "networkElementSubType", "disableDate")
)
;

COMMENT ON COLUMN CONF_NODI_TMP_STD_MAX."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_TMP_STD_MAX."networkElementSubType" IS 'Variante tipo di elemento di rete (eventualmente null)';
COMMENT ON COLUMN CONF_NODI_TMP_STD_MAX."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_TMP_STD_MAX."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_TMP_STD_MAX."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';
COMMENT ON TABLE CONF_NODI_TMP_STD_MAX IS 'Tempi massimi standard lavori stimati: se il calcolo supera la soglia, il tempo si imposta uguale alla soglia';


CREATE TABLE CONF_NODI_TMP_STD_GIUNZ (
  "networkElementType" VARCHAR2(4000 BYTE) NOT NULL ENABLE, 
  "manMinutes" FLOAT(126) NOT NULL ENABLE, 
  "insertDate" DATE NOT NULL ENABLE, 
  "disableDate" DATE, 
  CONSTRAINT CONF_NODI_TMP_STD_GIUNZ_UK1 UNIQUE ("networkElementType", "disableDate")
)
;

COMMENT ON COLUMN CONF_NODI_TMP_STD_GIUNZ."networkElementType" IS 'Tipo di elemento di rete';
COMMENT ON COLUMN CONF_NODI_TMP_STD_GIUNZ."manMinutes" IS 'Minuti/uomo stimati per l''intervento';
COMMENT ON COLUMN CONF_NODI_TMP_STD_GIUNZ."insertDate" IS 'Data di inserimento della voce di configurazione';
COMMENT ON COLUMN CONF_NODI_TMP_STD_GIUNZ."disableDate" IS 'Data di disabilitazione della voce di configurazione: se null la configurazione è attiva';
COMMENT ON TABLE CONF_NODI_TMP_STD_GIUNZ IS 'Tempi standard lavori stimati la giunzione';

quit
