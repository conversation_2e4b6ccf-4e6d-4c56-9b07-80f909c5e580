set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

drop table MACROTASKS;
drop table MACROTASKS_RUN;

CREATE TABLE SYNC_RUNS
(
    "runId" NUMBER not null
  , "customerId" varchar(20) not null
  , "contractId" varchar(20) not null
  , "projectId" NUMBER not null
  , "runDate" DATE not null
  , "sinfoLastModified" DATE not null
  , "syncType" varchar2(256)
  , CONSTRAINT SYNC_RUNS_PK PRIMARY KEY ( "runId" )
);

CREATE INDEX SYNC_RUNS_IDX1 ON SYNC_RUNS ("syncType", "customerId", "contractId", "projectId", "sinfoLastModified");

ALTER TABLE SYNC_RUNS
ADD CONSTRAINT SYNC_RUNS_UK1 UNIQUE 
(
  "syncType", "customerId", "contractId", "projectId", "runDate"
)
ENABLE;

COMMENT ON TABLE SYNC_RUNS IS 'Elenco dei campionamenti effettuati';

COMMENT ON COLUMN SYNC_RUNS."runId" IS 'Id univoco del campionamento';
COMMENT ON COLUMN SYNC_RUNS."customerId" IS 'Cliente a cui si riferisce il campionamento';
COMMENT ON COLUMN SYNC_RUNS."contractId" IS 'Contratto a cui si riferisce il campionamento';
COMMENT ON COLUMN SYNC_RUNS."projectId" IS 'Progetto a cui si riferisce il campionamento';
COMMENT ON COLUMN SYNC_RUNS."runDate" IS 'Data del campionamento';
COMMENT ON COLUMN SYNC_RUNS."sinfoLastModified" IS 'Data di ultima modifica dei macrotask restituita da SinfoWeb. Se uguale a quella indicata nel precedente campionamento non bisogna registrare i dati dei macrotask';
COMMENT ON COLUMN SYNC_RUNS."syncType" IS 'Tipo di campionamento (MACROTASKS, CAVI, GIUNZIONI)';

CREATE TABLE MACROTASKS
(
    "permitsAreaId" NUMBER
  , "workZoneid" NUMBER
  , "buildingId" NUMBER
  , "type" VARCHAR(20) not null
  , "category" VARCHAR(256) not null
  , "subCategory" VARCHAR(256)
  , "unitOfMeasure" VARCHAR(20) not null
  , "toDoQuantity" FLOAT not null
  , "doneQuantity" FLOAT not null
  , "duration" FLOAT not null
  , "runId" NUMBER not null
  , "historicizingRunId" NUMBER
);

ALTER TABLE MACROTASKS
ADD CONSTRAINT MACROTASKS_FK1 FOREIGN KEY
(
  "runId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

ALTER TABLE MACROTASKS
ADD CONSTRAINT MACROTASKS_FK2 FOREIGN KEY
(
  "historicizingRunId" 
)
REFERENCES SYNC_RUNS
(
  "runId" 
)
ENABLE;

COMMENT ON TABLE MACROTASKS IS 'Elenco dei macrotask';

COMMENT ON COLUMN MACROTASKS."permitsAreaId" IS 'Area permessi a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."workZoneid" IS 'WorkZone a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."buildingId" IS 'Ui a cui si riferisce la riga, se null il macrotask non è ancora stato assegnato';
COMMENT ON COLUMN MACROTASKS."type" IS 'Tipo di macrotask';
COMMENT ON COLUMN MACROTASKS."category" IS 'Categoria del macrotask';
COMMENT ON COLUMN MACROTASKS."subCategory" IS 'Sottocategoria del macrotask';
COMMENT ON COLUMN MACROTASKS."unitOfMeasure" IS 'Unità di misura';
COMMENT ON COLUMN MACROTASKS."toDoQuantity" IS 'Quantità da realizzare';
COMMENT ON COLUMN MACROTASKS."doneQuantity" IS 'Quantità realizzata';
COMMENT ON COLUMN MACROTASKS."duration" IS 'Durata stimata del macrotask';
COMMENT ON COLUMN MACROTASKS."runId" IS 'Id del campionamento';
COMMENT ON COLUMN MACROTASKS."historicizingRunId" IS 'Id del campionamento che ha sostituito quello indicato nella riga, se null si tratta del campionamento più recente';

quit
