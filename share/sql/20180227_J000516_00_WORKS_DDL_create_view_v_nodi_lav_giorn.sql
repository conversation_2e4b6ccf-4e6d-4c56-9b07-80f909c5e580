set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_NODI_LAV_GIORN AS 
select final."teamId"
  , final."executionDate"
  , final."networkElementId"
  , final."spentTime"
  , final."eventType"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='recoveryCable' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "recoveryCable"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='infrastructureCheck' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "infrastructureCheck"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='plannedBreakdown' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "plannedBreakdown"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='interruptedMinitubes' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "interruptedMinitubes"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='branchesCut' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "branchesCut"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pilingCheck' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pilingCheck"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable144foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "cable144foConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable24foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "cable24foConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable48foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "cable48foConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cable96foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "cable96foConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='cableOver192foConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "cableOver192foConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='foJunction' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "foJunction"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfpMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pfpMeasure"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfsMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pfsMeasure"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='ptaMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "ptaMeasure"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pteMeasure' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pteMeasure"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitterPermutations' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "splitterPermutations"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitter116Placing' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "splitter116Placing"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='splitter14Placing' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "splitter14Placing"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='terminations' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "terminations"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='continuousCablesConnection' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "continuousCablesConnection"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='fibersPlacedJunction' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "fibersPlacedJunction"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='prewiredPFS' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "prewiredPFS"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pfsPosition' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pfsPosition"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='junctionType' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "junctionType"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='junctionSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "junctionSite"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='ptaSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "ptaSite"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='pteSite' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "pteSite"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='lackOfMaterial' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "lackOfMaterial"
  , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='notAccessibleCockpit' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "notAccessibleCockpit"
    , case when final."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='notAccessibleCockpit' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = final.id))
    else null
    end "emptyingCockpit"
from (
  select es.id
  , es."teamId"
  , trunc(es.ultimo_Step_giorno) "executionDate"
  , es."networkElementId"
  , (
        select dtats.valore
        from v_dta dtats
        where 1=1 
        and dtats.id_attivita = es.id
        and dtats.nome = 'spentTime'
        and dtats.data_esecuzione = es.ultimo_Step_giorno
      )-nvl((
        select sum(dtats.valore)
        from v_dta dtats
          join v_dta dtats3 on dtats3.id_attivita = dtats.id_attivita and dtats3.data_esecuzione = dtats.data_esecuzione
        where 1=1 
        and dtats.id_attivita = es.id
        and dtats.nome = 'spentTime'
        and dtats3.nome = 'teamId'
        and dtats3.valore != es."teamId"
        and dtats.data_esecuzione <= (
          select max(dtats2.data_esecuzione)
          from v_dta dtats2
          where dtats2.id_attivita = dtats.id_attivita
            and dtats2.nome = dtats.nome
            and dtats2.data_esecuzione < es.ultimo_Step_giorno
        )
      ),0)"spentTime"
    , (
        select dtats.valore
        from v_dta dtats
        where 1=1 
        and dtats.id_attivita = es.id
        and dtats.nome = 'eventType'
        and dtats.data_esecuzione = es.ultimo_Step_giorno
      ) "eventType"
  From (
  with steps as (
  select distinct dta2.valore "teamId"
    , v.id
    , dta.data_esecuzione data_esecuzione
    , dts.valore "networkElementId"
  from v_attivita v
    join v_sistemi s on s.id = v.id_Sistema
    join v_dta dta on dta.id_attivita = v.id and dta.nome = 'eventType'
    join v_dta dta2 on dta2.id_attivita = dta.id_attivita and dta2.nome = 'teamId' and dta2.data_esecuzione = dta.data_Esecuzione
    join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'networkElementId'
  where 1=1
    and v.nome_tipo_Attivita = 'LAVORO'
    and s.tipo = 'NODO'
    and dta.valore in ('COMPLETE','PARTIAL_COMPLETE','NOT_DONE')
    --and v.id in (6978,6977)
  ) 
    select "teamId"
      , id
      , max(data_Esecuzione) ULTIMO_STEP_GIORNO
      , "networkElementId"
    From steps
    group by "teamId", id, "networkElementId"
  ) es
) final
;

quit
