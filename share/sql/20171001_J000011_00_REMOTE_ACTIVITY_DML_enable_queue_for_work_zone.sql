
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'WORK_ZONE',
    'Gestione Work Zone',
    1
  );

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'AREA_PERMESSI',
    'Gestione Area Permessi',
    1
  );

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_WORKS',
    'WORK_ZONE',
    'GET_INFO',
    'Gestione richiesta info Area Permessi',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_WORKS'
        and     SOURCE_CONTEXT = 'WORK_ZONE'
        and     EVENT = 'GET_INFO'
        and     ENABLED = 1
    ),
    'ENFTTH_AP',
    'AREA_PERMESSI',
    1
);

commit;

quit
