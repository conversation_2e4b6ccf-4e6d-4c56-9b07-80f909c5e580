set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

insert into operatori (ID_OPERATORE,
NOME_OPERATORE,
PASSWORD_OPERATORE,
ID_GRUPPO,
LOGIN_OPERATORE,
EMAIL,
MORTO,
SERVICE_USER,
TERMINAL,
COGNOME_OPERATORE,
WOR<PERSON>GR<PERSON>UP,
MOBILE_PHONE,
FONT,
SUONO_ABILITATO,
FIRMA_EMAIL,
FONTFACE,
NUMERO_LOGIN_CONSENTITI,
NUMERO_LOGIN_ATTUALI,
FAX,
FORZA_PW,
FIRST_LOGIN) values(
    seq_operatori.nextval,
    'Field',
    'f7b2993185f755d2212840328001bb2f',
    (Select id_gruppo from gruppi where nome = 'REMOTE_EVENT'),
    'FIELD_SERVICE_EVENT_USER',
    null,
    null,
    'S',
    null,
    'Service Event',
    null,
    null,
    '-1',
    null,
    null,
    null,
    100,
    null,
    null,
    'Y',
    null
);

insert into operatori_Gruppi values(
    (Select id_operatore from operatori where login_operatore = 'FIELD_SERVICE_EVENT_USER'),
    (Select id_Gruppo from gruppi where nome = 'REMOTE_EVENT')
);

show errors

commit;

quit
