set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_cavi_lav_giorn as
select estraz."teamId"
  , ESTRAZ.DATA_ESECUZIONE "executionDate"
  , estraz."cableId"
  , estraz."spentTime"
  , estraz."eventType"
  , case when estraz."eventType" = 'COMPLETE' then (select vcl."doneUndergroundLength" From v_cavi vcl where vcl."cableId" = estraz."cableId")
    else null
    end "sinfoDoneUndergroundLength"
  , case when estraz."eventType" = 'COMPLETE' then (select vcl."doneAerialLength" From v_cavi vcl where vcl."cableId" = estraz."cableId")
    else null
    end "sinfoDoneAerialLength"
  , case when estraz."eventType" = 'COMPLETE' then (select vcl."doneFacadeLength" From v_cavi vcl where vcl."cableId" = estraz."cableId")
    else null
    end "sinfoDoneFacadeLength"
  , case when estraz."eventType" = 'COMPLETE' then (select vcl."doneHandmaidLength" From v_cavi vcl where vcl."cableId" = estraz."cableId")
    else null
    end "sinfoDoneHandmaidLength"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneUndergroundLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "doneUndergroundLength"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneAerialLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "doneAerialLength"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneFacadeLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "doneFacadeLength"
  , case when estraz."eventType" = 'COMPLETE' then (select dtsext.valore from v_dt_sistemi dtsext where dtsext.nome ='doneHandmaidLength' and dtsext.id_sistema = (select attext.id_sistema from v_attivita attext where attext.id = estraz.id))
    else null
    end "doneHandmaidLength"  
From (
with es as(
select v.id
  , dta.valore "teamId"
  , vsa.data_esecuzione
  , dts.valore "cableId"
  , (
      select dtats.valore
      from v_storia_attivita vsats
        join v_dta dtats on dtats.id_attivita = vsats.id_attivita and dtats.data_esecuzione = vsats.data_Esecuzione and dtats.nome = 'spentTime'
      where 1=1
      and vsats.id_attivita = v.id
      and vsats.data_esecuzione = vsa.data_esecuzione
      and vsats.azione = vsa.azione
      and vsats.data_esecuzione = (
        select max(vsats2.data_esecuzione)
        from v_storia_attivita vsats2
        where 1=1
        and vsats2.azione = vsats.azione
        and vsats2.id_attivita = vsats.id_Attivita
        and vsats2.data_esecuzione between trunc(vsats.data_esecuzione) and trunc(vsats.data_esecuzione)+1
      )
    )"currentSpentTime"
    , (
      select dtats.valore
      from v_dta dtats 
      where dtats.id_attivita = v.id
      and dtats.nome = 'spentTime'
      and dtats.data_esecuzione = (
        select max(dtats2.data_esecuzione)
        from v_dta dtats2
        where 1=1
        and dtats2.id_attivita = dtats.id_Attivita
        and dtats2.nome = dtats.nome
        and dtats2.data_esecuzione < trunc(vsa.data_esecuzione)
      )
    )"previousSpentTime"
    ,(
      select dtats.valore
      from v_storia_attivita vsats
        join v_dta dtats on dtats.id_attivita = vsats.id_attivita and dtats.data_esecuzione = vsats.data_Esecuzione and dtats.nome = 'eventType'
      where 1=1
      and vsats.id_attivita = v.id
      and vsats.data_esecuzione = vsa.data_esecuzione
      and vsats.azione = vsa.azione
      and vsats.data_esecuzione = (
        select max(vsats2.data_esecuzione)
        from v_storia_attivita vsats2
        where 1=1
        and vsats2.azione = vsats.azione
        and vsats2.id_attivita = vsats.id_Attivita
        and vsats2.data_esecuzione between trunc(vsats.data_esecuzione) and trunc(vsats.data_esecuzione)+1
      )
    )"eventType"
from v_attivita v
  join v_sistemi s on s.id = v.id_Sistema
  join v_storia_attivita vsa on vsa.id_attivita = v.id and vsa.azione = 'AVANZAMENTO'
  join v_dta dta on dta.id_attivita = v.id and dta.data_esecuzione = vsa.data_esecuzione and dta.nome = 'teamId'
  join v_dta dta2 on dta2.id_attivita = v.id and dta2.data_esecuzione = vsa.data_esecuzione and dta2.nome = 'eventType'
  join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = 'cableId'
where 1=1
  and v.nome_tipo_Attivita = 'LAVORO'
  and s.tipo = 'CAVO'
  and dta2.valore in ('COMPLETE','PARTIAL_COMPLETE','NOT_DONE')
)
select id
  ,"teamId"
  ,trunc(data_esecuzione) data_esecuzione
  ,"cableId"
  , sum ((nvl(es."currentSpentTime",0)- nvl(es."previousSpentTime",0))) "spentTime"
  ,"eventType"
From es
group by id
  ,"teamId"
  ,trunc(data_esecuzione)
  ,"cableId"
  ,"eventType"
)estraz
;

grant select on v_cavi_lav_giorn to works_rpt with grant option;

quit
