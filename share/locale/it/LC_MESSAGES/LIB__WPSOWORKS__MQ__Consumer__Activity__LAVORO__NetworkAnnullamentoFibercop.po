# Language file for package WPSOWORKS::MQ::Consumer::Activity::LAVORO::NetworkAnnullamentoFibercop
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOWORKS::MQ::Consumer::Activity::LAVORO::NetworkAnnullamentoFibercop\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer/Activity/LAVORO\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkAnnullamentoFibercop.pm\n"

#: NetworkAnnullamentoFibercop.pm:71
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: NetworkAnnullamentoFibercop.pm:72
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkAnnullamentoFibercop.pm:77
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: NetworkAnnullamentoFibercop.pm:115
#, perl-brace-format
msgid "Successfully worked activity {id}: current status {status}"
msgstr "Attività con id {id} lavorata con successo: stato corrente {status}"

#: NetworkAnnullamentoFibercop.pm:119
#, perl-brace-format
msgid "Successfully worked {s} activities"
msgstr "Lavarate con successo {s} attività"
