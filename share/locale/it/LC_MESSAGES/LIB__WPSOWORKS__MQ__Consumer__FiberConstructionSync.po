# Language file for package WPSOWORKS::MQ::Consumer::FiberConstructionSync
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::MQ::Consumer::FiberConstructionSync\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<PERSON><PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: FiberConstructionSync.pm\n"

#: FiberConstructionSync.pm:79
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: FiberConstructionSync.pm:80
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: FiberConstructionSync.pm:101
#, perl-brace-format
msgid "Unknown param {param} {type}"
msgstr "Parametro sconosciuto {param} {type}"

#: FiberConstructionSync.pm:115
#, perl-brace-format
msgid "Unable to search system of type {type}: {error}"
msgstr "Impossibile cercare un sistema di tipo {type}: {error}"

#: FiberConstructionSync.pm:122
#, perl-brace-format
msgid "Found more than one system of type {type} for {key} {value}"
msgstr "Trovato più di un sistema di tipo {type} per {key} {value}"

#: FiberConstructionSync.pm:131 FiberConstructionSync.pm:148
#: FiberConstructionSync.pm:163
#, perl-brace-format
msgid "Unable to update system of type {type} with id {id}: {error}"
msgstr "Impossibile aggiornare il sistema di tipo {type} con id {id}: {error}"

#: FiberConstructionSync.pm:168
#, perl-brace-format
msgid "System of type {type} with id {id} updated"
msgstr "Sistema di tipo {type} con id {id} aggiornato"

#: FiberConstructionSync.pm:180
#, perl-brace-format
msgid "Unable to create system of type {type}: {error}"
msgstr "Impossibile creare un sistema di tipo {type}: {error}"

#: FiberConstructionSync.pm:185
#, perl-brace-format
msgid "System of type {type} created with id {id}"
msgstr "Creato sistema di tipo {type} con id {id}"

#: FiberConstructionSync.pm:192
msgid "Missing required data 'groups'"
msgstr "Paramentro obbligatorio 'groups'"

#: FiberConstructionSync.pm:199
msgid "Bad data 'groups': "
msgstr "Parametro 'groups' errato: "

#: FiberConstructionSync.pm:205
msgid "Bad data 'groups': must be an ARRAY"
msgstr "Parametro 'groups' errato: deve essere un ARRAY"

#: FiberConstructionSync.pm:214
#, perl-brace-format
msgid "Missing info group: {info}"
msgstr "Info del gruppo mancanti: {info}"

#: FiberConstructionSync.pm:235
#, perl-brace-format
msgid "Creation of group {groupName} deferred to Service Management"
msgstr "Creazione del gruppo {groupName} delegata al Service Management"

#, perl-brace-format
#~ msgid "Unable to create group {groupName}: {error}"
#~ msgstr "Impossibile creare il gruppo {groupName}: {error}"

#, perl-brace-format
#~ msgid "Group {groupName} created with id {id}"
#~ msgstr "Gruppo {groupName} creato con id {id}"
