# Language file for package WPSOWORKS::Collection::System::LAVORO
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::Collection::System::LAVORO\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/Collection/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LAVORO.pm\n"

#: LAVORO.pm:253
#, perl-brace-format
msgid ""
"No network {networkId} found for customer {customerId} and contract "
"{contractId}"
msgstr ""
"Nessuna network {networkId} trovata per cliente {customerId} e contratto "
"{contractId}"

#: LAVORO.pm:257
#, perl-brace-format
msgid ""
"Found {n} network with id {networkId} for customer {customerId} and contract "
"{contractId}"
msgstr ""
"Trovate {n} network con id {networkId} per cliente {customerId} e contratto "
"{contractId}"

#: LAVORO.pm:292
#, perl-brace-format
msgid "Found work ongoing of type {workType} for ROE {id}"
msgstr "Trovato lavoro in corso di tipo {workType} per ROE {id}"

#, perl-brace-format
#~ msgid "Unknown {param} {value}"
#~ msgstr "Parametro {param} {value} sconosciuto"
