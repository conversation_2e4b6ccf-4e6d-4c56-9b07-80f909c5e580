# Language file for package API::ART::APP::Activity::LAVORO
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: API::ART::APP::Activity::LAVORO\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:25+0200\n"
"PO-Revision-Date: 2025-08-28 10:25+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LAVORO.pm\n"

#: LAVORO.pm:102 LAVORO.pm:130 LAVORO.pm:158 LAVORO.pm:186
#, perl-brace-format
msgid "Date {date1} must be equal or greater than {date2}"
msgstr "La data {date1} deve essere maggiore o uguale di {date2}"

#: LAVORO.pm:201
#, perl-brace-format
msgid "Activities in status {status} can not be updated"
msgstr "Le attività nello stato {status} non possono essere aggiornate"

#: LAVORO.pm:212
#, perl-brace-format
msgid "Only activities with system type {type} can be updated"
msgstr ""
"Solamente le attività con sistema di tipo {type} possono essere aggiornate"

#: LAVORO.pm:236 LAVORO.pm:256
msgid "Only activity of type construction site can call macro tasks"
msgstr "Solamente le attività di tipo cantiere possono chiamare i macro lavori"

#: LAVORO.pm:338
msgid "Category"
msgstr "Categoria"

#: LAVORO.pm:344
msgid "Subcategory"
msgstr "Sottocategoria"

#: LAVORO.pm:350
msgid "To do quantity"
msgstr "Quantità da realizzare"

#: LAVORO.pm:356
msgid "To assign quantity"
msgstr "Quantità da assegnare"

#: LAVORO.pm:362
msgid "UoM"
msgstr "UdM"

#: LAVORO.pm:368
msgid "Estimated duration"
msgstr "Durata stimata"

#: LAVORO.pm:419
#, perl-brace-format
msgid "Automatic accounting not enabled for system type {type}"
msgstr "Rendicontazione automatica non abilitata per sistema di tipo {type}"

#: LAVORO.pm:430
#, perl-brace-format
msgid "Automatic accounting not enabled for activity in status {status}"
msgstr ""
"Rendicontazione automatica non abilitata per attività nello stato {status}"

#: LAVORO.pm:438
msgid "Automatic accounting already done!"
msgstr "Rendicontazione automatica già effettuata!"

#: LAVORO.pm:465
#, perl-brace-format
msgid "Param {param} with value {value} not related to activity {id}"
msgstr ""
"Il parametro {param} con valore {value} non riferisce all'attività {id}"

#: LAVORO.pm:484
#, perl-brace-format
msgid "Accounting validation not enabled for system type {type}"
msgstr ""
"Validazione della rendicontazione non abilitata per sistema di tipo {type}"

#: LAVORO.pm:496
#, perl-brace-format
msgid "Accounting validation not enabled for activity in status {status}"
msgstr ""
"Validazione della rendicontazione non abilitata per attività nello stato "
"{status}"

#: LAVORO.pm:573
#, perl-brace-format
msgid "Param {param} must be an ARRAY"
msgstr "Il parametro {param} deve essere un ARRAY"

#: LAVORO.pm:595
#, perl-brace-format
msgid "Param {param} can be an ARRAY of scalar o hashes"
msgstr "Il parametro {param} può essere un ARRAY di scalar o hashes"

#~ msgid "Mismatch on param {param}: expected {expValue} requested {reqValue}"
#~ msgstr ""
#~ "Incoerenza sul parametro {param}: attesa {expValue} richiesta {reqValue}"
