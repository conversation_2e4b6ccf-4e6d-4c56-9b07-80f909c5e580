# Language file for package WPSOWORKS::MQ::Consumer::Activity::LAVORO::GestioneTT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOWORKS::MQ::Consumer::Activity::LAVORO::GestioneTT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer/Activity/LAVORO\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: GestioneTT.pm\n"

#: GestioneTT.pm:73
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: GestioneTT.pm:74
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: GestioneTT.pm:79
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: GestioneTT.pm:86
#, perl-brace-format
msgid "Param {param} must be {values}"
msgstr "Il parametro {param} deve valere {values}"

#: GestioneTT.pm:104
#, perl-brace-format
msgid "Found {n} projects for projectId {projectId}"
msgstr "Trovati {n} progetti per il projectId {projectId}"

#: GestioneTT.pm:129
#, perl-brace-format
msgid "Activity {id} already closed"
msgstr "Attività {id} già chiusa"

#: GestioneTT.pm:136
#, perl-brace-format
msgid "WorkId {workId} not associated to project {projectId}"
msgstr "WorkId {workId} non associato al progetto {projectId}"

#: GestioneTT.pm:164
#, perl-brace-format
msgid "Successfully worked activity {id}: current status {status}"
msgstr "Attività con id {id} lavorata con successo: stato corrente {status}"
