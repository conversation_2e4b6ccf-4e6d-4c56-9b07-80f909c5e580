# Language file for package WPSOWORKS
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.liv<PERSON><EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOWORKS.pm\n"

#: WPSOWORKS.pm:35
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: WPSOWORKS.pm:36
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: WPSOWORKS.pm:194 WPSOWORKS.pm:201 WPSOWORKS.pm:209
#, perl-brace-format
msgid "Unable to parse server response (MESSAGE: {response_content})"
msgstr ""
"Impossibile effettuare il parsing della risposta del server (MESSAGE: "
"{response_content})"

#: WPSOWORKS.pm:204
msgid "Client error"
msgstr "Errore lato client"

#: WPSOWORKS.pm:212
msgid "Server error"
msgstr "Errore lato server"

#: WPSOWORKS.pm:253
msgid "You cannot use BODY and RAW_BODY jointly"
msgstr "Non puoi usare BODY e RAW_BODY insieme"

#: WPSOWORKS.pm:259
msgid "You cannot use BODY param with method GET, HEAD or DELETE"
msgstr "Non puoi usare il parametro BODY con i metodi GET, HEAD o DELETE"

#: WPSOWORKS.pm:331
#, perl-brace-format
msgid ""
"Unable to parse server response (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"
msgstr ""
"Impossibile effettuare il parsing della risposta del server (HTTP_STATUS: "
"{response_code}, MESSAGE: {response_content})"

#: WPSOWORKS.pm:339
#, perl-brace-format
msgid ""
"Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato client (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: WPSOWORKS.pm:344
#, perl-brace-format
msgid ""
"Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato server (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"
