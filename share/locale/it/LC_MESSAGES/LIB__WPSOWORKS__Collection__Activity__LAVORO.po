# Language file for package WPSOWORKS::Collection::Activity::LAVORO
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::Collection::Activity::LAVORO\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LAVORO.pm\n"

#: LAVORO.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LAVORO.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LAVORO.pm:95
msgid "User is not enabled to open the activity"
msgstr "L'utente non è abilitato all'apertura dell'attività"

#: LAVORO.pm:127
#, perl-brace-format
msgid "Param {param} can not be empty"
msgstr "Il parametro {param} non può essere vuoto"

#: LAVORO.pm:132
#, perl-brace-format
msgid "Accounting operation not available for {workType}"
msgstr "Operazione contabile non disponibile per {workType}"

#: LAVORO.pm:169
msgid "It is not possible to assign work to a generic working group code"
msgstr "Non è possibile assegnare il lavoro a un generico centro di lavoro"

#: LAVORO.pm:175
msgid "It is not possible to assign an accounting operation without team"
msgstr "Non è possibile assegnare un lavoro contabile senza squadra"

#: LAVORO.pm:179
msgid ""
"It is not possible to assign an accounting operation without subcontract"
msgstr "Non è possibile assegnare un lavoro contabile senza subappalto"

#: LAVORO.pm:225 LAVORO.pm:261 LAVORO.pm:467
#, perl-brace-format
msgid "Missing mandatory param {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: LAVORO.pm:231
#, perl-brace-format
msgid "For {param} {value} {param1} can have only one value"
msgstr "Per {param} {value} {param1} può avere solo un valore"

#: LAVORO.pm:250
msgid "assetId must be an integer"
msgstr "assetId deve essere un intero"

#: LAVORO.pm:282
#, perl-brace-format
msgid "At least one param between {param} and {param1} must be defined"
msgstr "Almeno un parametro tra {param} e {param1} deve essere definito"

#: LAVORO.pm:286
#, perl-brace-format
msgid "Only one param between {param} and {param1} must be defined"
msgstr "Solamente un parametro tra {param} e {param1} deve essere definito"

#: LAVORO.pm:315
#, perl-brace-format
msgid "Params {param} and {param1} must have the same size"
msgstr "I parametri {param} e {param1} devono avere le stesse dimensioni"

#: LAVORO.pm:460
#, perl-brace-format
msgid "Activity {activityId} created for {type} {typeId}"
msgstr "Attività {activityId} creata per {type} {typeId}"

#: LAVORO.pm:475 LAVORO.pm:533 LAVORO.pm:542
#, perl-brace-format
msgid "Date {date1} must be equal or greater than {date2}"
msgstr "La data {date1} deve essere maggiore o uguale alla data {date2}"

#: LAVORO.pm:518
#, perl-brace-format
msgid "Activity {activityId} created for {id}"
msgstr "Attività {activityId} creata per {id}"

#: LAVORO.pm:638
msgid ""
"Fields team.teamId and team.TeamName must be both defined or both not defined"
msgstr ""
"I campi team.teamId e team.teamName devono essere entrambi definiti o "
"entrambi non definiti"

#: LAVORO.pm:695
#, perl-brace-format
msgid "Activity {activityId} created"
msgstr "Attività {activityId} creata"

#: LAVORO.pm:696
#, perl-brace-format
msgid "Activity {activityId} {param} {value}"
msgstr "Attività {activityId} {param} {value}"

#, perl-brace-format
#~ msgid "Date {date} can not be in the past"
#~ msgstr "La data {date} non può essere nel passato"

#~ msgid "User can not open activity"
#~ msgstr "L'utente non può aprire l'attività"
