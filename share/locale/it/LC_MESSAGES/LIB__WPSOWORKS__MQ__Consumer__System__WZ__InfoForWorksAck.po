# Language file for package WPSOWORKS::MQ::Consumer::System::WZ::InfoForWorksAck
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOWORKS::MQ::Consumer::System::WZ::InfoForWorksAck\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer/System/WZ\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: InfoForWorksAck.pm\n"

#: InfoForWorksAck.pm:73
msgid "Working RA_ID ack..."
msgstr "Lavorando RA_ID ack..."

#: InfoForWorksAck.pm:74
#, perl-brace-format
msgid "{type}: {value}"
msgstr "{type}: {value}"

#: InfoForWorksAck.pm:74
msgid "GLOBAL"
msgstr "GLOBAL"

#: InfoForWorksAck.pm:74
msgid "TARGET ACK"
msgstr "TARGET ACK"

#: InfoForWorksAck.pm:78
msgid "Unnecessary"
msgstr "Non necessario"

#: InfoForWorksAck.pm:83
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: InfoForWorksAck.pm:88
#, perl-brace-format
msgid "Unable to retrieve system: {error}"
msgstr "Impossibile recuperare il sistema: {error}"

#: InfoForWorksAck.pm:94 InfoForWorksAck.pm:106 InfoForWorksAck.pm:111
#: InfoForWorksAck.pm:118 InfoForWorksAck.pm:134
#, perl-brace-format
msgid "ACK KO: {error}"
msgstr "ACK KO: {error}"

#: InfoForWorksAck.pm:106
msgid "cityId not found"
msgstr "cityId non trovato"

#: InfoForWorksAck.pm:111
msgid "Found more than one cityId"
msgstr "Trovato più di un cityId"

#: InfoForWorksAck.pm:118
msgid "Unable to set property cityId"
msgstr "Impossiibile impostare la proprietà cityId"

#: InfoForWorksAck.pm:134
#, perl-brace-format
msgid "Unable to create city group: {error}"
msgstr "Impossible creare il gruppo city: {error}"

#: InfoForWorksAck.pm:141
#, perl-brace-format
msgid "WorkZone {workZoneId} with id {id} updated!"
msgstr "WorkZone {workZoneId} con id {id} aggiornata!"

#: InfoForWorksAck.pm:146
#, perl-brace-format
msgid "ACK KO: Unable to add group {group} to system {id}: {error}"
msgstr ""
"ACK KO: Impossibile aggiungere il gruppo {group} al sistema {id}: {error}"
