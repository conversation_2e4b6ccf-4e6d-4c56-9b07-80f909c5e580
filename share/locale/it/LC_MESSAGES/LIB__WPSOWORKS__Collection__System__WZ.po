# Language file for package WPSOWORKS::Collection::System::WZ
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::Collection::System::WZ\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/Collection/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WZ.pm\n"

#: WZ.pm:124 WZ.pm:132 WZ.pm:140
#, perl-brace-format
msgid "Invalid param {name}: {error}"
msgstr "Parametro {name} non valido: {error}"

#: WZ.pm:124
msgid "polygon"
msgstr "poligono"

#: WZ.pm:132
msgid "requestDate"
msgstr "requestDate"

#: WZ.pm:140
msgid "username"
msgstr "username"

#: WZ.pm:140
#, perl-brace-format
msgid "{username} not found"
msgstr "{username} non trovato"

#: WZ.pm:161
#, perl-brace-format
msgid ""
"workZone {workZoneId} for project {project}, customer_id {customer_id}, "
"contract_id {contract_id} and permitsAreaId {permitsAreaId} already present!"
msgstr ""
"workZone {workZoneId} per progetto {project}, cliente {customer_id}, "
"contratto {contract_id} e area permessi {permitsAreaId} già presente!"

#: WZ.pm:232
#, perl-brace-format
msgid "Invalid raData: {error}"
msgstr "raData non valido: {error}"

#: WZ.pm:249
#, perl-brace-format
msgid "Unable to send request to AP: {error}"
msgstr "Impossibile inviare la richiesta a AP: {error}"
