# Language file for package WPSOWORKS::Sync
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::Sync\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: Sync.pm\n"

#: Sync.pm:23 Sync.pm:26 Sync.pm:29 Sync.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: Sync.pm:24 Sync.pm:27 Sync.pm:30 Sync.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: Sync.pm:193
#, perl-brace-format
msgid "Unable to insert sample run: {message}"
msgstr "Impossibile inserire il campionamento: {message}"

#: Sync.pm:270
#, perl-brace-format
msgid "Unable to historicize {what}: {message}"
msgstr "Impossibile storicizzare {what}: {message}"

#: Sync.pm:279
#, perl-brace-format
msgid "Unable to insert data: {message}"
msgstr "Impossibile inserire i dati: {message}"

#: Sync.pm:289
#, perl-brace-format
msgid "Unable to check previous run: {message}"
msgstr "Impossibile verificare il precedente campionamento: {message}"

#: Sync.pm:296
msgid "Data are the same than previous run: nothing to insert"
msgstr ""
"I dati sono gli stessi del precedente campionamento: non inserisco nulla"

#: Sync.pm:337
msgid "Fetching Sinfo last modified date for the project"
msgstr "Recupero la data di ultima modifica da Sinfo per il progetto"

#: Sync.pm:356
#, perl-brace-format
msgid "{date} is not a valid rfc7232 date"
msgstr "{date} non è una data rfc7232 valida"

#: Sync.pm:362
msgid "Inserting sample entry"
msgstr "Inserisco un campionamento"

#: Sync.pm:369
msgid ""
"Verifing if Sinfo last modified date is greater than our last sample enty"
msgstr ""
"Verifico se la data di ultima modifica su Sinfo è maggiore della nostra data "
"di ultima modifica"

#: Sync.pm:372
msgid ""
"Sinfo last modified date is greater than our last sample entry or force "
"enabled: fetching data from Sinfo"
msgstr ""
"La data ultima modifica Sinfo è maggiore dell'ultimo campionamento o "
"forzatura eseguita: recupero i dati da Sinfo"

#: Sync.pm:391
#, perl-brace-format
msgid "Getting {what} info from Sinfo"
msgstr "Recupero {what} info da Sinfo"

#: Sync.pm:401
msgid "Data are the same than previous run: I don't save sample run"
msgstr ""
"I dati sono gli stessi del precedente campionamento: non salvo il "
"campionamento"

#: Sync.pm:407
msgid ""
"Sinfo last modified date is not greater than our last sample entry: nothing "
"to do"
msgstr ""
"La data di ultima modifica di Sinfo è minore della nostra data di ultimo "
"campionamento: non faccio nulla"

#~ msgid ""
#~ "Sinfo last modified date is greater than our last sample entry: fetching "
#~ "data from Sinfo"
#~ msgstr ""
#~ "La data di ultima modifica di Sinfo è maggiore della nostra data di "
#~ "ultimo campionamento: recupero i dati da Sinfo"

#~ msgid "Data are the same than previous run: nothing to do"
#~ msgstr ""
#~ "I dati sono gli stessi del precedente campionamento: non faccio nulla"
