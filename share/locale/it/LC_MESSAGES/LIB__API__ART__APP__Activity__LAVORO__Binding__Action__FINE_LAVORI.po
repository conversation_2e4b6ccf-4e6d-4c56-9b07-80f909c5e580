# Language file for package API::ART::APP::Activity::LAVORO::Binding::Action::FINE_LAVORI.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"API::ART::APP::Activity::LAVORO::Binding::Action::FINE_LAVORI\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:25+0200\n"
"PO-Revision-Date: 2025-08-28 10:25+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/LAVORO/Binding/"
"Action\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: FINE_LAVORI.pm\n"

#: FINE_LAVORI.pm:21
msgid "Unable to close activity without team assigment"
msgstr "Impossibile chiudere l'attività senza l'assegnazione alla squadra"

#: FINE_LAVORI.pm:43
#, perl-brace-format
msgid "Param {param} must be greater or equal than {param1}"
msgstr "Il parametro {param} deve essere maggiore o uguale a {param1}"

#: FINE_LAVORI.pm:49
msgid "Attachment mandatory when FIR is required"
msgstr "L'allegato è obbligatorio quando è richiesto il FIR"

#, perl-brace-format
#~ msgid "Missing mandatory param {param}"
#~ msgstr "Parametro obbligatorio mancante {param}"
