# Language file for package WPSOWORKS::Collection::Activity::AS_BUILT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::Collection::Activity::AS_BUILT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: AS_BUILT.pm\n"

#: AS_BUILT.pm:30
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: AS_BUILT.pm:31
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: AS_BUILT.pm:85
msgid "User is not enabled to open the activity"
msgstr "L'utente non è abilitato all'apertura dell'attività"

#: AS_BUILT.pm:116
#, perl-brace-format
msgid "Activities {type} not found!"
msgstr "Attività di tipo {type} non trovata!"

#: AS_BUILT.pm:129
#, perl-brace-format
msgid "Activities {type} must have the same project"
msgstr "Le attività di tipo {type} devono avere lo stesso progetto"

#: AS_BUILT.pm:134
#, perl-brace-format
msgid "Activities {type} must have the same type of maker"
msgstr "Le attività di tipo {type} devono avere lo stesso tipo di gestore"

#: AS_BUILT.pm:140
#, perl-brace-format
msgid "Activities {type} must have the same maker"
msgstr "Le attività di tipo {type} devono avere lo stesso gestore"

#: AS_BUILT.pm:165
#, perl-brace-format
msgid ""
"Unable to find system for customerId {customerId} and contractId {contractId}"
msgstr ""
"Impossibile trovare il sistema per customerId {customerId} e contractId "
"{contractId}"

#: AS_BUILT.pm:194
#, perl-brace-format
msgid "Activity {activityId} created"
msgstr "Attività {activityId} creata"

#~ msgid "User can not open activity"
#~ msgstr "L'utente non può aprire l'attività"
