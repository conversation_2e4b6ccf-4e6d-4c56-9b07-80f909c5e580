# Language file for package WPSOWORKS::MQ::Consumer::ServiceManagement.
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOWORKS::MQ::Consumer::ServiceManagement\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <<PERSON><PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ServiceManagement.pm\n"

#: ServiceManagement.pm:62
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: ServiceManagement.pm:63
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: ServiceManagement.pm:74
msgid "Missing required data 'groups'"
msgstr "Paramentro obbligatorio 'groups' mancante"

#: ServiceManagement.pm:81
msgid "Bad data 'groups': "
msgstr "Parametro 'groups' errato: "

#: ServiceManagement.pm:87
msgid "Bad data 'groups': must be an ARRAY"
msgstr "Parametro 'groups' errato: deve essere un ARRAY"

#: ServiceManagement.pm:95
#, perl-brace-format
msgid "Missing info group: {info}"
msgstr "Info del gruppo mancanti: {info}"

#: ServiceManagement.pm:109
#, perl-brace-format
msgid "Group {groupName} retrieved with id {id}"
msgstr "Gruppo {groupName} recuperato con id {id}"

#: ServiceManagement.pm:112
#, perl-brace-format
msgid "Unable to retrieve group {groupName}: {error}"
msgstr "Impossibile recuperare {groupName} di gruppo: {error}"

#: ServiceManagement.pm:121
#, perl-brace-format
msgid "Group {groupName} created with id {id}"
msgstr "Gruppo {groupName} recuperato con id {id}"

#: ServiceManagement.pm:124
#, perl-brace-format
msgid "Unable to create group {groupName}: {error}"
msgstr "Impossibile creare il gruppo {groupName}: {error}"

#: ServiceManagement.pm:142
#, perl-brace-format
msgid "Group {group} was already present for system {system}"
msgstr "Gruppo {group} già presente per il sistema {system}"

#: ServiceManagement.pm:153
#, perl-brace-format
msgid "Group {group} added to system {system}"
msgstr "Gruppo {group} aggiunto al sistema {system}"

#: ServiceManagement.pm:159
#, perl-brace-format
msgid "Unable to add group {group} to system {system}: {error}"
msgstr "Impossibile aggiungere il gruppo {group} al sistema {system}: {error}"
