# Language file for package WPSOWORKS::MQ::Consumer::Activity::LAVORO::NetworkBooking
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOWORKS::MQ::Consumer::Activity::LAVORO::NetworkBooking\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-28 10:26+0200\n"
"PO-Revision-Date: 2025-08-28 10:26+0200\n"
"Last-Translator: <PERSON> <F<PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, "
"r.be<PERSON><PERSON>@ext.sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.7\n"
"X-Poedit-Basepath: ../../../perl5/WPSOWORKS/MQ/Consumer/Activity/"
"LAVORO\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkBooking.pm\n"

#: NetworkBooking.pm:73
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: NetworkBooking.pm:74
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkBooking.pm:79
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: NetworkBooking.pm:88
#, perl-brace-format
msgid "Bad JSON {err}"
msgstr "JSON non valido {err}"

#: NetworkBooking.pm:104
#, perl-brace-format
msgid "Successfully created activity {id}: current status {status}"
msgstr "Attività con id {id} lavorata correttamente ({status})"

#: NetworkBooking.pm:143
#, perl-brace-format
msgid "Successfully worked activity {id}: current status {status}"
msgstr "Attività con id {id} lavorata correttamente ({status})"

#: NetworkBooking.pm:146
msgid "All works has been worked"
msgstr "Tutti i lavori sono stati processati"
