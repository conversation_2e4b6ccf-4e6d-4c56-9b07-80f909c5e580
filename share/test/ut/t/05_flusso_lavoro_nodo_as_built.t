#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 25;
use JSON;
use MIME::Base32;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::Activity::LAVORO;
use WPSOWORKS::Collection::Activity::AS_BUILT;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $projectId = "99999999";
my $projectGroupName = 'PROJECT_'.$customerId."_".$contractId."_".$projectId;
my $project4RPBGroupName = 'PROJECT4RPB_'.$customerId."_".$contractId."_".$projectId;
my $cadastralCode = 'A794';
my $cityId = '99999999';
my $cityGroupName = 'CITY_'.$cityId;
my $pop = "02 - NA-FUORIGROTTA";
my $popId = encode_base32($pop);
my $ring = "11E";
my $ringId = encode_base32($ring);
my $pfpName = "01e2";
my $workingGroupCode = '6833';
my $latitude = "40.8539282";
my $longitude = "14.2114972";
my $estimatedDuration = 120;

my $gruppoCity = $art->create_group (NAME => $cityGroupName, DESCRIPTION => $cityGroupName);
ok	defined($gruppoCity),
	$msg->( qq{create_group()} . " " . $@ );
	
$class = 'WPSOWORKS::Collection::System::PROJECT';
my $collProject = eval { 
	WPSOWORKS::Collection::System::PROJECT->new(
		ART	=> $art 
	)
};
ok	defined($collProject),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collProject, $class;

my $progetto = $collProject->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "projectId"		=> $projectId
	, "project4RPBGroupName" => $project4RPBGroupName
	, "project4RPBGroupDescription" => $project4RPBGroupName
	, "projectGroupName" => $projectGroupName
	, "projectGroupDescription" => $projectGroupName
	, "cadastralCode" => $cadastralCode
	, "cityId" => $cityId
	, "cityGroupName" => $cityGroupName
	, "pop" => $pop
	, "pfpName" => $pfpName
);
ok defined($progetto),
	$msg->( qq{crea()});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
my $collLAVORO = eval { 
	WPSOWORKS::Collection::Activity::LAVORO->new(
		ART	=> $art 
	)
};
ok	defined($collLAVORO),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collLAVORO, $class;

my $lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Splice'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

my $lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
my $action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
my $collASBUILT = eval { 
	WPSOWORKS::Collection::Activity::AS_BUILT->new(
		ART	=> $art 
	)
};
ok	defined($collASBUILT),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collASBUILT, $class;

my $as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"ttId" 	=> 23333
	}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($lavoro_lc);
$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Splice'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
$as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_KO';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
$as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"ttId" 	=> 23333
	}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA_TT_KO';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		ttKOreason => 'KO ko'
	}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
