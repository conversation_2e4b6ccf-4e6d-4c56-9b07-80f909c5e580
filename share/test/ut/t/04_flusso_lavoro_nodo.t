#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 60;
use JSON;
use MIME::Base32;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::Activity::LAVORO;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $projectId = "99999999";
my $projectGroupName = 'PROJECT_'.$customerId."_".$contractId."_".$projectId;
my $project4RPBGroupName = 'PROJECT4RPB_'.$customerId."_".$contractId."_".$projectId;
my $cadastralCode = 'A794';
my $cityId = '99999999';
my $cityGroupName = 'CITY_'.$cityId;
my $pop = "02 - NA-FUORIGROTTA";
my $popId = encode_base32($pop);
my $ring = "11E";
my $ringId = encode_base32($ring);
my $pfpName = "01e2";
my $workingGroupCode = '6833';
my $latitude = "40.8539282";
my $longitude = "14.2114972";
my $estimatedDuration = 120;

my $gruppoCity = $art->create_group (NAME => $cityGroupName, DESCRIPTION => $cityGroupName);
ok	defined($gruppoCity),
	$msg->( qq{create_group()} . " " . $@ );
	
$class = 'WPSOWORKS::Collection::System::PROJECT';
my $collProject = eval { 
	WPSOWORKS::Collection::System::PROJECT->new(
		ART	=> $art 
	)
};
ok	defined($collProject),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collProject, $class;

my $progetto = $collProject->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "projectId"		=> $projectId
	, "project4RPBGroupName" => $project4RPBGroupName
	, "project4RPBGroupDescription" => $project4RPBGroupName
	, "projectGroupName" => $projectGroupName
	, "projectGroupDescription" => $projectGroupName
	, "cadastralCode" => $cadastralCode
	, "cityId" => $cityId
	, "cityGroupName" => $cityGroupName
	, "pop" => $pop
	, "pfpName" => $pfpName
);
ok defined($progetto),
	$msg->( qq{crea()});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
my $collLAVORO = eval { 
	WPSOWORKS::Collection::Activity::LAVORO->new(
		ART	=> $art 
	)
};
ok	defined($collLAVORO),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collLAVORO, $class;

my $lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Splice'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

my $lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
my $action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});



$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	,"maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00'
		,'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'ANOMALIA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceCode" => "3" ,
		"planningServiceDescription" => "fallita creazione su OFSC"
	}
	),
	$msg->(qq{step($action)});

$action = 'MODIFICA_DATI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'LAVORABILE_DA_SIRTI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"slaStart" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"slaEnd"	=> '2017-06-24T02:00:00.000000000+02:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T03:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'CARICAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"asBuiltId" => 1
	}
	,ATTACHMENTS => ['rtest.pl']
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		ttKOreason => 'KO TT'
	}
	),
	$msg->(qq{step($action)});

$action = 'CARICAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"asBuiltId" => 1
	}
	,ATTACHMENTS => ['rtest.pl']
	),
	$msg->(qq{step($action)});
	
$action = 'CHIUSURA_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'ANOMALIA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceCode" => "3" ,
		"planningServiceDescription" => "fallita creazione su OFSC"
	}
	),
	$msg->(qq{step($action)});

$action = 'MODIFICA_DATI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'LAVORABILE_DA_SIRTI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"slaStart" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"slaEnd"	=> '2017-06-24T02:00:00.000000000+02:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T04:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'NOT_DONE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T09:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T12:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE_UNSCHEDULE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
	}
	),
	$msg->(qq{step($action)});

ok !defined $lavoro_lc->activity_property('teamId'),
	$msg->("activity_property(teamId)");
	
ok !defined $lavoro_lc->activity_property('teamName'),
	$msg->("activity_property(teamName)");	

ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->activity_property('teamId') eq 'T1',
	$msg->("activity_property(teamId)");
	
ok $lavoro_lc->activity_property('teamName') eq 'TN1',
	$msg->("activity_property(teamName)");

$action = 'RICHIESTA_ANNULLAMENTO_KO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceCode" => 1
	}
	),
	$msg->(qq{step($action)});
	
$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,recoveryCable => 'CAVO di recupero'
		,infrastructureCheck => 'FATTO'
		,plannedBreakdown => 'SI'
		,interruptedMinitubes => 'SI'
		,branchesCut => 'SI'
		,pilingCheck => 'SI'
		,cable144foConnection => 3
		,cable192foConnection => 4
		,cable24foConnection => 5
		,cable48foConnection => 6
		,cable96foConnection => 7
		,cableOver192foConnection => 8
		,foJunction => 9
		,pfpMeasure => 10
		,pfsMeasure => 11
		,ptaMeasure => 12
		,pteMeasure => 13
		,splitterPermutations => 14
		,splitter116Placing => 15
		,splitter14Placing => 16
		,terminations => 17
		,continuousCablesConnection => 'SI'
		,fibersPlacedJunction => 'SI'
		,prewiredPFS => 'SI'
		,pfsPosition => 'pfpPosition'
		,junctionType => 'junctionType'
		,junctionSite => 'junctionSite'
		,ptaSite => 'ptaSite'
		,pteSite => 'pteSite'
		,lackOfMaterial => 'SI'
		,notAccessibleCockpit => 'SI'
		,emptyingCockpit => 'SI'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});	

$action = 'RIPRESA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_ANNULLAMENTO_OK';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Splice'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
	}
	,"details" => [{
		"projectId"		=> $projectId
		, "ringId"	=> $ringId
		,"networkElementName" => 'test'
		,"networkElementId" => '1'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude" => $latitude
		,"longitude" => $longitude
		,"estimatedDuration" => $estimatedDuration
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_ANNULLAMENTO_KO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceCode" => 1
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO_FORZATO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
