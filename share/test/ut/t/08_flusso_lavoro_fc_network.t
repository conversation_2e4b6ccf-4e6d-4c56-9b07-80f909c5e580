#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 29;

use WPSOWORKS::Collection::Activity::LAVORO;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;


my $workingGroupCode = '106656';
my $networks = $art->_dbh()->fetchall_hashref('
	select dts.valore "customerId"
		, dts2.valore "contractId"
		, dts3.valore "networkId"
	from v_sistemi vs
		join v_dt_sistemi dts on dts.id_sistema = vs.id and dts.nome = \'customerId\'
		join v_dt_sistemi dts2 on dts2.id_sistema = vs.id and dts2.nome = \'contractId\'
		join v_dt_sistemi dts3 on dts3.id_sistema = vs.id and dts3.nome = \'networkId\'
		left join v_dt_sistemi dts4 on dts4.id_sistema = vs.id and dts4.nome = \'onFieldIntegrationDisabled\'
	where vs.tipo = \'NETWORK\'
		and dts4.valore is null
	and rownum<2
');
my $customerId = $networks->[0]->{customerId};
my $contractId = $networks->[0]->{contractId};
my $networkId = $networks->[0]->{networkId};
my $description = 'ut';

my $today = $art->get_iso_date_from_date(substr($art->_dbh()->get_sysdate(),0,8));

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
my $collLAVORO = eval { 
	WPSOWORKS::Collection::Activity::LAVORO->new(
		ART	=> $art 
	)
};
ok	defined($collLAVORO),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collLAVORO, $class;

my $lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 0
	, "maker"				=> 'Subcontract'
	, "subcontractInfo"		=> {
		'startPlannedDate'	=> $today
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	, "details" => [
		{
			"type" => "updateDatabase"
		},
		{
			"type" => "test"
		},
		{
			"type" => "junction"
		},
		{
			"type" => "civil"
		},
		{
			"type" => "cableLaying"
		},
		{
			"type" => "survey"
		},
		{
			"type" => "design"
		},
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

my $lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
ok defined $lavoro_lc->system_property('networkId'),
	$msg->("system_property(networkId)");

ok defined $lavoro_lc->system_property('updateDatabase'),
	$msg->("system_property(updateDatabase)");

ok $lavoro_lc->activity_property('projectId') eq $networkId,
	$msg->("activity_property(projectId)");

my $action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ANNULLATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 0
	, "maker"				=> 'Subcontract'
	, "subcontractInfo"		=> {
		'startPlannedDate'	=> $today
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	, "details" => [
		{
			"type" => "updateDatabase"
		},
		{
			"type" => "test"
		},
		{
			"type" => "junction"
		},
		{
			"type" => "civil"
		},
		{
			"type" => "cableLaying"
		},
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
ok !defined $lavoro_lc->system_property('survey'),
	$msg->("system_property(survey)");

$action = 'FINE_LAVORI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startWorkDate"	=> '2017-06-14T02:00:00.000000000+02:00',
		"endWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 1
	, "maker"				=> 'Subcontract'
	, "subcontractInfo"		=> {
		'startPlannedDate'	=> $today
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	, "details" => [
		{
			"type" => "updateDatabase"
		},
		{
			"type" => "test"
		},
		{
			"type" => "junction"
		},
		{
			"type" => "civil"
		},
		{
			"type" => "cableLaying"
		},
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
ok !defined $lavoro_lc->system_property('survey'),
	$msg->("system_property(survey)");

$action = 'FINE_LAVORI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startWorkDate"	=> '2017-06-14T02:00:00.000000000+02:00',
		"endWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	}
	,ATTACHMENTS => [
		{
			FILENAME => '/dev/null',
			DOC_TYPE => 'FIR'
		}
	]
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"			=> $customerId
#	, "contractId"			=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode"	=> $workingGroupCode
#	,"maker" => 'Team'
#	,"team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00'
#		,'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		},
#		{
#			"type" => "test"
#		},
#		{
#			"type" => "junction"
#		},
#		{
#			"type" => "civil"
#		},
#		{
#			"type" => "cableLaying"
#		},
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'ANOMALIA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"KO",
#		"planningServiceCode" => "3" ,
#		"planningServiceDescription" => "fallita creazione su OFSC"
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'MODIFICA_DATI';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'LAVORABILE_DA_SIRTI';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"slaStart" 	=> '2017-06-14T02:00:00.000000000+02:00'
#		,"slaEnd"	=> '2017-06-24T02:00:00.000000000+02:00'
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'MOVE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T03:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'COMPLETE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T03:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");
#
#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"		=> $customerId
#	, "contractId"		=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode" => $workingGroupCode
#	, "maker" => 'Team'
#	, "team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
#		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		}
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'ANOMALIA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"KO",
#		"planningServiceCode" => "3" ,
#		"planningServiceDescription" => "fallita creazione su OFSC"
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'MODIFICA_DATI';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'LAVORABILE_DA_SIRTI';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"slaStart" 	=> '2017-06-14T02:00:00.000000000+02:00'
#		,"slaEnd"	=> '2017-06-24T02:00:00.000000000+02:00'
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'COMPLETE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T04:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");
#
#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"		=> $customerId
#	, "contractId"		=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode" => $workingGroupCode
#	, "maker" => 'Team'
#	,"team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
#		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		}
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'NOT_DONE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T09:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'COMPLETE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T12:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");
#
#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"		=> $customerId
#	, "contractId"		=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode" => $workingGroupCode
#	, "maker" => 'Team'
#	,"team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
#		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		}
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'ANNULLAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'MOVE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'MOVE_UNSCHEDULE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok !defined $lavoro_lc->activity_property('teamId'),
#	$msg->("activity_property(teamId)");
#	
#ok !defined $lavoro_lc->activity_property('teamName'),
#	$msg->("activity_property(teamName)");
#
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'MOVE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#	}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->activity_property('teamId') eq 'T1',
#	$msg->("activity_property(teamId)");
#	
#ok $lavoro_lc->activity_property('teamName') eq 'TN1',
#	$msg->("activity_property(teamName)");
#
#$action = 'RICHIESTA_ANNULLAMENTO_KO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"KO",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceCode" => 1
#	}
#	),
#	$msg->(qq{step($action)});
#	
#$action = 'AVANZAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		eventType => 'COMPLETE'
#		,note => 'note'
#		,startPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endPlannedDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,startWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,endWorkDate =>  '2017-06-24T02:00:00.000000000+02:00'
#		,teamId => 'T1'
#		,teamName => 'TN1'
#		,doneUndergroundLength => 'doneUndergroundLength'
#		,doneAerialLength => 'doneAerialLength'
#		,doneFacadeLength => 'doneFacadeLength'
#		,doneHandmaidLength => 'doneHandmaidLength'
#		,recoveryCable => 'recoveryCable'
#		,infrastructureCheck => 'infrastructureCheck'
#		,plannedBreakdown => 'plannedBreakdown'
#		,interruptedMinitubes => 'interruptedMinitubes'
#		,branchesCut => 'branchesCut'
#		,pilingCheck => 'pilingCheck'
#		,teamNote => 'teamNote'
#		,assistantNote => 'assistantNote'
#		,onFieldAssistant => '091247.Mura Salvatore'
#	}
#	),
#	$msg->(qq{step($action)});	
#
#$action = 'RIPRESA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");
#
#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"		=> $customerId
#	, "contractId"		=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode" => $workingGroupCode
#	, "maker" => 'Team'
#	,"team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
#		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		}
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'ANNULLAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'RICHIESTA_ANNULLAMENTO_OK';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ANNULLATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");
#
#$class = 'WPSOWORKS::Collection::Activity::LAVORO';
#$lavori_lc = $collLAVORO->crea(
#	"customerId"		=> $customerId
#	, "contractId"		=> $contractId
#	, "networkId"			=> $networkId
#	, "workType"			=> 'Network'
#	, "description"			=> $description
#	, "workingGroupCode" => $workingGroupCode
#	, "maker" => 'Team'
#	,"team" => {
#		'slaStart' => '2017-06-14T02:00:00.000000000+02:00',
#		'slaEnd' => '2017-06-14T02:00:00.000000000+02:00'
#	}
#	, "details" => [
#		{
#			"type" => "updateDatabase"
#		}
#	]
#);
#ok defined($lavori_lc),
#	$msg->( qq{crea()});
#
#$lavoro_lc = $lavori_lc->[0];
#
#$class = ref($lavoro_lc);
#$action = 'CONFERMA';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"OK",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceId" => "OFSC1",
#		"planningServiceCode" => 0
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'ANNULLAMENTO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'RICHIESTA_ANNULLAMENTO_KO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {
#		"planningServiceResult"=>"KO",
#		"planningServiceDescription" => "fallita creazione su OFSC",  
#		"planningServiceCode" => 1
#	}
#	),
#	$msg->(qq{step($action)});
#
#$action = 'ANNULLAMENTO_FORZATO';
#ok $lavoro_lc->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});
#
#ok $lavoro_lc->get_current_status_name() eq 'ANNULLATO',
#	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$networks = $art->_dbh()->fetchall_hashref('
	select dts.valore "customerId"
		, dts2.valore "contractId"
		, dts3.valore "networkId"
	from v_sistemi vs
		join v_dt_sistemi dts on dts.id_sistema = vs.id and dts.nome = \'customerId\'
		join v_dt_sistemi dts2 on dts2.id_sistema = vs.id and dts2.nome = \'contractId\'
		join v_dt_sistemi dts3 on dts3.id_sistema = vs.id and dts3.nome = \'networkId\'
		join v_dt_sistemi dts4 on dts4.id_sistema = vs.id and dts4.nome = \'onFieldIntegrationDisabled\'
	where vs.tipo = \'NETWORK\'
		and dts4.valore = \'1\'
	and rownum<2
');
$customerId = $networks->[0]->{customerId};
$contractId = $networks->[0]->{contractId};
$networkId = $networks->[0]->{networkId};

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 0
	, "maker" => 'Team'
	, "team" => {
		'slaStart' => $today,
		'slaEnd' => $today,
		'teamId' => '1',
		'teamName' => 'pipppo',
	}
	, "onFieldIntegrationDisabled" => 1
	, "details" => [
		{
			"type" => "updateDatabase"
		}
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'FINE_LAVORI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startWorkDate"	=> '2017-06-14T02:00:00.000000000+02:00',
		"endWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 1
	, "maker" => 'Team'
	, "team" => {
		'slaStart' => $today,
		'slaEnd' => $today,
		'teamId' => '1',
		'teamName' => 'pipppo',
	}
	, "onFieldIntegrationDisabled" => 1
	, "details" => [
		{
			"type" => "updateDatabase"
		}
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'FINE_LAVORI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startWorkDate"	=> '2017-06-14T02:00:00.000000000+02:00',
		"endWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	}
	,ATTACHMENTS => [
		{
			FILENAME => '/dev/null',
			DOC_TYPE => 'FIR'
		}
	]
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ESPLETATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"			=> $customerId
	, "contractId"			=> $contractId
	, "assetId"				=> $networkId
	, "workType"			=> 'Network'
	, "description"			=> $description
	, "workingGroupCode"	=> $workingGroupCode
	, "flagFIR"				=> 0
	, "maker" => 'Team'
	, "team" => {
		'slaStart' => $today,
		'slaEnd' => $today,
		'teamId' => '1',
		'teamName' => 'pipppo',
	}
	, "onFieldIntegrationDisabled" => 1
	, "details" => [
		{
			"type" => "updateDatabase"
		}
	]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'ANNULLAMENTO_SIRTI_OFFLINE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->get_current_status_name() eq 'ANNULLATO',
	$msg->("check_stato_finale(".$lavoro_lc->get_current_status_name().")");

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
