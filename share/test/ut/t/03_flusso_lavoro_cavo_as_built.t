#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 21;
use JSON;
use MIME::Base32;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::Activity::LAVORO;
use WPSOWORKS::Collection::Activity::AS_BUILT;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $cadastralCode = 'A794';
my $pop = "02 - NA-FUORIGROTTA";
my $popId = encode_base32($pop);
my $ring = "11E";
my $ringId = encode_base32($ring);
my $workingGroupCode = '6833';
my $cables = $art->_dbh()->fetchall_hashref('
	select c."cableId"
		, dts.valore "projectId"
	From v_sistemi s
	  join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = \'projectId\'
	  join v_cavi c on c."projectId" = dts.valore
	where s.tipo = \'PROJECT\'
	and not exists (
	  select 1
	  from v_sistemi s2
		join v_dt_sistemi dts2 on dts2.id_sistema = s2.id and dts2.nome = \'cableId\'
	  where s2.tipo = \'CAVO\'
		and dts2.valore = c."cableId"
	)
	and rownum<2
');
my $cableId = $cables->[0]->{cableId};
my $projectId = $cables->[0]->{projectId};
my $projectGroupName = 'PROJECT_'.$customerId."_".$contractId."_".$projectId;
my $project4RPBGroupName = 'PROJECT4RPB_'.$customerId."_".$contractId."_".$projectId;

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
my $collLAVORO = eval { 
	WPSOWORKS::Collection::Activity::LAVORO->new(
		ART	=> $art 
	)
};
ok	defined($collLAVORO),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collLAVORO, $class;

my $lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Cable'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

my $lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
my $action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
my $collASBUILT = eval { 
	WPSOWORKS::Collection::Activity::AS_BUILT->new(
		ART	=> $art 
	)
};
ok	defined($collASBUILT),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collASBUILT, $class;

my $as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"ttId" 	=> 23333
	}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($lavoro_lc);
$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Cable'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
$as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_KO';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::AS_BUILT';
$as_built = $collASBUILT->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "ids" => [$lavoro_lc->id()]
	, "description" => 'apertura tT'
	, "subContractStartWorkDate" 	=> '2017-06-14T02:00:00.000000000+02:00'
	, "subContractEndWorkDate" 	=> '2017-06-15T02:00:00.000000000+02:00'
);
ok defined($as_built),
	$msg->( qq{crea()});

$class=ref($as_built);
$action = 'APERTURA_TT_OK';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"ttId" 	=> 23333
	}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA_TT_KO';
ok $as_built->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		ttKOreason => 'KO ko'
	}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
