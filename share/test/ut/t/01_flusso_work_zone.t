#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 8;
use JSON;

use WPSOWORKS::Collection::Activity::WZ_LC;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $projectId = "99999999";
my $permitsAreaId = "88888888";

$class = 'WPSOWORKS::Collection::Activity::WZ_LC';
my $collWZ_LC = eval { 
	WPSOWORKS::Collection::Activity::WZ_LC->new(
		ART	=> $art 
	)
};
ok	defined($collWZ_LC),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collWZ_LC, $class;

my $wz_lc = $collWZ_LC->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "projectId"		=> $projectId
	, "permitsAreaId"	=> $permitsAreaId
	, "workZoneId"		=> '1'
	, "requestDate"		=> '2012-07-14T01:00:00+01:00'
	, "name"			=> "test"
	, "polygon"			=> {
		"coordinates" => [[[38.09445325947705,13.362910022445703],[38.09478516445756,13.362473646774333],[38.095457699926655,13.362130171905392],[38.09427493074841,13.360116319404597],[38.093759191284434,13.3606237433778],[38.09415266586765,13.362088747993539]]]
		,"type" => "Polygon"
	}
	, "centralPoint"		=> '123123'
	, "note"			=> 'note test'
	, "username"			=> 'ROOT'
);
ok defined($wz_lc),
	$msg->( qq{crea()});

my $action = 'CHIUSURA';
ok $wz_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
