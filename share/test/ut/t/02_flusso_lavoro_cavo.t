#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 70;
use JSON;
use MIME::Base32;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::Activity::LAVORO;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $cadastralCode = 'A794';
my $pop = "02 - NA-FUORIGROTTA";
my $popId = encode_base32($pop);
my $ring = "11E";
my $ringId = encode_base32($ring);
my $workingGroupCode = '6833';
my $cables = $art->_dbh()->fetchall_hashref('
	select c."cableId"
		, c."projectId"
	From v_sistemi s
	  join v_dt_sistemi dts on dts.id_sistema = s.id and dts.nome = \'projectId\'
	  join v_cavi c on c."projectId" = dts.valore
	where s.tipo = \'PROJECT\'
	and c."status" = \'Workable\'
	and c."customerId" = \'ENEL\'
	and c."contractId" = \'FTTH\'
	and not exists (
	  select 1
	  from v_sistemi s2
		join v_dt_sistemi dts2 on dts2.id_sistema = s2.id and dts2.nome = \'cableId\'
	  where s2.tipo = \'CAVO\'
		and dts2.valore = c."cableId"
	)
	and rownum<2
');
my $cableId = $cables->[0]->{cableId};
my $projectId = $cables->[0]->{projectId};

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
my $collLAVORO = eval { 
	WPSOWORKS::Collection::Activity::LAVORO->new(
		ART	=> $art 
	)
};
ok	defined($collLAVORO),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collLAVORO, $class;

my $lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Cable'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.*********+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

my $lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
ok defined $lavoro_lc->system_property('plannedToStockLength'),
	$msg->("system_property(plannedToStockLength)");

ok defined $lavoro_lc->system_property('plannedFromStockLength'),
	$msg->("system_property(plannedFromStockLength)");

my $action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.*********+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	,"maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00'
		,'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'ANOMALIA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceCode" => "3" ,
		"planningServiceDescription" => "fallita creazione su OFSC"
	}
	),
	$msg->(qq{step($action)});

$action = 'MODIFICA_DATI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'LAVORABILE_DA_SIRTI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"slaStart" 	=> '2017-06-14T02:00:00.*********+02:00'
		,"slaEnd"	=> '2017-06-24T02:00:00.*********+02:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T03:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'RENDICONTAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
			"accountingData"=>{
				"poseTypeChanges" => $JSON::true,
				"doneUndergroundLength" => 1,
				"doneHandmaidLength" => 1,
				"doneAerialLength" => 1,
				"doneFacadeLength" => 1,
				"doneToStockLength" => 1,
				"doneFromStockLength" => 1
			},
			"accountingUser" => "TN1",
			"accountingNote" => "Forza Atalanta",
			"planningServiceId" => "OFSC1"
		}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T03:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'CARICAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"asBuiltId" => 1
	}
	,ATTACHMENTS => ['rtest.pl']
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		ttKOreason => 'KO TT'
	}
	),
	$msg->(qq{step($action)});

$action = 'CARICAMENTO_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"asBuiltId" => 1
	}
	,ATTACHMENTS => ['rtest.pl']
	),
	$msg->(qq{step($action)});
	
$action = 'CHIUSURA_AS_BUILT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00',
		'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'ANOMALIA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceCode" => "3" ,
		"planningServiceDescription" => "fallita creazione su OFSC"
	}
	),
	$msg->(qq{step($action)});

$action = 'MODIFICA_DATI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'LAVORABILE_DA_SIRTI';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"slaStart" 	=> '2017-06-14T02:00:00.*********+02:00'
		,"slaEnd"	=> '2017-06-24T02:00:00.*********+02:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T04:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00',
		'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'NOT_DONE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T09:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T12:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00',
		'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE_UNSCHEDULE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
	}
	),
	$msg->(qq{step($action)});

ok !defined $lavoro_lc->activity_property('teamId'),
	$msg->("activity_property(teamId)");
	
ok !defined $lavoro_lc->activity_property('teamName'),
	$msg->("activity_property(teamName)");

ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'MOVE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
	}
	),
	$msg->(qq{step($action)});

ok $lavoro_lc->activity_property('teamId') eq 'T1',
	$msg->("activity_property(teamId)");
	
ok $lavoro_lc->activity_property('teamName') eq 'TN1',
	$msg->("activity_property(teamName)");

$action = 'RICHIESTA_ANNULLAMENTO_KO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceCode" => 1
	}
	),
	$msg->(qq{step($action)});
	
$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});	

$action = 'RIPRESA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'AS_BUILT_NON_NECESSARIO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00',
		'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_ANNULLAMENTO_OK';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00',
		'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_ANNULLAMENTO_KO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"KO",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceCode" => 1
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO_FORZATO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOWORKS::Collection::Activity::LAVORO';
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "workType" => 'Cable'
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workingGroupCode" => $workingGroupCode
	,"maker" => 'Team'
	,"team" => {
		'slaStart' => '2017-06-14T02:00:00.*********+02:00'
		,'slaEnd' => '2017-06-14T02:00:00.*********+02:00'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'CONFERMA';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"planningServiceResult"=>"OK",
		"planningServiceDescription" => "fallita creazione su OFSC",  
		"planningServiceId" => "OFSC1",
		"planningServiceCode" => 0
	}
	),
	$msg->(qq{step($action)});

$action = 'RENDICONTAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
			"accountingData"=>{
				"poseTypeChanges" => $JSON::true,
				"doneUndergroundLength" => 1,
				"doneHandmaidLength" => 1,
				"doneAerialLength" => 1,
				"doneFacadeLength" => 1,
				"doneToStockLength" => 1,
				"doneFromStockLength" => 1
			},
			"accountingUser" => "PIPPO",  
			"planningServiceId" => "OFSC1"
		}
	),
	$msg->(qq{step($action)});

$action = 'AVANZAMENTO';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		eventType => 'COMPLETE'
		,note => 'note'
		,startPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endPlannedDate =>  '2017-06-24T02:00:00.*********+02:00'
		,startWorkDate =>  '2017-06-24T02:00:00.*********+02:00'
		,endWorkDate =>  '2017-06-24T03:00:00.*********+02:00'
		,teamId => 'T1'
		,teamName => 'TN1'
		,doneUndergroundLength => 'doneUndergroundLength'
		,doneAerialLength => 'doneAerialLength'
		,doneFacadeLength => 'doneFacadeLength'
		,doneHandmaidLength => 'doneHandmaidLength'
		,recoveryCable => 'recoveryCable'
		,infrastructureCheck => 'infrastructureCheck'
		,plannedBreakdown => 'plannedBreakdown'
		,interruptedMinitubes => 'interruptedMinitubes'
		,branchesCut => 'branchesCut'
		,pilingCheck => 'pilingCheck'
		,teamNote => 'teamNote'
		,assistantNote => 'assistantNote'
		,onFieldAssistant => '091247.Mura Salvatore'
	}
	),
	$msg->(qq{step($action)});

$action = 'INOLTRO_RENDICONTAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
			"validationNote" => "Forza Atalanta",
			"validationData"=>{
				"poseTypeChangesV" => $JSON::true,
				"doneUndergroundLengthV" => 1.5,
				"doneHandmaidLengthV" => 1,
				"doneAerialLengthV" => 1,
				"doneFacadeLengthV" => 1,
				"doneToStockLengthV" => 1,
				"doneFromStockLengthV" => 1
			}
		}
	),
	$msg->(qq{step($action)});

$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($collLAVORO);
$lavori_lc = $collLAVORO->crea(
	"customerId"		=> $customerId
	, "contractId"		=> $contractId
	, "cadastralCode" => $cadastralCode
	, "popId"	=> $popId
	, "workType" => 'Cable'
	, "workingGroupCode" => $workingGroupCode
	, "maker" => 'Subcontract'
	, "subcontractInfo" => {
		'startPlannedDate' => '2017-06-14T02:00:00.*********+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	,"details" => [{
			"ringId"		=> $ringId
			,"cableId"		=> $cableId
			,"projectId"	=> $projectId
	}]
);
ok defined($lavori_lc),
	$msg->( qq{crea()});

$lavoro_lc = $lavori_lc->[0];

$class = ref($lavoro_lc);
$action = 'PIANIFICAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"startPlannedDate" 	=> '2017-06-14T02:00:00.*********+02:00'
		,"subContractName"	=> 'SIELTE'
		,"subContractCode"	=> 'ASI123'
	}
	),
	$msg->(qq{step($action)});

$action = 'INOLTRO_RENDICONTAZIONE';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
			"subContractStartWorkDate" 	=> '2017-06-14T02:00:00.*********+02:00'
			,"subContractEndWorkDate" 	=> '2017-06-24T02:00:00.*********+02:00'
			,"validationData"=>{
				"poseTypeChangesV" => $JSON::true,
				"doneUndergroundLengthV" => 1.5,
				"doneHandmaidLengthV" => 1,
				"doneAerialLengthV" => 1,
				"doneFacadeLengthV" => 1,
				"doneToStockLengthV" => 1,
				"doneFromStockLengthV" => 1
			}
		}
	),
	$msg->(qq{step($action)});

$action = 'TIMEOUT';
ok $lavoro_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
