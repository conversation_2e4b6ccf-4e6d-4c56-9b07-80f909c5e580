#!/usr/bin/perl -w

use strict;
use warnings;
use TAP::Harness;
use Getopt::Long;

#esegue controlli case sensitive sugli switch e non accetta switch -- abbreviati.
Getopt::Long::Configure(qw(bundling no_ignore_case no_auto_abbrev));

my $usage = sub {
	print <<"**EOT**";

Usage:

	$0 [OPTIONS] connstr artuser password 
	
OPTIONS:
	
	-h|--help ..........: this help
	-c|--color .........: uses color output
	-v|--verbosity=n ...: set verbosity level:
		 1   verbose        Print individual test results to STDOUT.
		 0   normal
		-1   quiet          Suppress some test output (mostly failures while tests are running).
		-2   really quiet   Suppress everything but the tests summary.
		-3   silent         Suppress everything.
**EOT**
;
	exit 1;
};

my $verbosity  = 0;
my $color      = 0;
my $show_count = 1;

if (
	GetOptions(
		'h|help'        => sub { $usage->() },
		'c|color'       => \$color,
		'v|verbosity=i' => \$verbosity,
	)
  ) {
	my $harness = TAP::Harness->new(
		{
			color      => $color,
			verbosity  => $verbosity,
			show_count => $show_count,
			test_args  => [@ARGV],
			lib        => [ '../.' ]
		}
	);
	$harness->runtests(
		map {
			chomp;
			$_
		  } `ls -1 ./t/*.t`
	);
} else {
	$usage->();
}
