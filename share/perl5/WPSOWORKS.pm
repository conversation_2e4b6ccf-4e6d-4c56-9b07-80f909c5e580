package WPSOWORKS;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use REST::Client;
use HTTP::Status qw(:constants :is status_message);
use JSON;
use Crypt::JWT qw(encode_jwt decode_jwt);
use HTTP::Request::Common;
use LWP::UserAgent;

use SIRTI::Reports;
use SIRTI::Cache;

use WPSOWORKS::Profilo;
use WPSOWORKS::Collection::System::CUSTOMER;
use WPSOWORKS::Collection::System::CONTRACT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

my $_are_master_sessions_registered = 0;

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{UTILS} = SIRTI::Reports->instance(DB => $self->_db());
	
	$self->{SINFO_WS_BASE_URL} = $ENV{SINFO_WS_BASE_URL};
	
	return bless( $self, $class );
}

sub art { shift->{ART} }

sub _db { shift->{ART}->_dbh() }

sub _logger { shift->{LOGGER} }

sub _utils { shift->{UTILS} }

sub _get_sinfo_ws_base_url { shift->{SINFO_WS_BASE_URL} }

sub camelize
{
	my $self = shift;
	my $s = shift;
	return lcfirst(join('', map{ ucfirst lc($_) } split(/(?<=[A-Za-z])_(?=[A-Za-z])|\b/, $s)));
}

# restituisce un nome file dove sono elencati i dati relativi al report
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anziché un nome file, default false
sub report {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 QUERY		=> { isa => 'SCALAR' }
					
				}
				,OPTIONAL   => {
					 COLUMNS								=> { isa => 'ARRAY' }
					,OUTPUT_FORMAT							=> { isa => 'SCALAR', list => [ 'xlsx', 'json', 'csv' ] }
					,DIR									=> { isa => 'SCALAR' }
					,JSON_RETURN_STRING						=> { isa => 'SCALAR', list => [ 1, 0 ] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$params{OUTPUT_FORMAT} = 'json' unless defined $params{OUTPUT_FORMAT};
	$params{JSON_RETURN_STRING} = defined $params{JSON_RETURN_STRING} ? $params{JSON_RETURN_STRING} : 0;
	
	my %report_params = (
		 QUERY => $params{QUERY}
		,OUTPUT_FORMAT => $params{OUTPUT_FORMAT}
		,XLSX_SHEET_NAME => $params{ACTIVITY_TYPE_NAME}
		,XLSX_DATE_FORMAT => 'dd/mm/yyyy'
		,XLSX_FONT => 'Calibri'
		,XLSX_STRING_HORIZONTAL_ALIGN => 'left'
		,XLSX_NUMBER_HORIZONTAL_ALIGN => 'right'
		,XLSX_DATE_HORIZONTAL_ALIGN => 'right'
		,SQL_DATE_FORMAT_STRING => 'DD/MM/YYYY'
		,ENCODING => 'utf8'
		,JSON_RETURN_STRING => $params{JSON_RETURN_STRING}
		,JSON_USE_ID_INSTEAD_OF_NAME => 1
	);
	$report_params{COLUMNS} = $params{COLUMNS} if defined $params{COLUMNS};
	$report_params{DIR} = $params{DIR} if defined $params{DIR};
	
	my $report = $self->_utils->report( %report_params );

	$self->art()->last_error($self->_utils->last_error())
		&& return undef
			unless defined $report;
	
	return $report;
}

sub profilo {
	my $self = shift;
	
	my $profilo = eval {
		WPSOWORKS::Profilo->new(ART => $self->art())
	};
	
	$self->art()->last_error($@)
		&& return undef
			if $@;
			
	return $profilo;
}

sub rest_client_sinfo {
	my $self = shift;
	
	$self->{REST_CLIENT_SINFO} = REST::Client->new( host => $self->_get_sinfo_ws_base_url() )
		unless($self->{REST_CLIENT_SINFO});
	
	return $self->{REST_CLIENT_SINFO};
}

sub lwp_user_agent {
	my $self = shift;
	
	$self->{LWP_USER_AGENT} = LWP::UserAgent->new()
		unless($self->{LWP_USER_AGENT});
	
	return $self->{LWP_USER_AGENT};
}

sub upload_tmpfiles{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 SERVICE		=> { isa => 'SCALAR' }
					 ,ATTACHMENTS	=> { isa => 'ARRAY' }
					 ,JWT_SID		=> { isa => 'SCALAR' }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	
	my $auth_header = "JWT ".encode_jwt(
			payload => { sid => $params{JWT_SID} }
			, key => $ENV{SINFO_JWT_SECRET}
			, alg => 'HS256'
		);
	
	my $res = $self->lwp_user_agent()->request(
		POST $self->_get_sinfo_ws_base_url().sprintf($ENV{SINFO_POST_TMPFILES_MASK_RESOURCE}, $params{SERVICE})
		,Content_Type => 'form-data'
		,Content => [
			attachment => $params{"ATTACHMENTS"}
		]
		,'X-jwt-Authorization' => $auth_header,
		,'Accept' =>  'application/json'
	);
 	
	if($res->is_success) {
		my $ret = eval{ decode_json $res->content() };
		if($@) {
			$self->art()->last_error(__x("Unable to parse server response (MESSAGE: {response_content})", response_content => $res->content()));
			return undef;
		}
		return $ret;
	} elsif($res->is_client_error) {
		my $ret = eval{ decode_json $res->content() };
		if($@) {
			$self->art()->last_error(__x("Unable to parse server response (MESSAGE: {response_content})", response_content => $res->content()));
			return undef;
		}
		$self->art()->last_error(__x("Client error"));
		return undef;
	} else{#if(is_server_error()) {
		my $ret = eval{ decode_json $res->content() };
		if($@) {
			$self->art()->last_error(__x("Unable to parse server response (MESSAGE: {response_content})", response_content => $res->content()));
			return undef;
		}
		$self->art()->last_error(__x("Server error"));
		return undef;
	}
}

# FIXME: ora la funzione è utilizzata anche per entrare su altri sottosistemi e quindi
#        è scorretto che si chiami invoke_sinfo_rest
sub invoke_sinfo_rest {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 RESOURCE	=> { isa => 'SCALAR', pattern => qr{^/} }
					,IS_FATAL	=> { isa => 'SCALAR' }
				}
				,OPTIONAL => {
					 QUERY_PARAMS	=> { isa => 'HASH' }
					,METHOD			=> { isa => 'SCALAR', list => [ 'GET', 'HEAD', 'POST', 'PUT', 'DELETE' ]}
					,BODY			=> { isa => 'HASH' }
					,RAW_BODY		=> { isa => 'SCALAR' }
					,HEADERS		=> { isa => 'HASH' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	${$params{IS_FATAL}} = 0;
	
	$params{METHOD} = 'GET'
		unless defined $params{METHOD};
	
	$params{HEADERS} = { 'Accept' => 'application/json' }
		unless defined $params{HEADERS};
	
	if(defined $params{BODY} && defined $params{RAW_BODY}) {
		$self->art()->last_error(__("You cannot use BODY and RAW_BODY jointly"));
		${$params{IS_FATAL}} = 1;
		return undef;
	}
	
	if(defined $params{BODY} && $params{METHOD} =~ /^(GET|HEAD|DELETE)$/) {
		$self->art()->last_error(__("You cannot use BODY param with method GET, HEAD or DELETE"));
		${$params{IS_FATAL}} = 1;
		return undef;
	}
	
	my $rest_client_sinfo = $self->rest_client_sinfo();
	
	my $query_params = '';
	if(defined $params{QUERY_PARAMS}) {
		$query_params = $rest_client_sinfo->buildQuery(
			%{$params{QUERY_PARAMS}}
		);
	}
	my $url = $params{RESOURCE}.$query_params;
	
	$self->_logger()->debug("Invoco il ws sinfo $params{METHOD} ".$self->_get_sinfo_ws_base_url().$url);
	my @rest_params = ($url);
	if($params{METHOD} =~ /^(POST|PUT)$/) {
		if(defined $params{BODY}) {
			if($params{RAW_BODY}) {
				push @rest_params, $params{BODY};
			} else {
				push @rest_params, encode_json($params{BODY});
				$params{HEADERS}->{"Content-Type"} = 'application/json';
			}
		} else {
			push @rest_params, {};
		}
	}
	
	# rendo tutte minuscole le chiavi in quanto l'headers è case-insensitive
	for my $k (keys %{$params{HEADERS}}){
		# se la chiave è già minuscola  non devo fare nulla
		next if lc($k) eq $k;
		# altrimenti la converto in minuscola e cancello la chiave esistente
		$params{HEADERS}->{lc($k)} = $params{HEADERS}->{$k};
		delete $params{HEADERS}->{$k};
	}
	
	unless (defined $params{HEADERS}->{"x-jwt-authorization"}){
		# creo jwt da mettere nell'header
		# FIXME: ora la funzione è utilizzata anche per entrare su altri sottosistemi e quindi
		#        è scorretto mettere il JWT dell'utente SiNFO
		$params{HEADERS}->{"x-jwt-authorization"} = "JWT ".encode_jwt(
			payload => { sid => $ENV{WPSOWORKS_USER_SID} }
			, key => $ENV{SINFO_JWT_SECRET}
			, alg => 'HS256'
		);
	}
	
	push @rest_params, $params{HEADERS};
	my $method = $params{METHOD};
	$rest_client_sinfo->$method(@rest_params);
	my $response_code = $rest_client_sinfo->responseCode();
	my @response_headers = $rest_client_sinfo->responseHeaders();
	my $response_content = $rest_client_sinfo->responseContent();
	my $response_headers_hash = {};

	$self->_logger()->debug("responseCode: ".$response_code." - ".status_message($response_code));
	$self->_logger()->trace("responseContent : ".$response_content);
	for my $head (@response_headers) {
		$head = lc $head;
		$self->_logger()->trace("responseHeaders.".$head." : ".$rest_client_sinfo->responseHeader($head));
		$response_headers_hash->{$head} = $rest_client_sinfo->responseHeader($head);
	}

	my $ret;
	if(is_success($response_code)) {
		if(defined $response_headers_hash->{'content-type'} && $response_headers_hash->{'content-type'} =~ /application\/json/) {
			$ret = eval{ decode_json $response_content };
			if($@) {
				# non è fatale in quanto server error
				$self->art()->last_error(__x("Unable to parse server response (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
				return undef;
			}
		} else {
			$ret = $response_content;
		}
	} elsif(is_client_error($response_code)) {
		# è fatale in quanto client error
		$self->art()->last_error(__x("Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
		${$params{IS_FATAL}} = 1;
		return undef;
	} elsif(is_server_error($response_code)) {
		# non è fatale in quanto server error
		$self->art()->last_error(__x("Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
		return undef;
	}

	return wantarray ? ($response_code, $response_content, $response_headers_hash) : $ret;
}

# la seguente funzione statica registra nel cache server la sessione master per l'utente sinfo
# accetta il parametro DEBUG => 0|1
sub register_master_sessions {
	my %params = @_;
	
	# verifico se ho già inserito le sessioni master nel cache server
	print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server\n"
		if($params{DEBUG});
	return
		if($_are_master_sessions_registered);
	
	my $CACHE = eval { SIRTI::Cache->new(DRIVER => "SIRTI::Cache::Driver::Memcached", SERVER => $ENV{ART_WS_CACHE_MEMCACHED_SERVER}); };
	if($@) {
		print STDERR "ERROR> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Impossibile istanziare la cache per salvare le sessioni master: $@";
		return;
	}
	
	# recupero le master session da gestire
	my @instances = split (',', $ENV{SERVICE_INSTANCES});
	
	for my $instance (@instances){
	
		# Registro nel cache server la sessione master dell'utente
		print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Registro nel cache server la sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}.")\n"
			if($params{DEBUG});
	
		eval { $CACHE->set($ENV{$instance."_USER_SID"}, { username => $ENV{$instance."_USER_USERNAME"}, password => $ENV{$instance."_USER_PASSWORD"} }) };
		if($@) {
			print STDERR "ERROR> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Impossibile salvare la sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}."): $@";
			return;
		}
		# La sessione master dell'utente è stata salvata
		print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: La sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}.") è stata salvata\n"
			if($params{DEBUG});
	}
	
	$_are_master_sessions_registered = 1;
	
	return 1
}

sub customers {
	my $self = shift;
	my %params = @_;

	return $self->{CUSTOMERS} if defined $self->{CUSTOMERS};

	$self->{CUSTOMERS} = eval {
		WPSOWORKS::Collection::System::CUSTOMER->new(
			ART => $self->art()
		)
	};

	$self->art()->last_error($@)
		&& return undef
			if $@;

	return $self->{CUSTOMERS};
}

sub get_ref_group_by_context{
	my $self = shift;
	my %params = @_;
		
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					context		=> { isa => 'SCALAR', list => ['OF_AB', 'OF_PNRR', 'CREATION_TIM_OLO', 'FASTWEB_MNT', 'WINDTRE_CREATION'] }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 0
			);
	
	return $params{context} eq 'OF_AB' ? 'USER' :
			$params{context} eq 'OF_PNRR' ? 'USER02' : 
			$params{context} eq 'FASTWEB_MNT' ? 'USER03' :
			$params{context} eq 'WINDTRE_CREATION' ? 'USER04' : 'USER01'
		   ;
}

sub contracts {
	my $self 		= shift;
	my %params 		= @_;
	my $customerId	= $params{customerId}; 
	
	return $self->{CONTRACTS}->{$customerId} if exists $self->{CONTRACTS}->{$customerId};
	
	my $collCustomer = WPSOWORKS::Collection::System::CUSTOMER->new(ART => $self->art());
	my $customers = $collCustomer->cerca(customerId=>$customerId);
	
	$self->{CONTRACTS}->{$customerId} = eval {
		WPSOWORKS::Collection::System::CONTRACT->new(
			ART 		=> $self->art()
			,CUSTOMER	=> $customers->[0] 
		)
	};
	
	$self->art()->last_error($@)
		&& return undef
			if $@;
	
	return $self->{CONTRACTS}->{$customerId};
}

sub get_field_service {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					customerId	=> { isa => 'SCALAR' },
					contractId	=> { isa => 'SCALAR' },
					workType	=> { isa => 'SCALAR' },
					maker		=> { isa => 'SCALAR' },
					details		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	return {} if $params{maker} ne 'Team';

	# se c'è un elemeno solo nella details lo cerco, se c'è più di uno cerco solo in funzione del workType
	my $prepare;
	my @bind_params;
	my $sql;
	my $res;
	if (scalar @{$params{details}} == 1){
		$sql = q{
            select  cfs.field_service
            from    works.cfg_field_service cfs
            where   cfs.enabled = 'Y'
            and     cfs."customerId" = ?
			and     cfs."contractId" = ?
			and     cfs."workType" = ?
			and 	cfs.tipo_dato_tecnico_attivita = ?
            and     cfs.valore_tdta = '1'
            and rownum < 2
        };
        $prepare = $self->art->_create_prepare(__PACKAGE__.'_cfs_p', $sql);
        if ( !defined $prepare ) {
            $self->art->last_error($self->art->_dbh->get_errormessage);
            return;
        }
        @bind_params = (
            $params{customerId},
			$params{contractId},
			$params{workType},
            $params{details}->[0]->{type},
        );

		$res = $prepare->fetch_minimalized(@bind_params);
	} 
	
	unless (defined $res) {
		# se c'è più di un details ne prende uno enabled
		$sql = q{
            select  cfs.field_service
            from    works.cfg_field_service cfs
            where   cfs.enabled = 'Y'
            and     cfs."customerId" = ?
			and     cfs."contractId" = ?
			and     cfs."workType" = ?
			and		cfs.tipo_dato_Tecnico_attivita is null
            and rownum < 2
        };
        $prepare = $self->art->_create_prepare(__PACKAGE__.'_cfs', $sql);
        if ( !defined $prepare ) {
            $self->art->last_error($self->art->_dbh->get_errormessage);
            return;
        }
        @bind_params = (
			$params{customerId},
			$params{contractId},
            $params{workType},
        );

		$res = $prepare->fetch_minimalized(@bind_params);
	}

	return $res ? {NAME => $res} : {};
}

sub get_team_info {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 userid	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = q{
		select  username userid,
			email,
			soc,
			cidsap,
			cognome,
			nome
		from    works.mv_anag_persone map
		where   map.username = ?
		and rownum < 2
	};
	my $prepare = $self->art->_create_prepare(__PACKAGE__.'_gtea', $sql);
	if ( !defined $prepare ) {
		$self->art->last_error($self->art->_dbh->get_errormessage);
		return;
	}
	my @bind_params = (
		$params{userid}
	);

	my $res = $prepare->fetchall_hashref(@bind_params);

	return defined $res->[0] ? $res->[0] : {};
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoworks = eval{
		WPSOWORKS->new(ART => $art);
	};
	
	if ($@) {
		die "WPSOWORKS error: $@";
	}
	
	my $team= $wpsoworks->get_field_service(
		customerId => 'TIM',
		contractId => 'CREATION',
		workType	=> 'Network',
		maker		=> 'Team',
		details => [
			{"restoration" => 1, "pippo" => 1}
		]
	);
	
	if( defined $team) {
		get_logger()->info("get_field_service: ".Dumper($team));
	} else {
		get_logger()->error("get_field_service Error: " . $art->last_error());
		die;
	}
	
	# my $is_fatal;
	# my @ret = $wpsoworks->invoke_sinfo_rest(
	# 	 RESOURCE => '/pippo'
	# 	,IS_FATAL => \$is_fatal
	# 	,METHOD => 'HEAD'
	# );
	
	# print Dumper \@ret;
	# print $art->last_error();

}

1;
