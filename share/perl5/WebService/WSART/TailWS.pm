package WebService::WSART::TailWS;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use HTTP::Status qw(:constants :is status_message);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use Dancer2 appname => 'API::ART::REST'; 

######## ROUTE / ###########

prefix '/';

any qr{.*} => sub {
	debug __PACKAGE__.": catchall";
	API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND);
};


1;
