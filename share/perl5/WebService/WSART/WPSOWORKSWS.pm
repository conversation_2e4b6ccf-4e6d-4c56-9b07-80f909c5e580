package WebService::WSART::WPSOWORKSWS;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use HTTP::Status qw(:constants :is status_message);
use Data::Dumper;

use File::Copy;
use MIME::Base32;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use Dancer2 appname => 'API::ART::REST'; 

use WPSOWORKS;
use WPSOWORKS::Sync::MACROTASKS;
use WPSOWORKS::Sync::CAVI;
use WPSOWORKS::Sync::NODI;
use WPSOWORKS::Collection::Activity::WZ_LC;
use WPSOWORKS::Collection::Activity::LAVORO;
use WPSOWORKS::Collection::Activity::AS_BUILT;

prefix defined $ENV{WPSOWORKSWS_WS_ROUTES_PREFIX} ? $ENV{WPSOWORKSWS_WS_ROUTES_PREFIX} : '/wpso/works';

############ HOOKS ################

### NOTA: l'hook viene eseguito sempre anche se fuori dal prefix

hook before => sub {
	
	debug __PACKAGE__.": sono in hook before";
	
	my $isOptionsNotPreflightAttach = 0;

	if (
		API::ART::REST::is_options() &&
		!API::ART::REST::is_preflight() &&
		(
			request->path() =~ m{/(.*)/contracts/(.*)/so/works/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/works/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/as-built/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/as-built/\d+/history/\d{14}/attachments/\d+}
		)
	){
		$isOptionsNotPreflightAttach = 1;
		my ($result, $error_message, $error_internal_message) = API::ART::REST::init_api_art();
		# se e' un errore mi blocco
		if (API::ART::REST::is_error($result)){
			info "Error:\n" .Dumper {
				UUID		=> vars->{uuid}
				,message	=> $error_message
				,internalMessage => $error_internal_message
			};
			status($result);
			halt(); # FIXME: bisognerebbe ritornare la struttura $error ma la funzione halt() non lo permette
		}
	}

	unless ($isOptionsNotPreflightAttach){
		return
			if API::ART::REST::is_options();
	}
	
	# se è la rotta di login non devo fare nulla
	return if API::ART::REST::is_login_route();
	
	my $wpsoworks;
	my $collWorkZone;
	my $collLavoro;
	my $collAsBuilt;
		
	eval{
		$wpsoworks = WPSOWORKS->new(ART => vars->{api_art});
		$collWorkZone = WPSOWORKS::Collection::Activity::WZ_LC->new( ART => vars->{api_art});
		$collLavoro = WPSOWORKS::Collection::Activity::LAVORO->new( ART => vars->{api_art});
		$collAsBuilt = WPSOWORKS::Collection::Activity::AS_BUILT->new( ART => vars->{api_art});
	};
	
	if ($@){
		status(HTTP_INTERNAL_SERVER_ERROR);
		$ENV{_WSART_UUID} = vars->{uuid};
		halt();
	}

	var wpsoworks		=> $wpsoworks;
	var collWorkZone	=> $collWorkZone;
	var collLavoro		=> $collLavoro;
	var collAsBuilt		=> $collAsBuilt;
	var "remap-blocks" => {
		WORKS			=> {
			ACTIVITY_TYPE_NAME => 'LAVORO'
		},
		SUBCONTRACT_WORKS	=> {
			ACTIVITY_TYPE_NAME => 'LAVORO',
			ACTIVITY_PROPERTIES_EQUAL => {
				"maker" => 'Subcontract'
			}
		},
		TEAM_WORKS		=> {
			ACTIVITY_TYPE_NAME => 'LAVORO',
			ACTIVITY_PROPERTIES_EQUAL => {
				"maker" => 'Team'
			}
		} 
	};

};

############ PRIVATE ################

# ritorna il rimappamento del profilo
sub _remap_profilo {
	
	my ($api, $profilo) = @_;
	
	my $remapped_info = {
		 "idUtente" => $profilo->id_utente()
		,"nomeUtente" => $profilo->nome_utente()
		,"nome" => $profilo->nome()
		,"cognome" => $profilo->cognome()
		,"email" => $profilo->email()
		,"gruppi" => $profilo->gruppi()
		,"telefoni" => $profilo->telefoni()
		,"ruoloAziendale" => $profilo->ruolo_aziendale()
	};
	
	return $remapped_info;

}

# ritorna il rimappamento della parte di profilo specifica del progetto
sub _remap_config_profilo {
	
	my ($api, $profilo) = @_;
	
	my $remapped_info = {
		"groups" => $profilo->gruppi()
		,"isAdmin" => $profilo->is_admin() ? $JSON::true : $JSON::false
		,"isAssistenteTecnicoCivile" => $profilo->is_assistente_tecnico_civile() ? $JSON::true : $JSON::false
		,"isProgettista" => $profilo->is_progettista() ? $JSON::true : $JSON::false
		,"isProgettistaCp" => $profilo->is_progettista_cp() ? $JSON::true : $JSON::false
		,"isCityManager" => $profilo->is_city_manager() ? $JSON::true : $JSON::false
		,"isAreaManager" => $profilo->is_area_manager() ? $JSON::true : $JSON::false
		,"isCoordinatoreAssistenteTecnicoCivile" => $profilo->is_coordinatore_at_civile() ? $JSON::true : $JSON::false
		,"isCoordinatoreAssistenteTecnicoGiunzioni" => $profilo->is_coordinatore_at_giunzioni() ? $JSON::true : $JSON::false
		,"isAssistenteTecnicoGiunzioni" => $profilo->is_assistente_tecnico_giunzioni() ? $JSON::true : $JSON::false
		,"isSupportoProgettazione" => $profilo->is_supporto_progettazione() ? $JSON::true : $JSON::false
		,"isReferentePermessiAreaPermessi" => $profilo->is_referente_permessi_ap() ? $JSON::true : $JSON::false
		,"isReferentePermessiBuilding" => $profilo->is_referente_permessi_building() ? $JSON::true : $JSON::false
		,"isContabile" => $profilo->is_contabile() ? $JSON::true : $JSON::false
		,"isPlanAndProgram" => $profilo->is_plan_and_program() ? $JSON::true : $JSON::false
		,"isQualityServiceAssurance" => $profilo->is_quality_service_assurance() ? $JSON::true : $JSON::false
		,"isFocalPoint" => $profilo->is_focal_point() ? $JSON::true : $JSON::false
		,"isCoordinatoreDelivery" => $profilo->is_coordinatore_delivery() ? $JSON::true : $JSON::false
		,"isAssistenteTecnico" => $profilo->is_assistente_tecnico() ? $JSON::true : $JSON::false
		,"isProjectManager" => $profilo->is_project_manager() ? $JSON::true : $JSON::false
		,"isRegionalManager" => $profilo->is_regional_manager() ? $JSON::true : $JSON::false
		,"isService" => $profilo->is_service() ? $JSON::true : $JSON::false
	};
	
	return $remapped_info;

}

sub _manage_attachments{
	
	my ($attachments) = @_;
	
	my $api = vars->{api_art};
	
	my $attachment_list = [];
	
	foreach my $att (@{$attachments}) {
		my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".API::ART::REST::get_sid()."_".$att;

		$api->last_error(__x("Not existent attachment"))
			&& return undef
				unless -f $filename && -f $filename.".idx";
		
		croak __x("Unable to open index file {file}.idx: {error}", file => $filename, error => $!)
			unless (open FD, $filename.".idx");
		
		my $idxjson = '';
		while(<FD>) {
			$idxjson .= $_;	
		}
		
		my $idx = eval{ from_json($idxjson) };
		croak __x("Unable to parse JSON idxfile {file}.idx: {error}", file => $filename, error => $@)
			if ($@);
		
		croak __x("Unable to copy file from {file} to {file1}: {error}", file => $filename, file1 => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}, error => $!)
			unless (copy($filename,$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}));
		
		my $attach_obj = {
			FILENAME => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}
		};
		$attach_obj->{'TITLE'} = $idx->{title} if exists $idx->{title};
		$attach_obj->{'DESCRIPTION'} = $idx->{description} if exists $idx->{description};
		$attach_obj->{'REVISION'} = $idx->{revision} if exists $idx->{revision};
		$attach_obj->{'REF_DATE'} = $idx->{refDate} if exists $idx->{refDate};
		$attach_obj->{'DOC_TYPE'} = $idx->{docType} if exists $idx->{docType};
		
		push @{$attachment_list}, $attach_obj;
	}
	
	return $attachment_list;
	
}

sub _get_lavoro{
	
	my ($customerId, $contractId, $workId) = @_;
	
	my $api = vars->{api_art};
	
	my $lavori = vars->{collLavoro}->cerca(
		 customerId => $customerId
		,contractId => $contractId
		,workId => [$workId]
	);
	croak $api->last_error()
		unless defined $lavori;
	
	$api->last_error(__("Work not found!"))
		unless scalar @{$lavori};

	return $lavori->[0];
}

sub _get_as_built{
	
	my ($customerId, $contractId, $asBuiltId) = @_;
	
	my $api = vars->{api_art};
	
	my $asbuilts = vars->{collAsBuilt}->cerca(
		 customerId => $customerId
		,contractId => $contractId
		,asBuiltId => [$asBuiltId]
	);
	croak $api->last_error()
		unless defined $asbuilts;
	
	$api->last_error(__("As Built not found!"))
		unless scalar @{$asbuilts};

	return $asbuilts->[0];
}

####### ROUTES ###########

######## ROUTE /ping ###########

options '/ping' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_ping_get {

	get '/ping' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		return API::ART::REST::send_ok(CODE => HTTP_NO_CONTENT);
	};
	
}

route_ping_get();

######## ROUTE /profilo ###########

options '/profilo' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_profilo_get {

	get '/profilo' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		return API::ART::REST::send_ok(MSG => _remap_profilo($api, $wpsoworks->profilo()));
	};
}

route_profilo_get();

######## ROUTE /config ###########

options '/config' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_config_get {

	get '/config' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $ret = {};
		
		$ret->{userProfile} = _remap_config_profilo($api, $wpsoworks->profilo());
		$ret->{profile} = $ret->{userProfile}; # FIXME: chiave da deprecare
		
		return API::ART::REST::send_ok(MSG => $ret);
	};

}

route_config_get();

######## ROUTE /:customerId/contracts/:contractId/so/works ###########

options '/:customerId/contracts/:contractId/so/works' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_post {
	post '/:customerId/contracts/:contractId/so/works' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
	
		my %query_params = defined request->params('query') ? request->params('query') : ();
	
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		#metto default per noWait
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} can value only {values}", param => 'noWait', values => '0|1'))
			if (defined $query_params{noWait} && $query_params{noWait} !~/^(0|1)$/);
		$query_params{noWait} = 0
			unless exists $query_params{noWait};

		my $coll = vars->{collLavoro};
		
		my %create_params = %body;
		
		$create_params{"customerId"}	= param('customerId');
		$create_params{"contractId"}	= param('contractId');
		
		# NB: i campi popId e ringId sono in base32
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $lavori= $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $lavori );
		
		my @results = map {$_->id()} @{$lavori};
		
		API::ART::REST::do_api_save( $api );
		
		# se siamo nella gestion OF metto lo sleep
		if (
			!$query_params{noWait}
			&&
			$body{workType} =~/^(Network|NetworkFibercop|ROE|PreeMaintenance|CorrMaintenance|ExtrMaintenance|PTE|PreeMaintenance01|ExtrMaintenance01)$/
		){
			# imposto uno sleep dinamico dopo il save e prima di dare la response per permettere al demone
			# su core di recepire la creazione dell'attività.
			my $sleepTime = 5;
			$sleepTime += ((scalar @results)-1)/2;
			sleep($sleepTime);
		}
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => \@results);

	};
}
route_customerId_contracts_contractId_so_works_post();

sub route_customerId_contracts_contractId_so_works_get {
	get '/:customerId/contracts/:contractId/so/works' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{type_equal} = 'LAVORO';
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{showOnlyWithVisibility} = 1;
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};
}
route_customerId_contracts_contractId_so_works_get();

######## ROUTE /:customerId/contracts/:contractId/so/works/fibercop ###########

options '/:customerId/contracts/:contractId/so/works/fibercop' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_fibercop_get {
	get '/:customerId/contracts/:contractId/so/works/fibercop' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();

		for my $p ('sp_workType_equal', 'ap_ref00_equal'){
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {param}", param => $p))
				unless defined ($query_params{$p});
		}

		my $works_list = {
			PTE => [
				'survey',
				'infrastructure',
				'laying',
				'junction',
				'updateDatabaseF1',
				'updateDatabaseF2',
				'testApp',
				'testOTDR',
				'restoration',
				'planning',
			],
			NetworkFibercop => [
				'laying',
				'civil',
				'junction',
				'restoration',
			]
		};

		my $found_wl;

		for my $wl (@{$works_list->{$query_params{sp_workType_equal}}}){
			if (exists $query_params{'sp_'.$wl.'_equal'}){
				$found_wl = $wl;
				last;
			}
		}

		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Unable to find work"))
			unless (defined $found_wl);

		if (defined $query_params{sp_assetId_in} && !ref $query_params{sp_assetId_in}){
			$query_params{sp_assetId_in} = [$query_params{sp_assetId_in}];
		}

		my $sql = '
			select pt.id_attivita "id",
				pt."maker",
				pt."teamId",
				pt."teamName",
				pt."subContractCode",
				pt."subContractName",
				att.nome_tipo_Attivita "type",
				att.in_stato_finale "isClosed",
				att.stato_corrente "status",
				dts3.valore "assetId"
			from pt_lavoro pt
				join v_attivita att on att.id = pt.id_attivita and att.nome_Tipo_Attivita = \'LAVORO\'
				join permission_sistemi ps on ps.id_sistema = att.id_sistema and ps.id_gruppo_abilitato in ('.join (',', map{'?'} @{$api->user->groups()}).')
				join v_dt_sistemi dts on dts.id_Sistema = att.id_sistema and dts.nome = \'workType\' and dts.valore = ?
				join v_dt_sistemi dts2 on dts2.id_Sistema = att.id_sistema and dts2.nome = ? and dts2.valore = ?
				join v_dt_sistemi dts3 on dts3.id_Sistema = att.id_sistema and dts3.nome = \'assetId\'
			where pt."customerId" = ?
				and pt."contractId" = ?
				and pt."ref00" = ?
			'.(defined $query_params{sp_assetId_in} ? ' and dts3.valore in ('.join (',', map {'?'} @{$query_params{sp_assetId_in}}).')' : '').'
			order by pt.id_attivita desc
		';

		my $prepare_name = "WPSOWORKS_fiber_f_".(scalar @{$api->user->groups()})."_".(defined $query_params{sp_assetId_in} ? scalar @{$query_params{sp_assetId_in}} : 0);

		my @bind_params = (
			@{$api->user->groups()},
			$query_params{'sp_workType_equal'},
			$found_wl,
			$query_params{'sp_'.$found_wl.'_equal'},
			params->{'customerId'},
			params->{'contractId'},
			$query_params{ap_ref00_equal},
		);

		if (defined $query_params{sp_assetId_in}){
			push @bind_params, @{$query_params{sp_assetId_in}}
		}

		my $prepare = $api->_create_prepare($prepare_name, $sql);

		my $result = $prepare->fetchall_hashref(@bind_params);

		my $ret = [];

		for my $r (@{$result}){
			my @found = grep {$r->{id} eq $_->{id}} @{$ret};
			if (scalar @found){
				push @{$found[0]->{system}->{properties}->{assetId}}, $r->{assetId};
				next;
			}
			my $tmp = {
				id => $r->{id},
				info => {
					type => $r->{type},
					isClosed => $r->{isClosed} eq 'N' ? $JSON::false : $JSON::true,
					status => $r->{status},
				},
				properties => {
					maker => $r->{maker},
					subContractCode => $r->{subContractCode},
					subContractName => $r->{subContractName},
					teamName => $r->{teamName},
					teamId => $r->{teamId},
				},
				system => {
					properties => {
						assetId => [$r->{assetId}]
					}
				}
			};
			if ($tmp->{info}->{isClosed}){
				$tmp->{availableActions} = [];
			} else {
				my $activity = API::ART::Activity::Factory->new(ART => $api, ID => $r->{id});

				my $dump = $activity->dump(
					EXCLUDE_INFO => 1,
					EXCLUDE_HISTORY => 1,
					EXCLUDE_PROPERTIES => 1,
					EXCLUDE_AVAILABLE_ACTIONS => 0,
					SYSTEM => {
						EXCLUDE_ALL => 1
					}
				);

				$tmp->{availableActions} = $dump->{availableActions};
			}
			push @{$ret}, $tmp;
		}

		return API::ART::REST::send_ok(CODE => HTTP_OK, MSG => $ret);
		
	};
}
route_customerId_contracts_contractId_so_works_fibercop_get();


######## ROUTE /:customerId/contracts/:contractId/so/works/dashboards/:blockId ###########

options '/:customerId/contracts/:contractId/so/works/dashboards/:blockId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_dashboards_blockId_get {

	get '/:customerId/contracts/:contractId/so/works/dashboards/:blockId' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		if (exists $block_object->{ACTIVITY_PROPERTIES_EQUAL}){
			for my $k (keys %{$block_object->{ACTIVITY_PROPERTIES_EQUAL}}){
				$findParams{"ap_".$k."_equal"} = $block_object->{ACTIVITY_PROPERTIES_EQUAL}->{$k};
			}
		}
		
		# default ordinamento non sovrascrivibile al momento
		$findParams{sort} = "status";
		
		my %uri_params = (
			OBJECT_TYPE => 'activities'
			,TYPE => $block_object->{ACTIVITY_TYPE_NAME}
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_works_dashboards_blockId_get();

######## ROUTE /:customerId/contracts/:contractId/so/works/dashboards/:blockId/contexts/:contextId/data ###########

options '/:customerId/contracts/:contractId/so/works/dashboards/:blockId/contexts/:contextId/data' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_dashboards_blockId_contexts_contextId_data_get {

	get '/:customerId/contracts/:contractId/so/works/dashboards/:blockId/contexts/:contextId/data' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		my $context_id = params->{'contextId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		if (exists $block_object->{ACTIVITY_PROPERTIES_EQUAL}){
			for my $k (keys %{$block_object->{ACTIVITY_PROPERTIES_EQUAL}}){
				$findParams{"ap_".$k."_equal"} = $block_object->{ACTIVITY_PROPERTIES_EQUAL}->{$k};
			}
		}
			
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> $block_object->{ACTIVITY_TYPE_NAME}
			,CONTEXT	=> $context_id
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_works_dashboards_blockId_contexts_contextId_data_get();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId ###########

options '/:customerId/contracts/:contractId/so/works/:workId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_get {

	get '/:customerId/contracts/:contractId/so/works/:workId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};

};

route_customerId_contracts_contractId_so_works_workId_get();

sub route_customerId_contracts_contractId_so_works_workId_put {

	put '/:customerId/contracts/:contractId/so/works/:workId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		my $aggiorna_lavoro = $lavoro->update(%body);
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined $aggiorna_lavoro;
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_NO_CONTENT);
	
	};

};

route_customerId_contracts_contractId_so_works_workId_put();

## activityProperties: Queste servono per la nuova gestione con la direttiva dell'activity
## activity-properties: vecchia gestione
######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/activityProperties ###########

options '/:customerId/contracts/:contractId/so/works/:workId/activityProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_activityProperties_get {

	get '/:customerId/contracts/:contractId/so/works/:workId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_activityProperties_get();

sub route_customerId_contracts_contractId_so_works_workId_activityProperties_put{

	put '/:customerId/contracts/:contractId/so/works/:workId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_activityProperties_put(BODY => \%body, URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_activityProperties_put();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/activity-properties ###########

options '/:customerId/contracts/:contractId/so/works/:workId/activity-properties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_activity_properties_get {
	
	get '/:customerId/contracts/:contractId/so/works/:workId/activity-properties' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};
};

route_customerId_contracts_contractId_so_works_workId_activity_properties_get;


######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/attachments ###########

sub route_customerId_contracts_contractId_so_works_workId_attachments_options {
	options '/:customerId/contracts/:contractId/so/works/:workId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_attachments_options();


sub route_customerId_contracts_contractId_so_works_workId_attachments_get {
	
	get '/:customerId/contracts/:contractId/so/works/:workId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};
};

route_customerId_contracts_contractId_so_works_workId_attachments_get;

sub route_customerId_contracts_contractId_so_works_workId_attachments_post {
	post '/:customerId/contracts/:contractId/so/works/:workId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_attachments_post();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/canDoAction/:ACTION ###########

options '/:customerId/contracts/:contractId/so/works/:workId/canDoAction/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_canDoAction_ACTION_get {

	get '/:customerId/contracts/:contractId/so/works/:workId/canDoAction/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_canDoAction_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id(), ACTION => params->{'ACTION'}});
	};

}

route_customerId_contracts_contractId_so_works_workId_canDoAction_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/destUsers/:ACTION ###########

options '/:customerId/contracts/:contractId/so/works/:workId/destUsers/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_destUsers_ACTION_get {

	get '/:customerId/contracts/:contractId/so/works/:workId/destUsers/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_destUsers_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id(), ACTION => param('ACTION')});
	};

}

route_customerId_contracts_contractId_so_works_workId_destUsers_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/hierarchy ###########

options '/:customerId/contracts/:contractId/so/works/:workId/hierarchy' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_hierarchy_get {

	get '/:customerId/contracts/:contractId/so/works/:workId/hierarchy' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_hierarchy_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_hierarchy_get();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/history ###########

options '/:customerId/contracts/:contractId/so/works/:workId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_history_post {
	
	post '/:customerId/contracts/:contractId/so/works/:workId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $lavoro->id()});
	};
};

route_customerId_contracts_contractId_so_works_workId_history_post();

sub route_customerId_contracts_contractId_so_works_workId_history_get {
	
	get '/:customerId/contracts/:contractId/so/works/:workId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id()});
	};
};

route_customerId_contracts_contractId_so_works_workId_history_get;

######## ROUTE /:customerId/contracts/:contractId/so/works/:workId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/works/:workId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $lavoro ? $lavoro->id() : param('workId')), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/works/:workId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
};
route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/works/:workId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_works_workId_history_transitionId_attachments_sequence_delete();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/lock ###########

options '/:customerId/contracts/:contractId/so/works/:workId/lock' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'DELETE', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_lock_del {

	del '/:customerId/contracts/:contractId/so/works/:workId/lock' => sub {
		
		my $api = vars->{api_art};
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_lock_del(URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_lock_del();

sub route_customerId_contracts_contractId_so_works_workId_lock_put{

	put '/:customerId/contracts/:contractId/so/works/:workId/lock' => sub {
		
		my $api = vars->{api_art};
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_activities_ID_lock_put(URI_PARAMS => {ID => $lavoro->id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_lock_put();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/systemProperties ###########

options '/:customerId/contracts/:contractId/so/works/:workId/systemProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_systemProperties_get {

	get '/:customerId/contracts/:contractId/so/works/:workId/systemProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		return API::ART::REST::handler_systems_ID_systemProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $lavoro->system_id()});
	};

}

route_customerId_contracts_contractId_so_works_workId_systemProperties_get();

######### ROUTE /:customerId/contracts/:contractId/so/works/:workId/macro-tasks ###########

options '/:customerId/contracts/:contractId/so/works/:workId/macro-tasks' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_works_workId_macro_tasks_get {
	
	get '/:customerId/contracts/:contractId/so/works/:workId/macro-tasks' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il workId richiesto appartenga effettivamente a customerId e contractId
		my $lavoro = eval{_get_lavoro(param('customerId'), param('contractId'), param('workId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $lavoro;
		
		my $report = $lavoro->get_macro_tasks_summary();
	
		unless (defined $report) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
		
		return API::ART::REST::send_file_ok($report, system_path => 1);
	};
};

route_customerId_contracts_contractId_so_works_workId_macro_tasks_get;

######## ROUTE /:customerId/contracts/:contractId/so/as-built ###########

options '/:customerId/contracts/:contractId/so/as-built' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_as_built_post {
	post '/:customerId/contracts/:contractId/so/as-built' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $coll = vars->{collAsBuilt};
		
		my %create_params = %body;
		
		$create_params{"customerId"}	= param('customerId');
		$create_params{"contractId"}	= param('contractId');
		
		# NB: i campi popId e ringId sono in base32
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $as_built= $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $as_built );
		
		my $remap_results = API::ART::REST::remap_activity($api,$as_built);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);
	};
}
route_customerId_contracts_contractId_so_as_built_post();

sub route_customerId_contracts_contractId_so_as_built_get {
	get '/:customerId/contracts/:contractId/so/as-built' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{type_equal} = 'AS_BUILT';
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{showOnlyWithVisibility} = 1;
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};
}
route_customerId_contracts_contractId_so_as_built_get();

######### ROUTE /:customerId/contracts/:contractId/so/as-built/:asBuiltId ###########

options '/:customerId/contracts/:contractId/so/as-built/:asBuiltId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_get {

	get '/:customerId/contracts/:contractId/so/as-built/:asBuiltId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id()});
		
	};

};

route_customerId_contracts_contractId_so_as_built_asBuiltId_get();

######### ROUTE /:customerId/contracts/:contractId/so/as-built/:asBuiltId/attachments ###########

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_options {
	options '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id()});
	};

}

route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_options();

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_get {
	
	get '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id()});
	};
};

route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_get;

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_post {
	post '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $asBuilt->id()});
	};

}

route_customerId_contracts_contractId_so_as_built_asBuiltId_attachments_post();

######### ROUTE /:customerId/contracts/:contractId/so/as-built/:asBuiltId/activity-properties ###########

options '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/activity-properties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_activity_properties_get {
	
	get '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/activity-properties' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id()});
	};
};

route_customerId_contracts_contractId_so_as_built_asBuiltId_activity_properties_get;

######### ROUTE /:customerId/contracts/:contractId/so/as-built/:asBuiltId/history ###########

options '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_history_post {
	
	post '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $asBuilt->id()});
	};
};

route_customerId_contracts_contractId_so_as_built_asBuiltId_history_post();

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_history_get {
	
	get '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id()});
	};
};

route_customerId_contracts_contractId_so_as_built_asBuiltId_history_get;

######## ROUTE /:customerId/contracts/:contractId/so/as-built/:asBuiltId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $asBuilt ? $asBuilt->id() : param('asBuiltId')), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
};
route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/as-built/:asBuiltId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# verifico che il asBuiltId richiesto appartenga effettivamente a customerId e contractId
		my $asBuilt = eval{_get_as_built(param('customerId'), param('contractId'), param('asBuiltId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $asBuilt;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $asBuilt->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_as_built_asBuiltId_history_transitionId_attachments_sequence_delete();

######## ROUTE /:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-report ###########

options '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-report' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_cables_report_get {
	
	get '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-report' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{format} = 'json' unless defined $query_params{format};
		
		my ($pop, $ring) = (decode_base32(param('popId')), decode_base32(param('ringId')));
		debug "popId: ".param('popId').", pop: ".$pop;
		debug "ringId: ".param('ringId').", ring: ".$ring;
		
		my $cr;
		eval {
			$cr = WPSOWORKS::Sync::CAVI->new(
				 WPSOWORKS => $wpsoworks
				,CUSTOMER_ID => param('customerId')
				,CONTRACT_ID => param('contractId')
			)
		};
		
		if ($@) {
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@);
		}
		
		my $report = $cr->progress_report(
			 "cadastralCode"	=> param('cadastralCode')
			,"pop"				=> $pop
			,"ring"				=> $ring
			,OUTPUT_FORMAT		=> $query_params{format}
		);
	
		unless (defined $report) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
			
		debug "Report: $report";
	
		API::ART::REST::remove_in_hook_after( $report );

		# FIXME: recuperare last modified NON su base progetto
		# my $sinfo_last_modified = $cr->get_sinfo_modified_dates(PROJECT_ID => param('projectId'), LIMIT => 1);
		# my $last_modified = scalar @{$sinfo_last_modified} ? $sinfo_last_modified->[0] : '19700101000000';
		# headers 'last-modified' => $api->get_rfc7232_date_from_date($last_modified);
		
		# FIXME: correggere nome file
		API::ART::REST::send_file_ok($report, system_path => 1, filename => 'cables_report_'.param('customerId').'_'.param('contractId').'_'.$api->get_sysdate().'.'.$query_params{format});
		
	};
}
route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_cables_report_get();

######## ROUTE /:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-details ###########

options '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-details' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_cables_details_get {
	
	get '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/cables-details' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{format} = 'json' unless defined $query_params{format};
		
		my ($pop, $ring) = (decode_base32(param('popId')), decode_base32(param('ringId')));
		debug "popId: ".param('popId').", pop: ".$pop;
		debug "ringId: ".param('ringId').", ring: ".$ring;
		
		my $cr;
		eval {
			$cr = WPSOWORKS::Sync::CAVI->new(
				 WPSOWORKS => $wpsoworks
				,CUSTOMER_ID => param('customerId')
				,CONTRACT_ID => param('contractId')
			)
		};
		
		if ($@) {
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@);
		}
		
		my %p = (
			 "cadastralCode"	=> param('cadastralCode')
			,"pop"				=> $pop
			,"ring"				=> $ring
			,OUTPUT_FORMAT		=> $query_params{format}
		);
		
		if (defined $query_params{f}) {
			if (ref $query_params{f}){
				$p{fromNetworkElementId} = $query_params{f};
			} else {
				$p{fromNetworkElementId} = [$query_params{f}];
			}
		}

		my $report = $cr->cables_details(%p);
	
		unless (defined $report) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
			
		debug "Report: $report";
	
		API::ART::REST::remove_in_hook_after( $report );

		# FIXME: recuperare last modified NON su base progetto
		# my $sinfo_last_modified = $cr->get_sinfo_modified_dates(PROJECT_ID => param('projectId'), LIMIT => 1);
		# my $last_modified = scalar @{$sinfo_last_modified} ? $sinfo_last_modified->[0] : '19700101000000';
		# headers 'last-modified' => $api->get_rfc7232_date_from_date($last_modified);
		
		# FIXME: correggere nome file
		API::ART::REST::send_file_ok($report, system_path => 1, filename => 'cables_details_'.param('customerId').'_'.param('contractId').'_'.$api->get_sysdate().'.'.$query_params{format});
		
	};
}
route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_cables_details_get();

######## ROUTE /:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-report ###########

options '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-report' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_nodes_report_get {
	
	get '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-report' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{format} = 'json' unless defined $query_params{format};
		
		my ($pop, $ring) = (decode_base32(param('popId')), decode_base32(param('ringId')));
		debug "popId: ".param('popId').", pop: ".$pop;
		debug "ringId: ".param('ringId').", ring: ".$ring;
		
		my $cr;
		eval {
			$cr = WPSOWORKS::Sync::NODI->new(
				 WPSOWORKS => $wpsoworks
				,CUSTOMER_ID => param('customerId')
				,CONTRACT_ID => param('contractId')
			)
		};
		
		if ($@) {
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@);
		}
		
		my $report = $cr->progress_report(
			 "cadastralCode"	=> param('cadastralCode')
			,"pop"				=> $pop
			,"ring"				=> $ring
			,OUTPUT_FORMAT		=> $query_params{format}
		);
	
		unless (defined $report) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
			
		debug "Report: $report";
	
		API::ART::REST::remove_in_hook_after( $report );

		# FIXME: recuperare last modified NON su base progetto
		# my $sinfo_last_modified = $cr->get_sinfo_modified_dates(PROJECT_ID => param('projectId'), LIMIT => 1);
		# my $last_modified = scalar @{$sinfo_last_modified} ? $sinfo_last_modified->[0] : '19700101000000';
		# headers 'last-modified' => $api->get_rfc7232_date_from_date($last_modified);
		
		# FIXME: correggere nome file
		API::ART::REST::send_file_ok($report, system_path => 1, filename => 'nodes_report_'.param('customerId').'_'.param('contractId').'_'.$api->get_sysdate().'.'.$query_params{format});
		
	};
}
route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_nodes_report_get();

######## ROUTE /:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-details ###########

options '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-details' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_nodes_details_get {
	
	get '/:customerId/contracts/:contractId/so/cities/:cadastralCode/pops/:popId/rings/:ringId/nodes-details' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{format} = 'json' unless defined $query_params{format};
		
		my ($pop, $ring) = (decode_base32(param('popId')), decode_base32(param('ringId')));
		debug "popId: ".param('popId').", pop: ".$pop;
		debug "ringId: ".param('ringId').", ring: ".$ring;
		
		my $cr;
		eval {
			$cr = WPSOWORKS::Sync::NODI->new(
				 WPSOWORKS => $wpsoworks
				,CUSTOMER_ID => param('customerId')
				,CONTRACT_ID => param('contractId')
			)
		};
		
		if ($@) {
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@);
		}
		
		my %p = (
			 "cadastralCode"	=> param('cadastralCode')
			,"pop"				=> $pop
			,"ring"				=> $ring
			,OUTPUT_FORMAT		=> $query_params{format}
		);
		
		if (defined $query_params{n}) {
			if (ref $query_params{n}){
				$p{networkElementId} = $query_params{n};
			} else {
				$p{networkElementId} = [$query_params{n}];
			}
		}

		my $report = $cr->nodes_details(%p);
	
		unless (defined $report) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
			
		debug "Report: $report";
	
		API::ART::REST::remove_in_hook_after( $report );

		# FIXME: recuperare last modified NON su base progetto
		# my $sinfo_last_modified = $cr->get_sinfo_modified_dates(PROJECT_ID => param('projectId'), LIMIT => 1);
		# my $last_modified = scalar @{$sinfo_last_modified} ? $sinfo_last_modified->[0] : '19700101000000';
		# headers 'last-modified' => $api->get_rfc7232_date_from_date($last_modified);
		
		# FIXME: correggere nome file
		API::ART::REST::send_file_ok($report, system_path => 1, filename => 'nodes_details_'.param('customerId').'_'.param('contractId').'_'.$api->get_sysdate().'.'.$query_params{format});
		
	};
}
route_customerId_contracts_contractId_so_cities_cadastralCode_pops_popId_rings_ringId_nodes_details_get();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/work-zones ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/work-zones' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

#sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_work_zones_get {
#
#	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/work-zones' => sub {
#		
#		my $api = vars->{api_art};
#		
#		my $wpsoworks = vars->{wpsoworks};
#		
#		my %query_params = defined request->params('query') ? request->params('query') : ();
#		
#		my $ap = eval{_get_area_permesso(param('customerId'), param('contractId'), param('projectId'), param('permitsAreaId'));};
#		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
#			if $@;
#		
#		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
#			unless defined $ap;
#		
#		my $wz_systems = $ap->get_children(SYSTEM_TYPE_NAME => ['WZ']); 
#		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $api->last_error())
#			unless defined $wz_systems;
#		
#		$query_params{type_equal} = 'WZ_LC';
#		# ottimizzazione: Bisogna implementare in API::ART e API::ART::REST i filtri sulle gerarchie dei sistemi
#		$query_params{systemId} = [map {$_->id()} @{$wz_systems}];
#		
#		# imposto filtro in modo che recuperi solo le attività legate al progetto
#		$query_params{sp_customerId_equal} = param('customerId');
#		$query_params{sp_contractId_equal} = param('contractId');
#		$query_params{sp_projectId_equal} = param('projectId');
#		$query_params{sp_permitsAreaId_equal} = param('permitsAreaId');
#		
#		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
#		
#	};
#
#}
#
#route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_work_zones_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_work_zones_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/work-zones' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $coll = vars->{collWorkZone};
		
		my %create_params = (
			"customerId"		=> param('customerId')
			,"contractId"		=> param('contractId')
			,"projectId"		=> param('projectId')
			,"permitsAreaId"	=> param('permitsAreaId')
			,"workZoneId"		=> $body{'workZoneId'}
			,"name"				=> $body{'name'}
			,"requestDate"		=> $body{'requestDate'}
			,"polygon"			=> $body{'polygon'}
			,"centralPoint"		=> $body{'centralPoint'}
			,"username"			=> $body{'username'}
		);
		
		$create_params{"note"} = $body{'note'} if defined $body{'note'};
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $wz = $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $wz );
	
		my $remap_results = API::ART::REST::remap_activity($api,$wz);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);

	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_work_zones_post();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/macro-tasks/report ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/macro-tasks/report' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_macro_woks_report_get {

	get '/:customerId/contracts/:contractId/so/projects/:projectId/macro-tasks/report' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		$query_params{format} = 'json' unless defined $query_params{format};
		
		my $mt;
		eval {
			$mt = WPSOWORKS::Sync::MACROTASKS->new(
				 WPSOWORKS => $wpsoworks
				,CUSTOMER_ID => param('customerId')
				,CONTRACT_ID => param('contractId')
			)
		};
		
		if ($@) {
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@);
		}
		
		my $sinfo_last_modified = $mt->get_sinfo_modified_dates(PROJECT_ID => param('projectId'), LIMIT => 1);
		
		my $details_by_project = $mt->details_by_project(
			 PROJECT_ID 	=> param('projectId')
			,OUTPUT_FORMAT	=> $query_params{format}
		);
	
		unless (defined $details_by_project) {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error());
		}
			
		debug "Report: $details_by_project";
	
		API::ART::REST::remove_in_hook_after( $details_by_project );
		
		my $last_modified = scalar @{$sinfo_last_modified} ? $sinfo_last_modified->[0] : '19700101000000';
		
		headers 'last-modified' => $api->get_rfc7232_date_from_date($last_modified);
		
		API::ART::REST::send_file_ok($details_by_project, system_path => 1, filename => 'macrotasks_details_by_project_'.param('customerId').'_'.param('contractId').'_'.param('projectId').(scalar @{$sinfo_last_modified} ? '_'.$sinfo_last_modified->[0] : '').'.'.$query_params{format});
		
	};

}
route_customerId_contracts_contractId_so_projects_projectId_macro_woks_report_get();

1;
