################################################################################
#
# WebService::Rendicontazione::Utils
#
################################################################################

package WebService::Rendicontazione::Utils;

use strict;
use warnings;

use Carp qw(verbose);
use Data::Dumper;
$Data::Dumper::Terse = 1;
use Digest::MD5 qw(md5_hex);
use JSON;
use Array::Utils qw(:all);
use HTTP::Status qw(:constants :is status_message);

sub send_ok {

	my $Response = shift;
	my %params = @_;
	
	$ENV{_RENDICONTAZIONE_UUID} = md5_hex(rand());
	
	$params{CODE} = HTTP_OK unless $params{CODE};
	$Response->{Charset} = 'utf-8';
	$Response->AddHeader("content-type", "application/json");
	$Response->{Status} = $params{CODE};
	print to_json $params{MSG}
		if defined $params{MSG};
	$Response->End();

}

sub send_ko {
	
	my $Response = shift;
	my %params = @_;
	
	$ENV{_RENDICONTAZIONE_UUID} = md5_hex(rand());
	
	$params{CODE} = HTTP_INTERNAL_SERVER_ERROR unless $params{CODE};
	$params{ERROR_MSG} = status_message($params{CODE}) unless $params{ERROR_MSG};
	$params{INTERNAL_ERROR_MSG} = $params{INTERNAL_ERROR_MSG} ? $params{INTERNAL_ERROR_MSG} : $params{ERROR_MSG};
	my $return = {
		 UUID		=> $ENV{_RENDICONTAZIONE_UUID}
		,message	=> $params{ERROR_MSG}
		,internalMessage => $params{INTERNAL_ERROR_MSG}
	};
	print STDERR "Error:\n" . Dumper $return;
	delete $return->{internalMessage}
		if $ENV{ART_ENVIRONMENT} =~ /^production/;

	$ENV{WS_ERROR_JSON} = to_json $return;
	$Response->{Status} = $params{CODE};
	$Response->End();

}

sub is_options { my $Request = shift; return lc($Request->{Method}) eq 'options' }

sub handle_cors_request {
	
	# ref http://www.html5rocks.com/en/tutorials/cors/
	my $Request = shift;
	my $Response = shift;
	
	my %params = @_;
	
	my $origin = _is_cors();
	
	if(defined $origin) {

		my $acrm = lc($ENV{HTTP_ACCESS_CONTROL_REQUEST_METHOD});

		if ( is_options($Request) && $acrm ) {
			
			# e' una preflight
			
			my $methods = $params{METHODS};
			
			if(scalar grep { lc($_) eq $acrm } @$methods) {
			
				my %headers = (
					 'Access-Control-Allow-Origin' => $origin
					,'Access-Control-Allow-Methods' => uc(join(', ', @$methods))
					,'Access-Control-Allow-Headers' => 'accept, content-type, content-length'
					,'Access-Control-Allow-Credentials' => 'true'
				);
				
				for my $h ( keys %headers ) {
					$Response->AddHeader($h, $headers{$h});
				}
				
			}
			
		} else {
			
			my %headers = (
				 'Access-Control-Allow-Origin' => $origin
				,'Access-Control-Expose-Headers' => 'accept, content-type, content-length'
				,'Access-Control-Allow-Credentials' => 'true'
			);
			
			for my $h ( keys %headers ) {
				$Response->AddHeader($h, $headers{$h});
			}
			
		}
	}
}

sub _is_cors { return defined $ENV{HTTP_ORIGIN} ? $ENV{HTTP_ORIGIN} : undef }

1;
