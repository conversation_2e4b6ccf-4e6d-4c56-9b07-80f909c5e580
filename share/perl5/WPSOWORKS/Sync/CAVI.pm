package WPSOWORKS::Sync::CAVI;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use SIRTI::Reports;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::System::CAVO;
use WPSOWORKS::Collection::Activity::LAVORO;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base "WPSOWORKS::Sync";

sub new {
	my $this   = shift;
	my $class  = ref($this) || $this;

	my $self = $class->SUPER::new(@_, SYNC_TYPE => 'CAVI');

	$self->_prepare()->{INSERT_CAVO} = $self->_art()->_create_prepare(__PACKAGE__.'_INSERT_CAVO', qq/
		INSERT
		INTO CAVI
		  (
		    "cableId",
		    "cableName",
		    "cableType",
		    "potential",
		    "unitOfMeasure",
		    "fromNetworkElementId",
		    "fromNetworkElementName",
		    "fromNetworkElementType",
		    "fromNetworkElementGeoLocation",
		    "toNetworkElementId",
		    "toNetworkElementName",
		    "toNetworkElementType",
		    "toNetworkElementGeoLocation",
		    "UI",
		    "horizontalLength",
		    "verticalLength",
		    "fromStockLength",
		    "toStockLength",
		    "plannedUndergroundLength",
		    "plannedAerialLength",
		    "plannedFacadeLength",
		    "status",
		    "doneUndergroundLength",
		    "doneAerialLength",
		    "doneFacadeLength",
		    "doneHandmaidLength",
		    "estimatedDuration",
		    "runId",
		    "historicizingRunId"
		  )
		  VALUES
		  (
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    null
		  )
	/);
	$self->_prepare()->{GET_ESTIMATED_DURATION} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION', qq{
		select
		  round((? / "aerialMtsByManHour" + ? / "facadeMtsByManHour" + ? / "undergroundMtsByManHour") * 60) "estimatedDuration"
		from
		  v_cavi_tls
		where
		  "cableType" = ?
	});
	$self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TMP_STD_MAX', qq{
		select "manMinutes" 
		from CONF_CAVI_TMP_STD_MAX
		where "insertDate" = (
			select max("insertDate") from CONF_CAVI_TMP_STD_MAX
		)
	});

	return $self;
}

# restituisce il tempo stimato per la realizzazione
# cableId => id del cavo
# plannedAerialLength => lunghezza posa cavo aereo pianificata
# plannedFacadeLength => lunghezza posa cavo in facciata pianificata
# plannedUndergroundLength => lunghezza posa cavo sotterraneo pianificata
sub _get_estimated_duration() {
	my $self = shift;
	my %params = @_;
	my $tot = 0;
	
	for my $k ('plannedAerialLength', 'plannedFacadeLength', 'plannedUndergroundLength') {
		$params{$k} = 0 unless defined $params{$k};
	}
	
	my @bind_params = (
		 $params{plannedAerialLength}
		,$params{plannedFacadeLength}
		,$params{plannedUndergroundLength}
		,$params{cableType}
	);
	
	my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION}->fetchall_arrayref(@bind_params);
	unless(scalar @{$ret}) {
		# se non trovo il cableType non ritorno errore ma restituisco 0
		$self->_logger()->warn(__x("Bad configuration: unable to find estimated duration for {cableType}", cableType => $params{cableType}));
		return 0
	}
	
	$tot = $ret->[0]->[0];
	
	# Tempo max totale di lavorazione
	my $ret_max = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX}->fetchall_arrayref();
	if(scalar @{$ret_max}) {
		if($tot > $ret_max->[0]->[0]) {
			$self->_logger()->debug(__x("Computed value for estimated duration ({estimated}) is greater than max allowed value ({max}): using max allowed value", estimated => $tot, max => $ret_max->[0]->[0]));
			$tot = $ret_max->[0]->[0];
		}
	} else {
		# se non trovo il valore massimo non ritorno errore ma restituisco il $tot calcolato
		$self->_logger()->warn(__("Bad configuration: unable to find max estimated duration"));
	}
	
	$tot =~ s/,/\./;
	return $tot;
}

# inserisce le informazioni sull'ultimo campionamento
# PROJECT_ID => Id del progetto
# RUN_ID => data in cui è stata invocata la rotta su Sinfo in formato API::ART::DEFAULT_DATE_FORMAT
# DATA => informazioni come documentate nella rotta
#   GET /sirti/api/private/sinfo/customers/{customerId}/contracts/{contractId}/design/projects/{projectId}/{sync_type}
# FORCE: indica di salvare i dati anche se sono uguali al run precedente
sub _do_work {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,RUN_ID		=> { isa => 'SCALAR' }
					,DATA		=> { isa => 'ARRAY' }
					,FORCE		=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my @bind_params = ();
	
	for my $d (@{$params{DATA}}) {
		
		# NOTA: In ambiente Sinfo, "contabilizzato" consiste nell'associazione di almeno un item di contabilità
		# ad un asset di progetto, indipendentemente dal fatto che questo generi effettiva "Contabilità"
		# In SO si utilizzerà l'espressione "contabilizzabile"
		$d->{status} = $d->{status} eq 'SinfoAccounted' ? 'Accountable' : $d->{status};

		$d->{plannedUndergroundLength} =~ s/\./,/ if defined $d->{plannedUndergroundLength};
		$d->{plannedAerialLength} =~ s/\./,/ if defined $d->{plannedAerialLength};
		$d->{plannedFacadeLength} =~ s/\./,/ if defined $d->{plannedFacadeLength};
		$d->{doneUndergroundLength} =~ s/\./,/ if defined $d->{doneUndergroundLength};
		$d->{doneAerialLength} =~ s/\./,/ if defined $d->{doneAerialLength};
		$d->{doneFacadeLength} =~ s/\./,/ if defined $d->{doneFacadeLength};
		$d->{doneHandmaidLength} =~ s/\./,/ if defined $d->{doneHandmaidLength};
		$d->{horizontalLength} =~ s/\./,/ if defined $d->{horizontalLength};
		$d->{verticalLength} =~ s/\./,/ if defined $d->{verticalLength};
		$d->{fromStockLength} =~ s/\./,/ if defined $d->{fromStockLength};
		$d->{toStockLength} =~ s/\./,/ if defined $d->{toStockLength};

		my $estimated_duration = $self->_get_estimated_duration(
			 cableType					=> $d->{cableType}
			,plannedAerialLength		=> $d->{plannedAerialLength}
			,plannedFacadeLength		=> $d->{plannedFacadeLength}
			,plannedUndergroundLength	=> $d->{plannedUndergroundLength}
		);
		$estimated_duration =~ s/\./,/;
		
		$d->{fromNetworkElementGeoLocation} = $d->{fromNetworkElementGeoLocation}->{coordinates}->[1].",".$d->{fromNetworkElementGeoLocation}->{coordinates}->[0];
		$d->{toNetworkElementGeoLocation} = $d->{toNetworkElementGeoLocation}->{coordinates}->[1].",".$d->{toNetworkElementGeoLocation}->{coordinates}->[0];
		
		push @bind_params, [
			 $d->{cableId}
			,$d->{cableName}
			,$d->{cableType}
			,$d->{potential}
			,$d->{unitOfMeasure}
			,$d->{fromNetworkElementId}
			,$d->{fromNetworkElementName}
			,$d->{fromNetworkElementType}
			,$d->{fromNetworkElementGeoLocation}
			,$d->{toNetworkElementId}
			,$d->{toNetworkElementName}
			,$d->{toNetworkElementType}
			,$d->{toNetworkElementGeoLocation}
			,$d->{UI}
			,$d->{horizontalLength}
			,$d->{verticalLength}
			,$d->{fromStockLength}
			,$d->{toStockLength}
			,$d->{plannedUndergroundLength}
			,$d->{plannedAerialLength}
			,$d->{plannedFacadeLength}
			,$d->{status}
			,$d->{doneUndergroundLength}
			,$d->{doneAerialLength}
			,$d->{doneFacadeLength}
			,$d->{doneHandmaidLength}
			,$estimated_duration
			,$params{RUN_ID}
		];
		
	}
	
	return $self->SUPER::_do_work(PROJECT_ID => $params{PROJECT_ID}, RUN_ID => $params{RUN_ID}, PREPARE_ID => 'INSERT_CAVO', BIND_PARAMS => \@bind_params, FORCE => $params{FORCE});
	
}

# restituisce un nome file ...
# cadastralCode => ...
# pop => ...
# ring => ...
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anzichè un nome file, default false
sub progress_report {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 "cadastralCode"	=> { isa => 'SCALAR' }
					,"pop"				=> { isa => 'SCALAR' }
					,"ring"				=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# fallback nel caso in cui non si trovi alcun progetto che corrisponde ai criteri di ricerca
	my @query_projects_id = ('select null "projectId" from dual where 1=0');
	
	my $projects_coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->_art());
	return undef
		unless defined $projects_coll;
	
	my $lavori_coll = WPSOWORKS::Collection::Activity::LAVORO->new(ART => $self->_art());
	return undef
		unless defined $lavori_coll;
	
	# cerco i progetti che rispondono ai criteri di ricerca
	my $projects = $projects_coll->cerca(
		 "customerId"		=> $self->_get_customer_id()
		,"contractId"		=> $self->_get_contract_id()
		,"cadastralCode"	=> $params{"cadastralCode"}
		,"pop"			=> $params{"pop"}
		,"ring"			=> $params{"ring"}
	);
	return undef
		unless defined $projects;
	
	for my $project (@{$projects}) {
		my $p = $project->property();
		push @query_projects_id, sprintf(
			 'select %s "projectId" from dual'
			,$self->_db()->quote($p->{projectId})
		);
	}
	
	my $query = '
		select es."projectId"
		  ,es."pfpName"
		  ,es."fromNetworkElementId"
		  ,es."fromNetworkElementName"
		  ,es."fromNetworkElementType"
		  ,es."fromNetworkElementGeoLocation"
		  ,count(1) "total"
		  ,sum(case when es."virtualStatus" = \'notWorkable\' then 1 else 0 end) "notWorkable"
		  ,sum(case when es."virtualStatus" = \'workable\' then 1 else 0 end) "workable"
		  ,sum(case when es."virtualStatus" = \'notPlanned\' then 1 else 0 end) "notPlanned"
		  ,sum(case when es."virtualStatus" = \'planned\' then 1 else 0 end) "planned"
		  ,sum(case when es."virtualStatus" = \'workInProgress\' then 1 else 0 end) "workInProgress"
		  ,sum(case when es."virtualStatus" = \'workEnded\' then 1 else 0 end) "workEnded"
		  ,sum(case when es."virtualStatus" = \'asBuiltNotWorked\' then 1 else 0 end) "asBuiltNotWorked"
		  ,sum(case when es."virtualStatus" = \'asBuiltWorked\' then 1 else 0 end) "asBuiltWorked"
		  ,sum(case when es."virtualStatus" = \'sinfoUpdated\' then 1 else 0 end) "sinfoUpdated"
		  ,sum(case when es."virtualStatus" = \'accountable\' then 1 else 0 end) "accountable"
		  ,sum(case when es."virtualStatus" = \'assurance\' then 1 else 0 end) "assurance"
		  ,sum(case when es."virtualStatus" = \'cancelled\' then 1 else 0 end) "cancelled"
		from works.mv_cavi es 
		where es."projectId" in ('.(join(' union ', @query_projects_id)).')
			and es."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
			and es."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
		group by
		   es."projectId"
		  ,es."pfpName"
		  ,es."fromNetworkElementId"
		  ,es."fromNetworkElementName"
		  ,es."fromNetworkElementType"
		  ,es."fromNetworkElementGeoLocation"
		order by 2,4
	';
	
	# return $query;
	
	my $columns = [
		# TRANSLATORS: "PID" is the acronym for "Project Id"
		{ NAME => "projectId", HEADER => __("PID"), JSON_DESCRIPTION => __("Project Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		# TRANSLATORS: "PFI" is the acronym for "PFP Id"
		#{ NAME => "pfpId", HEADER => __("PFI"), JSON_DESCRIPTION => __("PFP Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		# TRANSLATORS: "PFP" is the acronym for "Primary Supply Point"
		{ NAME => "pfpName", HEADER => __("PFP"), JSON_DESCRIPTION => __("Primary Supply Point"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		# TRANSLATORS: "NEI" is the acronym for "Starting Network Element Id"
		{ NAME => "fromNetworkElementId", HEADER => __("NEI"), JSON_DESCRIPTION => __("Starting Network Element Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		# TRANSLATORS: "SNE" is the acronym for "Starting Network Element"
		{ NAME => "fromNetworkElementName", HEADER => __("SNE"), JSON_DESCRIPTION => __("Starting Network Element"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		# TRANSLATORS: "NET" is the acronym for "Network Element Type"
		{ NAME => "fromNetworkElementType", HEADER => __("NET"), JSON_DESCRIPTION => __("Network Element Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		# TRANSLATORS: "NEG" is the acronym for "Network Element GeoLocation"
		{ NAME => "fromNetworkElementGeoLocation", HEADER => __("NEG"), JSON_DESCRIPTION => __("Network Element GeoLocation"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		# TRANSLATORS: "TOT." is the acronym for "Total"
		{ NAME => "total", HEADER => __("TOT."), JSON_DESCRIPTION => __("Total"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "N. WKB." is the acronym for "Not workable"
		{ NAME => "notWorkable", HEADER => __("N. WKB."), JSON_DESCRIPTION => __("Not workable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "WKB." is the acronym for "Workable"
		{ NAME => "workable", HEADER => __("WKB."), JSON_DESCRIPTION => __("Workable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "N. PLN." is the acronym for "Not planned"
		{ NAME => "notPlanned", HEADER => __("N. PLN."), JSON_DESCRIPTION => __("Not planned"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "PLN." is the acronym for "Planned"
		{ NAME => "planned", HEADER => __("PLN."), JSON_DESCRIPTION => __("Planned"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "WIP" is the acronym for "Work In Progress"
		{ NAME => "workInProgress", HEADER => __("WIP"), JSON_DESCRIPTION => __("Work In Progress"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "END" is the acronym for "Ended"
		{ NAME => "workEnded", HEADER => __("END"), JSON_DESCRIPTION => __("Ended"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "ABN" is the acronym for "As-Built not worked"
		{ NAME => "asBuiltNotWorked", HEADER => __("ABN"), JSON_DESCRIPTION => __("As-Built not worked"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "ABW" is the acronym for "As-Built worked"
		{ NAME => "asBuiltWorked", HEADER => __("ABW"), JSON_DESCRIPTION => __("As-Built worked"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "SUP" is the acronym for "SiNFO updated"
		{ NAME => "sinfoUpdated", HEADER => __("SUP"), JSON_DESCRIPTION => __("SiNFO updated"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "ACC" is the acronym for "Accountable", in italian CTZ -> Contabilizzabile
		{ NAME => "accountable", HEADER => __("ACC"), JSON_DESCRIPTION => __("Accountable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "ASS" is the acronym for "Assurance"
		{ NAME => "assurance", HEADER => __("ASS"), JSON_DESCRIPTION => __("Assurance"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		# TRANSLATORS: "CNC" is the acronym for "Cancelled"
		{ NAME => "cancelled", HEADER => __("CNC"), JSON_DESCRIPTION => __("Cancelled"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" }
	];
	
	return $self->report(%params, QUERY => $query, COLUMNS => $columns);

}

# restituisce un nome file ...
# cadastralCode => ...
# pop => ...
# ring => ...
# fromNetworkElementId => ...
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anzichè un nome file, default false
sub cables_details {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 "cadastralCode"	=> { isa => 'SCALAR' }
					,"pop"				=> { isa => 'SCALAR' }
					,"ring"				=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
					 "fromNetworkElementId"	=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my @filters = ();
	if(defined $params{fromNetworkElementId}) {
		for my $fromNetworkElementId (@{$params{fromNetworkElementId}}) {
			push @filters, 'n."fromNetworkElementId" = '.$self->_db()->quote($fromNetworkElementId);
		}
	}

	# fallback nel caso in cui non si trovi alcun progetto che corrisponde ai criteri di ricerca
	my @query_projects_id = ('select null "projectId" from dual where 1=0');

	my $projects_coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->_art());
	return undef
		unless defined $projects_coll;
	
	# cerco i progetti che rispondono ai criteri di ricerca
	my $projects = $projects_coll->cerca(
		 "customerId"		=> $self->_get_customer_id()
		,"contractId"		=> $self->_get_contract_id()
		,"cadastralCode"	=> $params{"cadastralCode"}
		,"pop"			=> $params{"pop"}
		,"ring"			=> $params{"ring"}
	);
	return undef
		unless defined $projects;
	
	for my $project (@{$projects}) {
		my $p = $project->property();
		push @query_projects_id, sprintf(
			 'select %s "projectId" from dual'
			,$self->_db()->quote($p->{projectId})
		);
	}
	
	my $query = '
		select
		   sr."projectId"
		  --,"pfpId"
		  ,dt4.valore "pfpName"
		  ,n."cableId"
		  ,n."cableName"
		  ,n."status"
		  ,n."fromNetworkElementName"
		  ,n."fromNetworkElementType"
		  ,n."toNetworkElementName"
		  ,n."toNetworkElementType"
		  ,n."cableType"
		  ,n."potential"
		  ,n."unitOfMeasure"
		  ,n."fromNetworkElementId"
		  ,n."fromNetworkElementGeoLocation"
		  ,n."toNetworkElementId"
		  ,n."toNetworkElementGeoLocation"
		  ,n."UI"
		  ,n."horizontalLength"
		  ,n."verticalLength"
		  ,n."fromStockLength"
		  ,n."toStockLength"
		  ,case
		    when n."status" not in (\'Done\',\'Accountable\') then n."plannedUndergroundLength"
		    when n."doneUndergroundLength" is null and n."doneHandmaidLength" is null then null
		    else nvl(n."doneUndergroundLength",0) + nvl(n."doneHandmaidLength",0)
		  end "undergroundLength"
		  ,case
		    when n."status" not in (\'Done\',\'Accountable\') then n."plannedAerialLength"
		    else n."doneAerialLength"
		  end "aerialLength"
		  ,case
		    when n."status" not in (\'Done\',\'Accountable\') then n."plannedFacadeLength"
		    else n."doneFacadeLength"
		  end "facadeLength"
		  ,n."estimatedDuration"
		  ,lav."id" "activityId"
		  ,lav."status" "activityStatus"
		  --,n."virtualStatus"
		from
		  CAVI n
		    join sync_runs sr on sr."runId" = n."runId"
		    join v_dt_sistemi dt1 on dt1.nome = \'projectId\' and dt1.valore = to_char(sr."projectId")
		    join v_dt_sistemi dt2 on dt2.id_sistema = dt1.id_sistema and dt2.nome = \'customerId\' and sr."customerId" = dt2.valore
		    join v_dt_sistemi dt3 on dt3.id_sistema = dt1.id_sistema and dt3.nome = \'contractId\' and sr."contractId" = dt3.valore
		    join v_dt_sistemi dt4 on dt4.id_sistema = dt1.id_sistema and dt4.nome = \'pfpName\'
		    join v_sistemi s on s.id = dt1.id_sistema
		    left join v_cavi_lavori lav on lav."cableId" = n."cableId" and lav."customerId" = sr."customerId" and lav."contractId" = sr."contractId" and lav."projectId" =  sr."projectId"
		where 1=1
		  and sr."syncType" = \'CAVI\'
		  and sr."projectId" in ('.(join(' union ', @query_projects_id)).')
		  and sr."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
		  and sr."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
		  and n."historicizingRunId" is null
		  and s.tipo = \'PROJECT\'
		  and s.data_dismissione is null
		  and s.data_sospensione is null
		  '.(scalar @filters ? 'and ('.(join(' or ', @filters)).')' : '').'
		order by
		   "status" DESC
		  ,"pfpName"
		  ,"cableName"
	';
	
	my $columns = [
		{ NAME => "projectId", HEADER => __("Project Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		#{ NAME => "pfpId", HEADER => __("PFP Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "pfpName", HEADER => __("PFP"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "cableId", HEADER => __("Cable Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "cableName", HEADER => __("Name"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "status", HEADER => __("Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "fromNetworkElementName", HEADER => __("Starting Network Element"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "fromNetworkElementType", HEADER => __("Starting Network Element Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "toNetworkElementName", HEADER => __("Ending Network Element"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "toNetworkElementType", HEADER => __("Ending Network Element Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "cableType", HEADER => __("Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "potential", HEADER => __("Potential"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "unitOfMeasure", HEADER => __("Unit of measure"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "fromNetworkElementId", HEADER => __("Starting Network Element Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		{ NAME => "fromNetworkElementGeoLocation", HEADER => __("Starting Network Element GeoLocation"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "toNetworkElementId", HEADER => __("Ending Network Element Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		{ NAME => "toNetworkElementGeoLocation", HEADER => __("Ending Network Element GeoLocation"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "UI", HEADER => __("Real Estate Units"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "horizontalLength", HEADER => __("Horizontal Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "verticalLength", HEADER => __("Vertical Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "fromStockLength", HEADER => __("Starting Element Stock Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "toStockLength", HEADER => __("Ending Element Stock Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "undergroundLength", HEADER => __("Underground Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "aerialLength", HEADER => __("Aerial Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "facadeLength", HEADER => __("Facade Length (mt)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "estimatedDuration", HEADER => __("Estimated Duration (min)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "activityId", HEADER => __("Work Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "activityStatus", HEADER => __("Work Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		#{ NAME => "virtualStatus", HEADER => __("Virtual Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 }
	];
	
	return $self->report(%params, QUERY => $query, COLUMNS => $columns);
	
}

sub search {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {}
				,OPTIONAL   => {
					"cableId"	=> { isa => 'ARRAY' }
					,"projectId"	=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $cond = [];
	
	#verifico singoli parametri
	for my $t ('cableId','projectId'){
		if (defined $params{$t}){
			for my $p (@{$params{$t}}){
				$self->_art()->last_error(__x('{param} must be an integer', param => $t))
					&& return undef 
						if defined $params{$t} && $p !~/^(\d+)$/;
			}
			push @{$cond}, 'mt."'.$t.'" in ('.join (',', @{$params{$t}}).')';
		}
	}
	
#	for my $t ('category', 'subCategory'){
#		push @{$cond}, 'mt."'.$t.'" in ('.join (',', map {$self->_db()->quote($_)} @{$params{$t}}).')' if defined $params{$t} && scalar @{$params{$t}};
#	}
	
	my $sql = '
		select 
			mt."projectId"
			,mt."cableId"
			,mt."cableName"
			,mt."cableType"
			,mt."potential"
			,mt."unitOfMeasure"
			,mt."fromNetworkElementId"
			,mt."fromNetworkElementName"
			,mt."fromNetworkElementType"
			,mt."fromNetworkElementGeoLocation"
			,mt."toNetworkElementId"
			,mt."toNetworkElementName"
			,mt."toNetworkElementType"
			,mt."toNetworkElementGeoLocation"
			,mt."UI"
			,mt."horizontalLength"
			,mt."verticalLength"
			,mt."fromStockLength"
			,mt."toStockLength"
			,mt."plannedUndergroundLength"
			,mt."plannedAerialLength"
			,mt."plannedFacadeLength"
			,mt."status"
			,mt."doneUndergroundLength"
			,mt."doneAerialLength"
			,mt."doneFacadeLength"
			,mt."doneHandmaidLength"
			,mt."estimatedDuration"
		from v_cavi mt
		where 1=1
			and mt."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
			and mt."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
			'.(scalar @{$cond} ? 'and '.join (' and ', @{$cond}) : '').' 
	';

	my $report = $self->report(OUTPUT_FORMAT => 'json', JSON_RETURN_STRING => 1, QUERY => $sql);
	return undef unless defined $report;
	
	my $ret = from_json($report);
	
	return $ret->{data};
	
}

###################

if (__FILE__ eq $0) {

	use API::ART;
	use WPSOWORKS;
	use JSON;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD},
			DEBUG => $ENV{ART_DB_DEBUG}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoworks = WPSOWORKS->new(ART => $art);
	
	my $mt;
	eval {
		$mt = WPSOWORKS::Sync::CAVI->new(
			 WPSOWORKS => $wpsoworks
			,CUSTOMER_ID => 'ENEL'
			,CONTRACT_ID => 'FTTH'
		)
	};
	
	if ($@) {
		die "WPSOWORKS::Sync::CAVI error: $@";
	}
	
	my $is_fatal = 0;
	if($mt->importa(PROJECT_ID => 885, IS_FATAL => \$is_fatal)) {
		get_logger()->info("OK import macro tasks");
	} else {
		if($is_fatal) {
			get_logger()->fatal($art->last_error());
		} else {
			get_logger()->error($art->last_error());
		}
		#die;
	}

	print get_logger()->info("get_sinfo_modified_dates: ".Dumper($mt->get_sinfo_modified_dates(PROJECT_ID => 885)));
	
	my $cables_details = $mt->cables_details(
		 "cadastralCode"		=> 'F839'
		,"pop"					=> '02 - NA-FUORIGROTTA'
		,"ring"					=> '11E'
		,"fromNetworkElementId"	=> [ 1001, 2001 ]
		,"OUTPUT_FORMAT"		=> 'json'
		,JSON_RETURN_STRING		=> 1
	);
	if(defined $cables_details) {
		get_logger()->info("OK report: ".$cables_details);
		my $x = from_json($cables_details);
		get_logger()->info("Righe ritornate: ".(scalar(@{$x->{data}})));
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}

	my $report = $mt->progress_report(
		 "cadastralCode"	=> 'F839'
		,"pop"				=> '02 - NA-FUORIGROTTA'
		,"ring"				=> '11E'
		,"OUTPUT_FORMAT"	=> 'json'
	);
	if(defined $report) {
		get_logger()->info("OK report: ".$report);
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}
	
	$art->cancel();
	
	exit 0;
	
}

1;
