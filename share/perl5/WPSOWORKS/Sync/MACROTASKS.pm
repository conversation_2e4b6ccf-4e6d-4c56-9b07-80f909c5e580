package WPSOWORKS::Sync::MACROTASKS;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use SIRTI::Reports;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use WPSOWORKS::Collection::System::PROJECT;

use base "WPSOWORKS::Sync";

sub new {
	my $this   = shift;
	my $class  = ref($this) || $this;

	my $self = $class->SUPER::new(@_, SYNC_TYPE => 'MACROTASKS');

	$self->_prepare()->{INSERT_MACROTASK} = $self->_art()->_create_prepare(__PACKAGE__.'_INSERT_MACROTASK', qq/
		INSERT
		INTO MACROTASKS
		  (
		    "permitsAreaId",
		    "type",
		    "category",
		    "subCategory",
		    "unitOfMeasure",
		    "toDoQuantity",
		    "doneQuantity",
		    "accountableQuantity",
		    "estimatedDuration",
		    "runId",
		    "historicizingRunId"
		  )
		  VALUES
		  (
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    null
		  )
	/);

	return $self;
}

# restituisce il tempo residuo per la realizzazione
# FIXME: in attesa di specifiche, per ora restituisce sempre 0
sub _get_estimated_duration() {
	return 0;
}

# inserisce le informazioni sull'ultimo campionamento
# PROJECT_ID => Id del progetto
# RUN_ID => data in cui è stata invocata la rotta su Sinfo in formato API::ART::DEFAULT_DATE_FORMAT
# DATA => informazioni come documentate nella rotta
#   GET /sirti/api/private/sinfo/customers/{customerId}/contracts/{contractId}/design/projects/{projectId}/{sync_type}
# FORCE: indica di salvare i dati anche se sono uguali al run precedente
sub _do_work {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,RUN_ID		=> { isa => 'SCALAR' }
					,DATA		=> { isa => 'ARRAY' }
					,FORCE		=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my @bind_params = ();
	
	for my $d (@{$params{DATA}}) {
		
		my $estimated_duration = $self->_get_estimated_duration();
		
		$d->{toDoQuantity} =~ s/\./,/ if defined $d->{toDoQuantity};
		$d->{doneQuantity} =~ s/\./,/ if defined $d->{doneQuantity};
		# NOTA: In ambiente Sinfo, "contabilizzato" consiste nell'associazione di almeno un item di contabilità
		# ad un asset di progetto, indipendentemente dal fatto che questo generi effettiva "Contabilità"
		# In SO si utilizzerà l'espressione "contabilizzabile"
		$d->{sinfoAccountedQuantity} =~ s/\./,/ if defined $d->{sinfoAccountedQuantity};
		
		$estimated_duration =~ s/\./,/;
		
		push @bind_params, [
			 $d->{permitsAreaId}
			,$d->{type}
			,$d->{category}
			,$d->{subCategory}
			,$d->{unitOfMeasure}
			,$d->{toDoQuantity}
			,$d->{doneQuantity}
			,$d->{sinfoAccountedQuantity} # NOTA: viene inserita nella colonna accountableQuantity
			,$estimated_duration
			,$params{RUN_ID}
		];
		
	}
	
	return $self->SUPER::_do_work(PROJECT_ID => $params{PROJECT_ID}, RUN_ID => $params{RUN_ID}, PREPARE_ID => 'INSERT_MACROTASK', BIND_PARAMS => \@bind_params, FORCE => $params{FORCE});
	
}

# restituisce un nome file dove sono elencati i dati relativi al progetto
# PROJECT_ID => Id del progetto
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anziché un nome file, default false
sub details_by_project {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,OPTIONAL   => {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $projects_coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->_art());
	return undef
		unless defined $projects_coll;
	
	# cerco i progetti che rispondono ai criteri di ricerca
	my $projects = $projects_coll->cerca(
		 "customerId"		=> $self->_get_customer_id()
		,"contractId"		=> $self->_get_contract_id()
		,"projectId"		=> $params{PROJECT_ID}
	);
	return undef
		unless defined $projects;
	
	my $cond = "1=1";
	unless(scalar @{$projects}) {
		# se non ho visibilità sul progetto aggiungo la condizione 1=0
		$cond = "1=0"
	}
	
	my $query = '
		select 
			 "permitsAreaId"
			  ,"type"
			  ,"category"
			  ,"subCategory"
			  ,"unitOfMeasure"
			  ,"totQuantity"
			  ,"toDoQuantity"
			  ,"inProgressQuantity"
			  ,"doneQuantity"
			  ,"accountableQuantity"
			  ,"inProgConstructionSiteCount"
			  ,"doneConstructionSiteCount"
			  ,"cancelConstructionSiteCount"
			  ,"estimatedDuration"
		from v_macrotasks_lavori
		where '.$cond.' -- se 1=0 vuole dire che l\'utente non ha visibilita sul progetto
			  and "customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
			  and "contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
			  and "projectId" = '.$params{PROJECT_ID}.'
		order by
		   "type"
		  ,"category"
		  ,"subCategory"
		  ,"permitsAreaId"
	';
	
	my $columns = [
		{
		  NAME => "permitsAreaId",
		  HEADER => __("Permits Area Id"),
		  JSON_HIDDEN => 1,
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string",
		  XLSX_EXCLUDE => 0,
		  CSV_EXCLUDE => 0
		},
		{
		  NAME => "type",
		  HEADER => __("Type"),
		  JSON_HIDDEN => 1,
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "category",
		  HEADER => __("Category"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "subCategory",
		  HEADER => __("Subcategory"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "unitOfMeasure",
		  HEADER => __("UoM"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "totQuantity",
		  HEADER => __("TOT"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "toDoQuantity",
		  HEADER => __("Todo quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "inProgressQuantity",
		  HEADER => __("In progress quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "doneQuantity",
		  HEADER => __("Done quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "accountableQuantity",
		  HEADER => __("Accountable quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "inProgConstructionSiteCount",
		  HEADER => __("In progress construction site count"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "doneConstructionSiteCount",
		  HEADER => __("Done construction site count"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "cancelConstructionSiteCount",
		  HEADER => __("Cancelled construction site count"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "estimatedDuration",
		  HEADER => __("Estimated duration"),
		  JSON_HIDDEN => 1,
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number",
		  XLSX_EXCLUDE => 1,
		  CSV_EXCLUDE => 1
		}
	];
	
	return $self->report(%params, QUERY => $query, COLUMNS => $columns);
	
}

sub search {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {}
				,OPTIONAL   => {
					"projectId"		=> { isa => 'ARRAY' }
					,"permitsAreaId"	=> { isa => 'ARRAY' }
					,"category"			=> { isa => 'ARRAY' }
					,"subCategory"		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $cond = [];
	
	#verifico singoli parametri
	for my $t ('projectId', 'permitsAreaId'){
		if (defined $params{$t}){
			for my $p (@{$params{$t}}){
				$self->_art()->last_error(__x('{param} must be an integer', param => $t))
					&& return undef 
						if defined $params{$t} && $p !~/^(\d+)$/;
			}
			push @{$cond}, 'mt."'.$t.'" in ('.join (',', @{$params{$t}}).')';
		}
	}
	
	for my $t ('category', 'subCategory'){
		push @{$cond}, 'mt."'.$t.'" in ('.join (',', map {$self->_db()->quote($_)} @{$params{$t}}).')' if defined $params{$t} && scalar @{$params{$t}};
	}
	
	my $sql = '
		select 
			mt."customerId"
			, mt."contractId"
			, mt."projectId"
			, mt."runDate"
			, mt."sinfoLastModified"
			, mt."permitsAreaId"
			, mt."type"
			, mt."category"
			, mt."subCategory"
			, mt."unitOfMeasure"
			, mt."totQuantity"
			, mt."toDoQuantity"
			, mt."inProgressQuantity"
			, mt."doneQuantity"
			, mt."accountableQuantity"
			, mt."inProgConstructionSiteCount"
			, mt."doneConstructionSiteCount"
			, mt."cancelConstructionSiteCount"
			, mt."estimatedDuration"
		from v_macrotasks_lavori mt
		where 1=1
			and mt."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
			and mt."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
			'.(scalar @{$cond} ? 'and '.join (' and ', @{$cond}) : '').' 
	';

	my $report = $self->report(OUTPUT_FORMAT => 'json', JSON_RETURN_STRING => 1, QUERY => $sql);
	return undef unless defined $report;
	
	my $ret = from_json($report);
	
	return $ret->{data};
	
}

###################

if (__FILE__ eq $0) {

	use API::ART;
	use WPSOWORKS;
	use JSON;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD},
			DEBUG => $ENV{ART_DB_DEBUG}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoworks = WPSOWORKS->new(ART => $art);
	
	my $mt;
	eval {
		$mt = WPSOWORKS::Sync::MACROTASKS->new(
			 WPSOWORKS => $wpsoworks
			,CUSTOMER_ID => 'ENEL'
			,CONTRACT_ID => 'FTTH'
		)
	};
	
	if ($@) {
		die "WPSOWORKS::Sync::MACROTASKS error: $@";
	}
	
	my $is_fatal = 0;
	if($mt->importa(PROJECT_ID => 885, IS_FATAL => \$is_fatal)) {
		get_logger()->info("OK import macro tasks");
	} else {
		if($is_fatal) {
			get_logger()->fatal($art->last_error());
		} else {
			get_logger()->error($art->last_error());
		}
		die;
	}
	
	print get_logger()->info("get_sinfo_modified_dates: ".Dumper($mt->get_sinfo_modified_dates(PROJECT_ID => 885)));
	
	my $details_by_project = $mt->details_by_project(PROJECT_ID => 885, OUTPUT_FORMAT => 'json'); 
	if(defined $details_by_project) {
		get_logger()->info("OK details_by_project: ".$details_by_project);
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}
	
	my $search = $mt->search(
		customerId => 'ENEL',
		contractId => 'FTTH',
		permitsAreaId => [2224518],
		category => ['POZZETTI'],
		subCategory => ['pozzetto 125x80']
	);
	if(defined $search) {
		get_logger()->info("OK: ".Dumper ($search));
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}
	
	$art->cancel();
	
	exit 0;
	
}

1;
