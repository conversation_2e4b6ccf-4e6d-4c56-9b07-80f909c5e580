package WPSOWORKS::Sync::NODI;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use YAML;

use SIRTI::Reports;

use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Collection::System::NODO;
use WPSOWORKS::Collection::Activity::LAVORO;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base "WPSOWORKS::Sync";

sub new {
	my $this   = shift;
	my $class  = ref($this) || $this;

	my $self = $class->SUPER::new(@_, SYNC_TYPE => 'NODI');

	$self->_prepare()->{INSERT_NODO} = $self->_art()->_create_prepare(__PACKAGE__.'_INSERT_NODO', qq/
		INSERT
		INTO NODI
		  (
		    "networkElementId",
		    "networkElementName",
		    "networkElementType",
		    "networkElementGeoLocation",
		    "cablingType",
		    "networkElementIsDone",
		    "infrastructureExists",
		    "infrastructureIsDone",
		    "inputCableId",
		    "inputCableName",
		    "inputCablePotential",
		    "inputCablePotentialPlanned",
		    "outputCableId",
		    "outputCableName",
		    "outputCablePotential",
		    "outputCablePotentialPlanned",
		    "status",
		    "splicedFibers",
		    "terminatedFibers",
		    "runId",
		    "historicizingRunId"
		  )
		  VALUES
		  (
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    ?,
		    null
		  )
	/);
	$self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_POSA_MECCANICA', qq{
		select "manMinutes"
		from CONF_NODI_POSA_MECCANICA
		where "networkElementType" = ?
		and "disableDate" is null
	});
	
	$self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE', qq{
		select "manMinutes" 
		from CONF_NODI_ATTESTAZIONE
		where "networkElementType" = ?
		and "disableDate" is null
		and ? between "minCablesNumbers" and "maxCablesNumbers"
	});
	
	$self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TIPO_CABLAGGIO', qq{
		select "manMinutes" 
		from CONF_NODI_TIPO_CABLAGGIO
		where "networkElementType" = ?
		and "disableDate" is null
		and "cablingType" = ?
	});
	
	$self->_prepare()->{GET_ESTIMATED_DURATION_CAVO_SPILLATO} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_CAVO_SPILLATO', qq{
		select "manMinutes" 
		from CONF_NODI_CAVO_SPILLATO
		where "networkElementType" = ?
		and "disableDate" is null
	});
	
	$self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_TOTALE} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TEMPO_TOTALE', qq{
		select "manMinutes" 
		from CONF_NODI_TEMPO_TOTALE
		where "networkElementType" = ?
		and "disableDate" is null
	});

	$self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NOT_NULL} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NOT_NULL', qq{
		select "manMinutes" 
		from CONF_NODI_TMP_STD_MAX
		where "networkElementType" = ?
		and "networkElementSubType" = ?
		and "disableDate" is null
	});

	$self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NULL} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NULL', qq{
		select "manMinutes" 
		from CONF_NODI_TMP_STD_MAX
		where "networkElementType" = ?
		and "networkElementSubType" is null
		and "disableDate" is null
	});

	$self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_GIUNZ} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ESTIMATED_DURATION_TMP_STD_GIUNZ', qq{
		select "manMinutes" 
		from CONF_NODI_TMP_STD_GIUNZ
		where "networkElementType" = ?
		and "disableDate" is null
	});

	$self->_prepare()->{UPDATE_ESTIMATED_DURATION} = $self->_art()->_create_prepare(__PACKAGE__.'_UPDATE_ESTIMATED_DURATION', qq{
		update NODI_RIEPILOGO
		set "estimatedDuration" = ceil(?)
			,"warnings" = ?
		where "networkElementId" = ? and "runId" = ?
	});
	
	$self->_prepare()->{HISTORICIZE_NODI_RIEPILOGO} = $self->_art()->_create_prepare(__PACKAGE__.'_HISTORICIZE_NODI_RIEPILOGO', qq/
		UPDATE NODI_RIEPILOGO
		SET "historicizingRunId" = ?
		WHERE "runId" = (
		  select max(m."runId")
		  from
		    NODI_RIEPILOGO m
		      join SYNC_RUNS mr on mr."runId" = m."runId" and mr."syncType" = 'NODI'
		  where 1=1
		    and m."historicizingRunId" is null
		    and mr."customerId" = ?
		    and mr."contractId" = ?
		    and mr."projectId" = ?
		)
	/);

	$self->_prepare()->{POPULATE_NODI_RIEPILOGO} = $self->_art()->_create_prepare(__PACKAGE__.'_POPULATE_NODI_RIEPILOGO', '
		insert into nodi_riepilogo
		select
		   sr."customerId"
		  ,sr."contractId"
		  ,dt5.valore "pop"
		  ,dt6.valore "ring"
		  ,sr."projectId"
		  ,dt7.valore "pfpId"
		  ,dt4.valore "pfpName"
		  ,n."networkElementId"
		  ,n."networkElementType"
		  ,n."networkElementName"
		  ,n."networkElementGeoLocation"
		  ,SUBSTR(n."networkElementGeoLocation", 1, INSTR(n."networkElementGeoLocation", \',\')-1) "latitude"
		  ,SUBSTR(n."networkElementGeoLocation", INSTR(n."networkElementGeoLocation", \',\')+1) "longitude"
		  ,case
		    when n."networkElementIsDone" = 1 then \'Done\'
		    else \'Not done\'
		  end "networkElementStatus"
		  ,case
		    when n."infrastructureExists" = 0 then \'Not exists\'
		    when n."infrastructureIsDone" = 1 then \'Done\'
		    else \'Not done\'
		  end "networkElementContainerStatus"
		  ,count(1) "total"
		  ,sum(case when n."status" = \'Planned\' then 1 else 0 end) "notWorkable"
		  ,sum(case when n."status" = \'Workable\' then 1 else 0 end) "workable"
		  ,sum(case when n."status" = \'Done\' then 1 else 0 end) "done"
		  ,0 "estimatedDuration"
		  ,null "warnings"
		  ,sr."runId"
		  ,null "historicizingRunId"
		  ,sum(case when n."status" = \'Accountable\' then 1 else 0 end) "accountable"
		from
		  NODI n
		    join sync_runs sr on sr."runId" = n."runId"
		    join v_dt_sistemi dt1 on dt1.nome = \'projectId\' and dt1.valore = to_char(sr."projectId")
		    join v_dt_sistemi dt2 on dt2.id_sistema = dt1.id_sistema and dt2.nome = \'customerId\' and sr."customerId" = dt2.valore
		    join v_dt_sistemi dt3 on dt3.id_sistema = dt1.id_sistema and dt3.nome = \'contractId\' and sr."contractId" = dt3.valore
		    join v_dt_sistemi dt4 on dt4.id_sistema = dt1.id_sistema and dt4.nome = \'pfpName\'
		    join v_dt_sistemi dt5 on dt5.id_sistema = dt1.id_sistema and dt5.nome = \'pop\'
		    join v_dt_sistemi dt6 on dt6.id_sistema = dt1.id_sistema and dt6.nome = \'ring\'
		    join v_dt_sistemi dt7 on dt7.id_sistema = dt1.id_sistema and dt7.nome = \'pfpId\'
		    join v_sistemi s on s.id = dt1.id_sistema
		where 1=1
		  and sr."syncType" = \'NODI\'
		  and sr."projectId" in ?
		  and sr."customerId" = ?
		  and sr."contractId" = ?
		  and n."historicizingRunId" is null
		  and s.tipo = \'PROJECT\'
		  and s.data_dismissione is null
		  and s.data_sospensione is null
		group by
		   sr."customerId"
		  ,sr."contractId"
		  ,dt5.valore -- "pop"
		  ,dt6.valore -- "ring"
		  ,sr."projectId"
		  ,dt7.valore -- "pfpId"
		  ,dt4.valore -- "pfpName"
		  ,n."networkElementId"
		  ,n."networkElementName"
		  ,n."networkElementType"
		  ,n."networkElementGeoLocation"
		  ,SUBSTR(n."networkElementGeoLocation", 1, INSTR(n."networkElementGeoLocation", \',\')-1) -- "latitude"
		  ,SUBSTR(n."networkElementGeoLocation", INSTR(n."networkElementGeoLocation", \',\')+1) -- "longitude"
		  ,case
		    when n."networkElementIsDone" = 1 then \'Done\'
		    else \'Not done\'
		  end -- "networkElementStatus"
		  ,case
		    when n."infrastructureExists" = 0 then \'Not exists\'
		    when n."infrastructureIsDone" = 1 then \'Done\'
		    else \'Not done\'
		  end -- "networkElementContainerStatus"
		  ,sr."runId"
	');
	
	$self->_prepare()->{GET_ALL_NODI} = $self->_art()->_create_prepare(__PACKAGE__.'_GET_ALL_NODI', '
		select *
		from nodi_riepilogo t
		  join nodi n on t."runId" = n."runId" and t."networkElementId" = n."networkElementId"
		where t."runId" = ?
		order by
		   t."networkElementId"
	');
	
	$self->_prepare()->{CHECK_COERENZA_NODI_NET} = $self->_art()->_create_prepare(__PACKAGE__.'_CHECK_COERENZA_NODI_NET', '
		select "networkElementId",decode(count(distinct "networkElementType"), 1, 1, 0) "consistentNetworkElementType"
		from nodi
		where 1=1
		  and "runId" = ?
		  and "networkElementId" = ?
		group by "networkElementId"
	');
	
	return $self;
}

# inserisce le informazioni sull'ultimo campionamento
# PROJECT_ID => Id del progetto
# RUN_ID => data in cui è stata invocata la rotta su Sinfo in formato API::ART::DEFAULT_DATE_FORMAT
# DATA => informazioni come documentate nella rotta
#   GET /sirti/api/private/sinfo/customers/{customerId}/contracts/{contractId}/design/projects/{projectId}/{sync_type}
# FORCE: indica di salvare i dati anche se sono uguali al run precedente
sub _do_work {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,RUN_ID		=> { isa => 'SCALAR' }
					,DATA		=> { isa => 'ARRAY' }
					,FORCE		=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my @bind_params = ();
	
	for my $d (@{$params{DATA}}) {
		
		# NOTA: In ambiente Sinfo, "contabilizzato" consiste nell'associazione di almeno un item di contabilità
		# ad un asset di progetto, indipendentemente dal fatto che questo generi effettiva "Contabilità"
		# In SO si utilizzerà l'espressione "contabilizzabile"
		$d->{status} = $d->{status} eq 'SinfoAccounted' ? 'Accountable' : $d->{status};
		
		# gestione boolean not null
		my $network_element_is_done = $d->{networkElementIsDone} ? 1 : 0;
		my $infrastructure_exists = $d->{infrastructureExists} ? 1 : 0;

		# gestione boolean nullable
		my $infrastructure_is_done = defined $d->{infrastructureIsDone} ? ($d->{infrastructureIsDone} ? 1 : 0) : undef;
		
#		use Data::Dumper;
#		print STDERR Dumper $d;
		
		$d->{networkElementGeoLocation} = $d->{networkElementGeoLocation}->{coordinates}->[1].",".$d->{networkElementGeoLocation}->{coordinates}->[0];
		
		push @bind_params, [
			 $d->{networkElementId}
			,$d->{networkElementName}
			,$d->{networkElementType}
			,$d->{networkElementGeoLocation}
			,$d->{cablingType}
			,$network_element_is_done
			,$infrastructure_exists
			,$infrastructure_is_done
			,$d->{inputCableId}
			,$d->{inputCableName}
			,$d->{inputCablePotential}
			,$d->{inputCablePotentialPlanned}
			,$d->{outputCableId}
			,$d->{outputCableName}
			,$d->{outputCablePotential}
			,$d->{outputCablePotentialPlanned}
			,$d->{status}
			,$d->{splicedFibers}
			,$d->{terminatedFibers}
			,$params{RUN_ID}
		];
		
	}
	
	$self->_db()->do("SAVEPOINT WPSOWORKS_SYNC_NODI__DOWORK");
	
	my $ret = $self->SUPER::_do_work(PROJECT_ID => $params{PROJECT_ID}, RUN_ID => $params{RUN_ID}, PREPARE_ID => 'INSERT_NODO', BIND_PARAMS => \@bind_params, FORCE => $params{FORCE});
	return undef
		unless defined $ret;
	
	# se _do_work ritorna 0 vuol dire che i dati sono uguali al run precedente (e di conseguenza il riepilogo)
	# non faccio nulla a meno che è impostato il parametro FORCE
	return $ret
		if !$params{FORCE} && $ret == 0;
	
	my @h_bind_params = (
		 $params{RUN_ID}
		,$self->_get_customer_id
		,$self->_get_contract_id
		,$params{PROJECT_ID}
	);
	
	unless ($self->_prepare()->{HISTORICIZE_NODI_RIEPILOGO}->execute(@h_bind_params)) {
		my $message = $self->_db()->get_errormessage();
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS_SYNC_NODI__DOWORK");
		$self->_art()->last_error(__x("Unable to historicize {what} summary: {message}", what => $self->_get_sync_type, message => $message));
		return undef;
	}
	
	unless ($self->_prepare()->{POPULATE_NODI_RIEPILOGO}->execute($params{PROJECT_ID}, $self->_get_customer_id(), $self->_get_contract_id())) {
		my $message = $self->_db()->get_errormessage();
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS_SYNC_NODI__DOWORK");
		$self->_art()->last_error(__x("Unable to populate table nodi riepilogo: {message}", message => $message));
		return undef;
	}
	
	unless(defined $self->_update_estimated_duration($params{RUN_ID})) {
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS_SYNC_NODI__DOWORK");
		return undef;
	}
	
	return $ret;
	
}

# restituisce un nome file ...
# cadastralCode => ...
# pop => ...
# ring => ...
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anziché un nome file, default false
sub progress_report {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 "cadastralCode"	=> { isa => 'SCALAR' }
					,"pop"				=> { isa => 'SCALAR' }
					,"ring"				=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# fallback nel caso in cui non si trovi alcun progetto che corrisponde ai criteri di ricerca
	my @query_projects_id = ('select null "projectId" from dual where 1=0');
	
	my $projects_coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->_art());
	return undef
		unless defined $projects_coll;
	
	# cerco i progetti che rispondono ai criteri di ricerca
	my $projects = $projects_coll->cerca(
		 "customerId"		=> $self->_get_customer_id()
		,"contractId"		=> $self->_get_contract_id()
		,"cadastralCode"	=> $params{"cadastralCode"}
		,"pop"				=> $params{"pop"}
		,"ring"				=> $params{"ring"}
	);
	return undef
		unless defined $projects;
	
	for my $project (@{$projects}) {
		my $p = $project->property();
		push @query_projects_id, sprintf(
			 'select %s "projectId" from dual'
			,$self->_db()->quote($p->{projectId})
		);
	}
	
	my $query = '
		select
		   n."projectId"
		  ,n."pfpName"
		  ,n."networkElementId"
		  ,n."networkElementName"
		  ,n."networkElementType"
		  ,n."networkElementGeoLocation"
		  ,n."latitude"
		  ,n."longitude"
		  ,n."networkElementStatus"
		  ,n."networkElementContainerStatus"
		  ,n."total"
		  ,n."notWorkable"
		  ,n."workable"
		  ,n."done"
		  ,n."accountable"
		  ,n."estimatedDuration"
		  ,n."warnings"
		  ,n."workId"
		  ,n."workStatus"
		  ,n."asBuiltStatus"
		from
		  V_NODI_LAVORI_RIEPILOGO n
		where 1=1
		  and n."projectId" in ('.(join(' union ', @query_projects_id)).')
		  and n."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
		  and n."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
		order by
		   n."pfpName"
		  ,n."networkElementName"
	';
	
	# return $query;
	
	my $columns = [
		{ NAME => "projectId", HEADER => __("Project Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		{ NAME => "pfpName", HEADER => __("PFP"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementId", HEADER => __("Network Element Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		{ NAME => "networkElementType", HEADER => __("Network Element Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementName", HEADER => __("Network Element Name"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementGeoLocation", HEADER => __("Network Element GeoLocation"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "latitude", HEADER => __("Network Element Latitude"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "longitude", HEADER => __("Network Element Longitude"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "networkElementStatus", HEADER => __("Network Element Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementContainerStatus", HEADER => __("Container Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "total", HEADER => __("Total"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "notWorkable", HEADER => __("Not workable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "workable", HEADER => __("Workable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "done", HEADER => __("Done"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "accountable", HEADER => __("Accountable"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "estimatedDuration", HEADER => __("Estimated duration"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "warnings", HEADER => __("Warnings"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "workId", HEADER => __("Work Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "workStatus", HEADER => __("Work Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "asBuiltStatus", HEADER => __("As-Built Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" }
	];
	
	return $self->report(%params, QUERY => $query, COLUMNS => $columns);

}

sub _qm_PFP {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;
	
	# NOTA: non avendo da SiNFO informazioni che ci permettano di distinguere il tipo di PFP,
	# per ora lo consideriamo sempre come spillato (caso più frequente)
	$subtype = 'SPILLATO';
	
	# Tempo max totale di lavorazione
	my $ret;
	if(defined $subtype) {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NOT_NULL}->fetchall_arrayref($d->{networkElementType}, $subtype);
	} else {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NULL}->fetchall_arrayref($d->{networkElementType});
	}
	if(scalar @{$ret}) {
		$ret->[0]->[0] =~ s/,/\./;
		$tot = $ret->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find max estimated duration for {networkElementType}", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qm_PFS {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo
	
	# Tempo max totale di lavorazione
	my $ret;
	if(defined $subtype) {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NOT_NULL}->fetchall_arrayref($d->{networkElementType}, $subtype);
	} else {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NULL}->fetchall_arrayref($d->{networkElementType});
	}
	if(scalar @{$ret}) {
		$ret->[0]->[0] =~ s/,/\./;
		$tot = $ret->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find max estimated duration for {networkElementType}", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qm_PD {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# Tempo max totale di lavorazione
	my $ret;
	if(defined $subtype) {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NOT_NULL}->fetchall_arrayref($d->{networkElementType}, $subtype);
	} else {
		$ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_MAX_SUBTYPE_NULL}->fetchall_arrayref($d->{networkElementType});
	}
	if(scalar @{$ret}) {
		$ret->[0]->[0] =~ s/,/\./;
		$tot = $ret->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find max estimated duration for {networkElementType}", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qm_GL {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# NOTA: non c'è quota massima ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qm_PTE {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# NOTA: non c'è quota massima ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qm_PTA {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# NOTA: non c'è quota massima ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qm_CabinaEnel {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# NOTA: non c'è quota massima ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qm_QuadroIP {
	my $self = shift;
	my $d = shift;
	my $tot = 999999;
	my $warnings = [];
	my $subtype;

	# NOTA: non sono previste varianti di tipo

	# NOTA: non c'è quota massima ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qf_PFP {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{networkElementStatus} ne 'Done') {
		# Tempo di posa meccanica del PFP: solo se se networkElementIsDone = false ovvero networkElementStatus <> 'Done'
		my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret}) {
			$ret->[0]->[0] =~ s/,/\./;
			$tot += $ret->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("mechanical laying"), networkElementType => $d->{networkElementType});
		}
	}
	
	return ($tot, $warnings);
}

sub _qf_PFS {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];

	# NOTA: non c'è quota fissa ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qf_PD {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{networkElementStatus} ne 'Done') {
		# Tempo di posa meccanica del PD: solo se se networkElementIsDone = false ovvero networkElementStatus <> 'Done'
		my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret}) {
			$ret->[0]->[0] =~ s/,/\./;
			$tot += $ret->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("mechanical laying"), networkElementType => $d->{networkElementType});
		}
	}
	if(!$d->{done} && !$d->{accountable}) {
		# Tempo di attestazione del cavo di input: solo se non esistono giunzioni in stato Done per il nodo, ossia non è stato ancora giuntato alcun cavo
		if(defined $d->{inputCablePotential}) {
			my $ret_ta_ci = [];
			$ret_ta_ci = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{inputCablePotential});
			if(scalar @{$ret_ta_ci}) {
				$ret_ta_ci->[0]->[0] =~ s/,/\./;
				$tot += $ret_ta_ci->[0]->[0];
			} else {
				push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("attestation time"), networkElementType => $d->{networkElementType});
			}
		} else {
			push @{$warnings}, __x("{key} cannot be null in computing fixed quote for {networkElementType}", key => "inputCablePotential", networkElementType => $d->{networkElementType});
		}
		# Tempo di preparazione del cavo spillato: solo se non esistono giunzioni in stato Done per il nodo, ossia non è stato ancora giuntato alcun cavo
		my $ret_tcs = $self->_prepare()->{GET_ESTIMATED_DURATION_CAVO_SPILLATO}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret_tcs}) {
			$ret_tcs->[0]->[0] =~ s/,/\./;
			$tot += $ret_tcs->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("twisted cable"), networkElementType => $d->{networkElementType});
		}
	}
	
	return ($tot, $warnings);
}

sub _qf_GL {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{networkElementStatus} ne 'Done') {
		# Tempo di posa meccanica del GL: solo se se networkElementIsDone = false ovvero networkElementStatus <> 'Done'
		my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret}) {
			$ret->[0]->[0] =~ s/,/\./;
			$tot += $ret->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("mechanical laying"), networkElementType => $d->{networkElementType});
		}
	}
	# Tempo di attestazione del cavo di input
	my $ret_ta_ci = [];
	if(defined $d->{inputCablePotential}) {
		$ret_ta_ci = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{inputCablePotential});
		if(scalar @{$ret_ta_ci}) {
			$ret_ta_ci->[0]->[0] =~ s/,/\./;
			$tot += $ret_ta_ci->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("attestation time"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing fixed quote for {networkElementType}", key => "inputCablePotential", networkElementType => $d->{networkElementType});
	}
	# Tempo di preparazione del cavo spillato
	my $ret_tcs = $self->_prepare()->{GET_ESTIMATED_DURATION_CAVO_SPILLATO}->fetchall_arrayref($d->{networkElementType});
	if(scalar @{$ret_tcs}) {
		$ret_tcs->[0]->[0] =~ s/,/\./;
		$tot += $ret_tcs->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("twisted cable"), networkElementType => $d->{networkElementType});
	}
	# Tempo di attestazione del cavo di output
	my $ret_ta_co = [];
	if(defined $d->{outputCablePotential}) {
		$ret_ta_co = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{outputCablePotential});
		if(scalar @{$ret_ta_co}) {
			$ret_ta_co->[0]->[0] =~ s/,/\./;
			$tot += $ret_ta_co->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("attestation time"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing fixed quote for {networkElementType}", key => "outputCablePotential", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qf_PTE {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{networkElementStatus} ne 'Done') {
		# Tempo di posa meccanica del PTE: solo se se networkElementIsDone = false ovvero networkElementStatus <> 'Done'
		my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret}) {
			$ret->[0]->[0] =~ s/,/\./;
			$tot += $ret->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("mechanical laying"), networkElementType => $d->{networkElementType});
		}
	}
	# Tempo standard di giunzione per PTE
	my $ret_tg = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_GIUNZ}->fetchall_arrayref($d->{networkElementType});
	if(scalar @{$ret_tg}) {
		$ret_tg->[0]->[0] =~ s/,/\./;
		$tot += $ret_tg->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("splice standard time"), networkElementType => $d->{networkElementType});
	}

	return ($tot, $warnings);
}

sub _qf_PTA {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{networkElementStatus} ne 'Done') {
		# Tempo di posa meccanica del PTA: solo se se networkElementIsDone = false ovvero networkElementStatus <> 'Done'
		my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_POSA_MECCANICA}->fetchall_arrayref($d->{networkElementType});
		if(scalar @{$ret}) {
			$ret->[0]->[0] =~ s/,/\./;
			$tot += $ret->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("mechanical laying"), networkElementType => $d->{networkElementType});
		}
	}
	# Tempo standard di giunzione per PTA
	my $ret_tg = $self->_prepare()->{GET_ESTIMATED_DURATION_TMP_STD_GIUNZ}->fetchall_arrayref($d->{networkElementType});
	if(scalar @{$ret_tg}) {
		$ret_tg->[0]->[0] =~ s/,/\./;
		$tot += $ret_tg->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("splice standard time"), networkElementType => $d->{networkElementType});
	}

	return ($tot, $warnings);
}

sub _qf_CabinaEnel {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# Tempo totale di lavorazione
	my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_TOTALE}->fetchall_arrayref($d->{networkElementType});
	if(scalar @{$ret}) {
		$ret->[0]->[0] =~ s/,/\./;
		$tot += $ret->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("total time"), networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qf_QuadroIP {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# Tempo totale di lavorazione
	my $ret = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_TOTALE}->fetchall_arrayref($d->{networkElementType});
	if(scalar @{$ret}) {
		$ret->[0]->[0] =~ s/,/\./;
		$tot += $ret->[0]->[0];
	} else {
		push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing fixed quote for {networkElementType}", type => __("total time"), networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qv_PFP {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# Tempo di attestazione del cavo di output
	if(defined $d->{outputCablePotential}) {
		my $ret_ta = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{outputCablePotential});
		if(scalar @{$ret_ta}) {
			$ret_ta->[0]->[0] =~ s/,/\./;
			$tot += $ret_ta->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType}", type => __("attestation time"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType}", key => "outputCablePotential", networkElementType => $d->{networkElementType});
	}
	# Tempo di esecuzione di una giunzione
	if(defined $d->{outputCablePotentialPlanned}) {
		my $ret_tc = $self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO}->fetchall_arrayref($d->{networkElementType}, $d->{cablingType});
		if(scalar @{$ret_tc}) {
			$ret_tc->[0]->[0] =~ s/,/\./;
			$tot += $ret_tc->[0]->[0] * $d->{outputCablePotentialPlanned};
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType}", type => __("cabling type"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType}", key => "outputCablePotentialPlanned", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qv_PFS {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	if($d->{cablingType} eq 'Splicing') {
		# Tempo di attestazione del cavo di input
		if(defined $d->{inputCableId}) {
			if(defined $d->{inputCablePotential}) {
				my $ret_ta_ci = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{inputCablePotential});
				if(scalar @{$ret_ta_ci}) {
					$ret_ta_ci->[0]->[0] =~ s/,/\./;
					$tot += $ret_ta_ci->[0]->[0];
				} else {
					push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType} and cablingType {cablingType}", type => __("attestation time"), networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
				}
			} else {
				push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "inputCablePotential", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
			}
		} else {
			push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "inputCableId", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
		}
		# Tempo di esecuzione di una giunzione
		if(defined $d->{inputCablePotentialPlanned}) {
			my $ret_tc_ci = $self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO}->fetchall_arrayref($d->{networkElementType}, 'Splicing');
			if(scalar @{$ret_tc_ci}) {
				$ret_tc_ci->[0]->[0] =~ s/,/\./;
				$tot += $ret_tc_ci->[0]->[0] * $d->{inputCablePotentialPlanned};
			} else {
				push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType} and cablingType {cablingType}", type => __("cabling type input cables"), networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
			}
		} else {
			push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "inputCablePotentialPlanned", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
		}
	} else { # $d->{cablingType} eq 'Termination'
		# Tempo di attestazione del cavo di output
		if(defined $d->{outputCableId}) {
			if(defined $d->{outputCablePotential}) {
				my $ret_ta_co = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{outputCablePotential});
				if(scalar @{$ret_ta_co}) {
					$ret_ta_co->[0]->[0] =~ s/,/\./;
					$tot += $ret_ta_co->[0]->[0];
				} else {
					push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType} and cablingType {cablingType}", type => __("attestation time"), networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
				}
			} else {
				push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "outputCablePotential", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
			}
		} else {
			 push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "outputCableId", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
		}
		# Tempo di esecuzione di una terminazione
		if(defined $d->{outputCablePotentialPlanned}) {
			my $ret_tc_co = $self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO}->fetchall_arrayref($d->{networkElementType}, 'Termination');
			if(scalar @{$ret_tc_co}) {
				$ret_tc_co->[0]->[0] =~ s/,/\./;
				$tot += $ret_tc_co->[0]->[0] * $d->{outputCablePotentialPlanned};
			} else {
				push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType} and cablingType {cablingType}", type => __("cabling type output cables"), networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
			}
		} else {
			push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType} and cablingType {cablingType}", key => "outputCablePotentialPlanned", networkElementType => $d->{networkElementType}, cablingType => $d->{cablingType});
		}
	}
	
	return ($tot, $warnings);
}

sub _qv_PD {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# Tempo di attestazione del cavo di output
	if(defined $d->{outputCablePotential}) {
		my $ret_ta = $self->_prepare()->{GET_ESTIMATED_DURATION_TEMPO_ATTESTAZIONE}->fetchall_arrayref($d->{networkElementType}, $d->{outputCablePotential});
		if(scalar @{$ret_ta}) {
			$ret_ta->[0]->[0] =~ s/,/\./;
			$tot += $ret_ta->[0]->[0];
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType}", type => __("attestation time"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType}", key => "outputCablePotential", networkElementType => $d->{networkElementType});
	}
	# Tempo di esecuzione di una giunzione
	if(defined $d->{outputCablePotentialPlanned}) {
		my $ret_tc = $self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO}->fetchall_arrayref($d->{networkElementType}, $d->{cablingType});
		if(scalar @{$ret_tc}) {
			$ret_tc->[0]->[0] =~ s/,/\./;
			$tot += $ret_tc->[0]->[0] * $d->{outputCablePotentialPlanned};
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType}", type => __("cabling type"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType}", key => "outputCablePotentialPlanned", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qv_GL {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# Tempo di esecuzione di una giunzione
	if(defined $d->{outputCablePotentialPlanned}) {
		my $ret_tc = $self->_prepare()->{GET_ESTIMATED_DURATION_TIPO_CABLAGGIO}->fetchall_arrayref($d->{networkElementType}, $d->{cablingType});
		if(scalar @{$ret_tc}) {
			$ret_tc->[0]->[0] =~ s/,/\./;
			$tot += $ret_tc->[0]->[0] * $d->{outputCablePotentialPlanned};
		} else {
			push @{$warnings}, __x("Bad configuration: unable to find estimated duration {type} in computing variable quote for {networkElementType}", type => __("cabling type"), networkElementType => $d->{networkElementType});
		}
	} else {
		push @{$warnings}, __x("{key} cannot be null in computing variable quote for {networkElementType}", key => "outputCablePotentialPlanned", networkElementType => $d->{networkElementType});
	}
	
	return ($tot, $warnings);
}

sub _qv_PTE {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# NOTA: non c'è quota variabile ma la funzione viene implementata comunque per individuare i casi non previsti

	return ($tot, $warnings);
}

sub _qv_PTA {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];
	
	# NOTA: non c'è quota variabile ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qv_CabinaEnel {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];

	# NOTA: non c'è quota variabile ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _qv_QuadroIP {
	my $self = shift;
	my $d = shift;
	my $tot = 0;
	my $warnings = [];

	# NOTA: non c'è quota variabile ma la funzione viene implementata comunque per individuare i casi non previsti
	
	return ($tot, $warnings);
}

sub _update_estimated_duration {
	my $self = shift;
	my $run_id = shift;
	
	my $data = $self->_prepare()->{GET_ALL_NODI}->fetchall_hashref($run_id);
	
	my $cur_neid = 0;
	my %tot = ();
	my %max_value = ();
	my %net_coerente = ();
	my %warnings = ();
	for(my $i=0; $i<@{$data}; $i++) {
		my $d = $data->[$i];
		if($cur_neid != $d->{networkElementId}) {
			$cur_neid = $d->{networkElementId};
			$tot{$d->{networkElementId}} = 0;
			$warnings{$d->{networkElementId}} = {};
			# precalcolo il valore max della durata stimata
			if($self->can('_qm_'.$d->{networkElementType})) {
				my $func = '_qm_'.$d->{networkElementType};
				my ($minutes, $w) = $self->$func($d);
				if(defined($minutes)) {
					# è stato possibile il valore max della durata stimata: lo memorizzo
					$max_value{$d->{networkElementId}} = $minutes;
					if(scalar(@{$w})) {
						# raccolgo gli warnings se presenti
						for my $warning (@{$w}) {
							$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
							$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
						}
					}
				} else {
					# errore fatale con last_error
					return undef;
				}
			} else {
				my $warning = __x("Handler for max estimation for networkElementType {networkElementType} not found", networkElementType => $d->{networkElementType});
				$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
				$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
			}
			# FIXME: calcolare anche la durata residua (metto solo i planned per la quota variabile, e calcolo la quota fissa solo se c'è almeno un planned)
			# se mancano dei valori che ci permettono di calcolare la durata stimata (bad configuration o dati mancanti) mettiamo estimatedDuration null e compiliamo il campo warnings
			# verifico che il campo networkElementType sia coerente per tutte le giunzioni/terminazioni
			my $ret_c = $self->_prepare()->{CHECK_COERENZA_NODI_NET}->fetchall_arrayref($run_id, $d->{networkElementId});
			$net_coerente{$ret_c->[0]->[0]} = $ret_c->[0]->[1];
			# calcolo la quota fissa
			# NOTA: la quota fissa si calcola solo se c'è almeno una giunzione workable
			if($d->{workable}) {
				unless($net_coerente{$d->{networkElementId}}) {
					# se ho trovato più di un networkElementType emetto un warning e passo alla successiva iterazione
					my $warning = __("Found more than one networkElementType for the same networkElementId");
					$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
					$warnings{$d->{networkElementId}}->{$warning} = 1;
					next;
				}
				if($self->can('_qf_'.$d->{networkElementType})) {
					my $func = '_qf_'.$d->{networkElementType};
					my ($minutes, $w) = $self->$func($d);
					if(defined($minutes)) {
						# è stato possibile calcolare la durata stimata: la sommo
						$tot{$d->{networkElementId}} += $minutes;
						if(scalar(@{$w})) {
							# raccolgo gli warnings se presenti
							for my $warning (@{$w}) {
								$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
								$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
							}
						}
					} else {
						# errore fatale con last_error
						return undef;
					}
				} else {
					my $warning = __x("Handler for fixed quote for networkElementType {networkElementType} not found", networkElementType => $d->{networkElementType});
					$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
					$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
				}
			}
		}
		# calcolo la quota variabile
		if($d->{status} eq 'Workable') {
			unless($net_coerente{$d->{networkElementId}}) {
				# se ho trovato più di un networkElementType ho già emesso un warning e passo alla successiva iterazione
				next;
			}
			if($self->can('_qv_'.$d->{networkElementType})) {
				my $func = '_qv_'.$d->{networkElementType};
				my ($minutes, $w) = $self->$func($d);
				if(defined($minutes)) {
					# è stato possibile calcolare la durata stimata: la sommo
					$tot{$d->{networkElementId}} += $minutes;
					if(scalar(@{$w})) {
						# raccolgo gli eventuali warnings
						for my $warning (@{$w}) {
							$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
							$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
						}
					}
				} else {
					# errore fatale con last_error
					return undef;
				}
			} else {
				my $warning = __x("Handler for variable quote for networkElementType {networkElementType} not found", networkElementType => $d->{networkElementType});
				$self->_logger()->warn(__x("networkElementId {networkElementId}: {warning}", networkElementId => $d->{networkElementId}, warning => $warning));
				$warnings{$d->{networkElementId}}->{$warning} = defined $warnings{$d->{networkElementId}}->{$warning} ? $warnings{$d->{networkElementId}}->{$warning} + 1 : 1;
			}
		}
	}
	
	$self->_db()->do("SAVEPOINT WPSOWORKS_SYNC_NODI_UPDESTDUR");
	
	for my $neid (keys %tot) {
		if($tot{$neid} > $max_value{$neid}) {
			# se il valore calcolato supera il valore max della durata stimata lo sostituisco con la durata massima
			$self->_logger()->debug(__x("Computed value for estimated duration ({estimated}) is greater than max allowed value ({max}): using max allowed value", estimated => $tot{$neid}, max => $max_value{$neid}));
			$tot{$neid} = $max_value{$neid};
		}
		# sostituisco il punto decimale (formato interno perl) con la virgola (formato per insert in oracle)
		$tot{$neid} =~ s/\./,/;
		my @w = ();
		if(keys %{$warnings{$neid}}) {
			for my $k (keys %{$warnings{$neid}}) {
				if($warnings{$neid}->{$k} == 1) {
					push @w, $k
				} else {
					push @w, __x("{warning} (repeated {n} times)", warning => $k, n => $warnings{$neid}->{$k});
				}
			}
		}
		my $warn;
		if(scalar @w) {
			local $YAML::UseHeader = 0;
			$warn = Dump({ __("The estimated duration cannot be fully calculated") => \@w })
		}
		unless ($self->_prepare()->{UPDATE_ESTIMATED_DURATION}->execute($tot{$neid}, $warn, $neid, $run_id)) {
			my $message = $self->_db()->get_errormessage();
			$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS_SYNC_NODI_UPDESTDUR");
			$self->_art()->last_error(__x("Unable to update estimated duration: {message}", message => $message));
			return undef;
		}
	}

	return 1;
}

# restituisce un nome file ...
# cadastralCode => ...
# pop => ...
# ring => ...
# networkElementId => ...
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anziché un nome file, default false
sub nodes_details {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 "cadastralCode"	=> { isa => 'SCALAR' }
					,"pop"				=> { isa => 'SCALAR' }
					,"ring"				=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
					 "networkElementId"	=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my @filters = ();
	if(defined $params{networkElementId}) {
		for my $networkElementId (@{$params{networkElementId}}) {
			push @filters, 'n."networkElementId" = '.$self->_db()->quote($networkElementId);
		}
	}

	# fallback nel caso in cui non si trovi alcun progetto che corrisponde ai criteri di ricerca
	my @query_projects_id = ('select null "projectId" from dual where 1=0');

	my $projects_coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->_art());
	return undef
		unless defined $projects_coll;
	
	# cerco i progetti che rispondono ai criteri di ricerca
	my $projects = $projects_coll->cerca(
		 "customerId"		=> $self->_get_customer_id()
		,"contractId"		=> $self->_get_contract_id()
		,"cadastralCode"	=> $params{"cadastralCode"}
		,"pop"			=> $params{"pop"}
		,"ring"			=> $params{"ring"}
	);
	return undef
		unless defined $projects;
	
	for my $project (@{$projects}) {
		my $p = $project->property();
		push @query_projects_id, sprintf(
			 'select %s "projectId" from dual'
			,$self->_db()->quote($p->{projectId})
		);
	}
	
	my $query = '
		select
		   sr."projectId"
		  ,( 
		    select dts4.valore
		    from v_Dt_Sistemi dts4
		    where dts4.id_sistema = s.id
		      and dts4.nome = \'pfpName\'
		  ) "pfpName"
		  ,n."networkElementId"
		  ,n."networkElementName"
		  ,n."networkElementType"
		  ,n."networkElementGeoLocation"
		  ,n."status"
		  ,n."cablingType"
		  ,n."inputCableName"
		  ,n."inputCablePotential"
		  ,n."inputCablePotentialPlanned"
		  ,n."outputCableName"
		  ,n."outputCablePotential"
		  ,n."outputCablePotentialPlanned"
		  ,case
		    when n."cablingType" = \'Splicing\' then "splicedFibers"
		    when n."cablingType" = \'Termination\' then "terminatedFibers"
		    else null
		  end "numFibersSplicedOrTerminated"
		from
		  NODI n
		    join sync_runs sr on sr."runId" = n."runId"
		    join v_dt_sistemi dt1 on dt1.nome = \'projectId\' and dt1.valore = to_char(sr."projectId")
		    join v_dt_sistemi dt2 on dt2.id_sistema = dt1.id_sistema and dt2.nome = \'customerId\' and sr."customerId" = dt2.valore
		    join v_dt_sistemi dt3 on dt3.id_sistema = dt1.id_sistema and dt3.nome = \'contractId\' and sr."contractId" = dt3.valore
		    join v_sistemi s on s.id = dt1.id_sistema
		where 1=1
		  and sr."syncType" = \'NODI\'
		  and sr."projectId" in ('.(join(' union ', @query_projects_id)).')
		  and sr."customerId" = '.$self->_db()->quote($self->_get_customer_id()).'
		  and sr."contractId" = '.$self->_db()->quote($self->_get_contract_id()).'
		  and n."historicizingRunId" is null
		  and s.tipo = \'PROJECT\'
		  and s.data_dismissione is null
		  and s.data_sospensione is null
		  '.(scalar @filters ? 'and ('.(join(' or ', @filters)).')' : '').'
		order by
		   2 -- "pfpName"
		  ,4 -- "networkElementName"
	';
	
	my $columns = [
		{ NAME => "projectId", HEADER => __("Project Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number", JSON_HIDDEN => 1 },
		{ NAME => "pfpName", HEADER => __("PFP"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementId", HEADER => __("Network Element Id"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "networkElementType", HEADER => __("Network Element Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementName", HEADER => __("Network Element Name"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "networkElementGeoLocation", HEADER => __("Network Element GeoLocation"), JSON_CHECKBOX_FILTER => 0, TYPE => "string", JSON_HIDDEN => 1 },
		{ NAME => "status", HEADER => __("Status"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "cablingType", HEADER => __("Type"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "inputCableName", HEADER => __("Input Cable Name"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "inputCablePotential", HEADER => __("Input Cable Potential (max)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "inputCablePotentialPlanned", HEADER => __("Input Cable Planned Fibers Number"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "outputCableName", HEADER => __("Output Cable Name"), JSON_CHECKBOX_FILTER => 0, TYPE => "string" },
		{ NAME => "outputCablePotential", HEADER => __("Output Cable Potential (max)"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "outputCablePotentialPlanned", HEADER => __("Output Cable Planned Fibers Number"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" },
		{ NAME => "numFibersSplicedOrTerminated", HEADER => __("Spliced/Terminated Fibers Number"), JSON_CHECKBOX_FILTER => 0, TYPE => "number" }
	];
	
	return $self->report(%params, QUERY => $query, COLUMNS => $columns);
	
}

###################

if (__FILE__ eq $0) {

	use API::ART;
	use WPSOWORKS;
	use JSON;
	
	my $log_level = 'INFO';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD},
			DEBUG => $ENV{ART_DB_DEBUG}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoworks = WPSOWORKS->new(ART => $art);
	
	my $mt;
	eval {
		$mt = WPSOWORKS::Sync::NODI->new(
			 WPSOWORKS => $wpsoworks
			,CUSTOMER_ID => 'ENEL'
			,CONTRACT_ID => 'FTTH'
		)
	};
	
	if ($@) {
		die "WPSOWORKS::Sync::NODI error: $@";
	}
	
	my $is_fatal = 0;
	if($mt->importa(
		 PROJECT_ID => 1028 # 1003 # 868
		,IS_FATAL => \$is_fatal
		,FORCE => 1
	)) {
		get_logger()->info("OK import giunzioni");
	} else {
		if($is_fatal) {
			get_logger()->fatal($art->last_error());
		} else {
			get_logger()->error($art->last_error());
		}
		die;
	}
	# print Dumper $art->_dbh()->fetchall_hashref("select * from NODI_RIEPILOGO");
	# $art->save();

	get_logger()->info("get_sinfo_modified_dates: ".Dumper($mt->get_sinfo_modified_dates(PROJECT_ID => 1028)));
	
	my $nodes_details = $mt->nodes_details(
		 "cadastralCode"		=> 'B354' # 'F839'
		,"pop"					=> '01 - CA-SELARGIUS' # '02 - NA-FUORIGROTTA' # '03 - NA-COLLI AMINEI'
		,"ring"					=> '03' # '01' # '78'
		#,"networkElementId"		=> [ 1001, 1002 ]
		,"OUTPUT_FORMAT"		=> 'xlsx'
		,JSON_RETURN_STRING		=> 0
	);
	if(defined $nodes_details) {
		get_logger()->info("OK report dettaglio: ".$nodes_details);
		# my $x = from_json($nodes_details);
		# get_logger()->info("Righe ritornate: ".(scalar(@{$x->{data}})));
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}
	
	my $report = $mt->progress_report(
		 "cadastralCode"		=> 'B354' # 'F839'
		,"pop"					=> '01 - CA-SELARGIUS' # '02 - NA-FUORIGROTTA' # '03 - NA-COLLI AMINEI'
		,"ring"					=> '03' # '01' # '78'
		,"OUTPUT_FORMAT"		=> 'xlsx'
	);
	if(defined $report) {
		get_logger()->info("OK report riepilogo: ".$report);
	} else {
		get_logger()->fatal($art->last_error());
		die;
	}

	$art->cancel();
	
	exit 0;
	
}

1;
