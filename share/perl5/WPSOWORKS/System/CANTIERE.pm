package WPSOWORKS::System::CANTIERE;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CHILDREN_CLASS = 'WPSOWORKS::System::MACROLAVORO';

use WPSOWORKS;
use WPSOWORKS::Collection::System::MACROLAVORO;

use base 'API::ART::System';

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
	
	$self->{ART} = $params->{ART};
	
	$self->{collMacroLavoro} = WPSOWORKS::Collection::System::MACROLAVORO->new(ART => $params->{ART});
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoworks {
	my $self = shift;
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->art()) unless defined $self->{WPSOWORKS};
	
	return $self->{WPSOWORKS};
}

sub _get_coll_macrolavoro { shift->{collMacroLavoro} }

sub end {
	my $self = shift;
	
	# in chiusura del cantiere chiuso anche tutte i sistemi figlio
	my $children = $self->get_children();
	return undef unless defined $children;
	
	for my $ch (@{$children}){
		return undef unless defined $ch->end();
	}	
	
	return $self->SUPER::end(@_);
}

sub add_macrotask{
	my $self = shift;
	my %params = @_;
	
	$self->_db()->do( "savepoint WPSOWORKS_Cll_CANT_addm" );
	
	# devo creare il sistema: le verifiche sono all'interno di ogni classe sistema
	my $sistema_macrolavoro = $self->_get_coll_macrolavoro->crea(%params);
	
	unless (defined $sistema_macrolavoro){
		$self->_db()->do( "rollback to savepoint WPSOWORKS_Cll_CANT_addm" );
		$self->_logger()->error( $self->art()->last_error() );
		return undef;
	}
	
	unless (defined $self->update_estimated_duration()){
		$self->_db()->do( "rollback to savepoint WPSOWORKS_Cll_CANT_addm" );
		$self->_logger()->error( $self->art()->last_error() );
		return undef;
	}
	
	return $sistema_macrolavoro;
}

sub update_estimated_duration{
	my $self = shift;
	
	$self->_db()->do( "savepoint WPSOWORKS_Cll_CANT_ued" );
	
	my $children = $self->get_children(SYSTEM_TYPE_NAME => ['MACROLAVORO']);
	return undef unless defined $children;
	
	my $totalEstimatedDuration=0;
	for my $c (@{$children}){
		$totalEstimatedDuration += $c->property('estimatedDuration');
	}

	
	# aggiorno la estimatedDuration
	unless ($self->set_property(
		PROPERTIES => {
			estimatedDuration => $totalEstimatedDuration
		}
	)) {
		$self->_db()->do( "rollback to savepoint WPSOWORKS_Cll_CANT_ued" );
		$self->_logger()->error( $self->art()->last_error() );
		return undef;
	}
	
	return 1;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $cantiere = WPSOWORKS::System::CANTIERE->new(ART => $art, ID => 2313);
	
	unless (defined $cantiere) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($cantiere));
	}
	
#	$art->save();
}

1;
