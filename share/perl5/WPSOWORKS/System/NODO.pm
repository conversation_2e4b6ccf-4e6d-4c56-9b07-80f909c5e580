package WPSOWORKS::System::NODO;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use WPSOWORKS;
#use WPSOWORKS::MQ::Sender::System::NODO;

use base 'API::ART::System';

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
	
	$self->{ART} = $params->{ART};
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoworks {
	my $self = shift;
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->art()) unless defined $self->{WPSOWORKS};
	
	return $self->{WPSOWORKS};
}


#sub get_sender {
#	my $self = shift;
#	$self->{SENDER} = WPSOWORKS::MQ::Sender::System::NODO->instance(DB => $self->_db());
#	return $self->{SENDER};
#}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $cavo = WPSOWORKS::System::NODO->new(ART => $art, ID => 2313);
	
	unless (defined $cavo) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($cavo));
	}
	
#	$art->save();
}

1;
