package WPSOWORKS::Stub;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};

	my $self = bless( {}, $class );

	# Controlli sui parametri
	
	die(__x('Missing mandatory param {paramname}', paramname => 'WPSOWORKS')) unless defined $params->{WPSOWORKS};
	die(__x('Param {paramname} must be of type {type}', paramname => 'WPSOWORKS', type => 'WPSOWORKS')) if ref($params->{WPSOWORKS}) ne 'WPSOWORKS';
	
	$self->{WPSOWORKS} = $params->{WPSOWORKS};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{PREPARE} = {
		FAKE_UPDATE => $self->_art()->_create_prepare(__PACKAGE__.'_FAKE_UPDATE', "
			update operatori set nome_operatore = nome_operatore where login_operatore = ?
		")
	};
	
	return $self;
}

sub _wpsoworks { shift->{WPSOWORKS} }

sub _art { shift->_wpsoworks()->art() }

sub _db { shift->_art()->_dbh() }

sub _logger { shift->{LOGGER} }

sub _prepare { shift->{PREPARE} }

sub fake_update {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 USERNAME	=> { isa => 'SCALAR' }
				}
				,OPTIONAL => {}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	$self->_db()->do("SAVEPOINT WPSOWORKS__Stub__FAKE_UPDATE");
	
	my @bind_params = (
		$params{USERNAME}
	);
	
	unless ($self->_prepare()->{FAKE_UPDATE}->execute(@bind_params)) {
		my $message = $self->_db()->get_errormessage();
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__Stub__FAKE_UPDATE");
		$self->_art()->last_error(__x("Error {action} {username}: {message}", action => __('updating'), username => $params{USERNAME}, message => $message));
		return undef;
	}
	
	return 1;
}

if (__FILE__ eq $0) {

	use API::ART;
	use WPSOWORKS;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoworks = WPSOWORKS->new(ART => $art);
	
	my $stub;
	eval {
		$stub = WPSOWORKS::Stub->new(
			WPSOWORKS => $wpsoworks
		)
	};
	
	if ($@) {
		die "WPSOWORKS::Stub error: $@";
	}
	
	if($stub->fake_update(USERNAME => 'PIPPO')) {
		get_logger()->info("OK");
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	$art->cancel();
}

1;
