package WPSOWORKS::<PERSON>ilo;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};

	my $self = bless( {}, $class );

	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub art { shift->{ART} }

sub _db { shift->art()->_dbh() }

sub _user { shift->art()->user() }

sub _logger { shift->{LOGGER} }

sub nome { shift->_user()->first_name() }

sub cognome { shift->_user()->last_name() }

sub email { shift->_user()->email() }

sub nome_utente { shift->_user()->name() }

sub id_utente { shift->_user()->id()*1 }

sub gruppi { 
	my $self = shift;
	
	my $groups = $self->_user()->groups();
	
	my @res = map { $self->art()->get_group_name($_) } @{$groups};
	
	return \@res; 
}

sub is_admin {
	my $self = shift;

	return $self->{IS_ADMIN} if defined $self->{IS_ADMIN};

	if ($self->_user()->is_admin()) {
		$self->{IS_ADMIN} = 1;
	} else {
		$self->{IS_ADMIN} = 0;
	}

	return $self->{IS_ADMIN};
}

sub is_progettista{
	my $self = shift;

	return $self->{IS_PROGETTISTA} if defined $self->{IS_PROGETTISTA};

	if ($self->_user()->is_admin()) {
		$self->{IS_PROGETTISTA} = 1;
	} else {
		$self->{IS_PROGETTISTA} = grep ($_ eq 'PROGETTISTA', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_PROGETTISTA};
}

sub is_progettista_cp{
	my $self = shift;

	return $self->{IS_PROGETTISTA_CP} if defined $self->{IS_PROGETTISTA_CP};

	if ($self->_user()->is_admin()) {
		$self->{IS_PROGETTISTA_CP} = 1;
	} else {
		$self->{IS_PROGETTISTA_CP} = grep ($_ eq 'PROGETTISTA_CP', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_PROGETTISTA_CP};
}

sub is_assistente_tecnico_civile{
	my $self = shift;

	return $self->{IS_ASSISTENTE_TECNICO_CIVILE} if defined $self->{IS_ASSISTENTE_TECNICO_CIVILE};

	if ($self->_user()->is_admin()) {
		$self->{IS_ASSISTENTE_TECNICO_CIVILE} = 1;
	} else {
		$self->{IS_ASSISTENTE_TECNICO_CIVILE} = grep ($_ eq 'AT_CIVILE', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_ASSISTENTE_TECNICO_CIVILE};
}

sub is_city_manager{
	my $self = shift;
	
	return $self->{IS_CITY_MANAGER} if defined $self->{IS_CITY_MANAGER};
	
	if ($self->_user()->is_admin()) {
		$self->{IS_CITY_MANAGER} = 1;
	} else {
		$self->{IS_CITY_MANAGER} = grep ($_ eq 'CITY_MANAGER', @{$self->gruppi()}) ? 1 : 0;
	}
	
	return $self->{IS_CITY_MANAGER};
}

sub is_area_manager{
	my $self = shift;
	
	return $self->{IS_AREA_MANAGER} if defined $self->{IS_AREA_MANAGER};
	
	if ($self->_user()->is_admin()) {
		$self->{IS_AREA_MANAGER} = 1;
	} else {
		$self->{IS_AREA_MANAGER} = grep ($_ eq 'AREA_MANAGER', @{$self->gruppi()}) ? 1 : 0;
	}
	
	return $self->{IS_AREA_MANAGER};
}

sub is_coordinatore_at_civile{
	my $self = shift;

	return $self->{IS_COORDINATORE_AT_CIVILE} if defined $self->{IS_COORDINATORE_AT_CIVILE};

	if ($self->_user()->is_admin()) {
		$self->{IS_COORDINATORE_AT_CIVILE} = 1;
	} else {
		$self->{IS_COORDINATORE_AT_CIVILE} = grep ($_ eq 'COORDINATORE_AT_CIVILE', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_COORDINATORE_AT_CIVILE};
}

sub is_coordinatore_at_giunzioni{
	my $self = shift;

	return $self->{IS_COORDINATORE_AT_GIUNZIONI} if defined $self->{IS_COORDINATORE_AT_GIUNZIONI};

	if ($self->_user()->is_admin()) {
		$self->{IS_COORDINATORE_AT_GIUNZIONI} = 1;
	} else {
		$self->{IS_COORDINATORE_AT_GIUNZIONI} = grep ($_ eq 'COORDINATORE_AT_GIUNZIONI', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_COORDINATORE_AT_GIUNZIONI};
}

sub is_assistente_tecnico_giunzioni{
	my $self = shift;

	return $self->{IS_ASSISTENTE_TECNICO_GIUNZIONI} if defined $self->{IS_ASSISTENTE_TECNICO_GIUNZIONI};

	if ($self->_user()->is_admin()) {
		$self->{IS_ASSISTENTE_TECNICO_GIUNZIONI} = 1;
	} else {
		$self->{IS_ASSISTENTE_TECNICO_GIUNZIONI} = grep ($_ eq 'AT_GIUNZIONI', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_ASSISTENTE_TECNICO_GIUNZIONI};
}

sub is_supporto_progettazione{
	my $self = shift;

	return $self->{IS_SUPPORTO_PROGETTAZIONE} if defined $self->{IS_SUPPORTO_PROGETTAZIONE};

	if ($self->_user()->is_admin()) {
		$self->{IS_SUPPORTO_PROGETTAZIONE} = 1;
	} else {
		$self->{IS_SUPPORTO_PROGETTAZIONE} = grep ($_ eq 'SUPPORTO_PROGETTAZIONE', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_SUPPORTO_PROGETTAZIONE};
}

sub is_referente_permessi_ap{
	my $self = shift;

	return $self->{IS_REFERENTE_PERMESSI_AP} if defined $self->{IS_REFERENTE_PERMESSI_AP};

	if ($self->_user()->is_admin()) {
		$self->{IS_REFERENTE_PERMESSI_AP} = 1;
	} else {
		$self->{IS_REFERENTE_PERMESSI_AP} = grep ($_ eq 'REFERENTE_PERMESSI_AP', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_REFERENTE_PERMESSI_AP};
}

sub is_referente_permessi_building{
	my $self = shift;

	return $self->{IS_REFERENTE_PERMESSI_BUILDING} if defined $self->{IS_REFERENTE_PERMESSI_BUILDING};

	if ($self->_user()->is_admin()) {
		$self->{IS_REFERENTE_PERMESSI_BUILDING} = 1;
	} else {
		$self->{IS_REFERENTE_PERMESSI_BUILDING} = grep ($_ eq 'REFERENTE_PERMESSI_BUILDING', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_REFERENTE_PERMESSI_BUILDING};
}

sub is_contabile{
	my $self = shift;

	return $self->{IS_CONTABILE} if defined $self->{IS_CONTABILE};

	if ($self->_user()->is_admin()) {
		$self->{IS_CONTABILE} = 1;
	} else {
		$self->{IS_CONTABILE} = grep ($_ eq 'CONTABILE', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_CONTABILE};
}

sub is_plan_and_program{
	my $self = shift;

	return $self->{IS_PLAN_AND_PROGRAM} if defined $self->{IS_PLAN_AND_PROGRAM};

	if ($self->_user()->is_admin()) {
		$self->{IS_PLAN_AND_PROGRAM} = 1;
	} else {
		$self->{IS_PLAN_AND_PROGRAM} = grep ($_ eq 'PLAN_AND_PROGRAM', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_PLAN_AND_PROGRAM};
}

sub is_quality_service_assurance{
	my $self = shift;

	return $self->{IS_QSA} if defined $self->{IS_QSA};

	if ($self->_user()->is_admin()) {
		$self->{IS_QSA} = 1;
	} else {
		$self->{IS_QSA} = grep ($_ eq 'QSA', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_QSA};
}

sub is_focal_point{
	my $self = shift;

	return $self->{FOCAL_POINT} if defined $self->{FOCAL_POINT};

	if ($self->_user()->is_admin()) {
		$self->{FOCAL_POINT} = 1;
	} else {
		$self->{FOCAL_POINT} = grep ($_ eq 'FOCAL_POINT', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{FOCAL_POINT};
}

sub is_coordinatore_delivery{
	my $self = shift;

	return $self->{COORDINATORE_DELIVERY} if defined $self->{COORDINATORE_DELIVERY};

	if ($self->_user()->is_admin()) {
		$self->{COORDINATORE_DELIVERY} = 1;
	} else {
		$self->{COORDINATORE_DELIVERY} = grep ($_ eq 'COORDINATORE_DELIVERY', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{COORDINATORE_DELIVERY};
}

sub is_assistente_tecnico{
	my $self = shift;

	return $self->{IS_AT} if defined $self->{IS_AT};

	if ($self->_user()->is_admin()) {
		$self->{IS_AT} = 1;
	} else {
		$self->{IS_AT} = grep ($_ =~/^(AT|AT03)$/, @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_AT};
}

sub is_project_manager{
	my $self = shift;

	return $self->{IS_PM} if defined $self->{IS_PM};

	if ($self->_user()->is_admin()) {
		$self->{IS_PM} = 1;
	} else {
		$self->{IS_PM} = grep ($_ eq 'PM', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_PM};
}

sub is_regional_manager{
	my $self = shift;

	return $self->{IS_REGIONAL_MANAGER} if defined $self->{IS_REGIONAL_MANAGER};

	if ($self->_user()->is_admin()) {
		$self->{IS_REGIONAL_MANAGER} = 1;
	} else {
		$self->{IS_REGIONAL_MANAGER} = grep ($_ eq 'REGIONAL_MANAGER', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_REGIONAL_MANAGER};
}

sub is_service{
	my $self = shift;

	return $self->{IS_SERVICE} if defined $self->{IS_SERVICE};

	if ($self->_user()->is_admin()) {
		$self->{IS_SERVICE} = 1;
	} else {
		$self->{IS_SERVICE} = grep ($_ eq 'SERVICE', @{$self->gruppi()}) ? 1 : 0;
	}

	return $self->{IS_SERVICE};
}

# per ora metto solo il mobile_phone
sub telefoni {
	my $self = shift;
	my $ret = [];
	if(defined $self->_user()->mobile_phone()) {
		push @{$ret}, $self->_user()->mobile_phone();
	}
	return $ret;
}

sub ruolo_aziendale {
	my $self = shift;
	# NB: per convenzione il ruolo aziendale è salvato nel campo FAX della tabella operatori, no comment
	# FIXME: implementare la gestione del fax nelle API::ART
	return $self->_db()->fetch_minimalized("select FAX from operatori where login_operatore = ".$self->_db()->quote($self->nome_utente()));
}

sub societa_appartenenza {
    my $self = shift;

    unless ( exists $self->{SOCIETA_APPARTENENZA} ) {
        my $ret;
        my $gruppi = $self->gruppi;
        LG: for my $gruppo ( @$gruppi ) {
            if ( $gruppo =~ m{^USER_(\w{2}(\d{2}))} ) {
                $ret->{companyAbbreviation} = $1;
                $ret->{companyCode}         = $2;
                last LG;
            }
        }
        unless (defined $ret) {
            $self->art->last_error(__("User without company's association"));
            return;
        }
        $self->{SOCIETA_APPARTENENZA} = $ret;
    }

    return $self->{SOCIETA_APPARTENENZA};
}


if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $profilo;
	eval {
		$profilo = WPSOWORKS::Profilo->new(
			ART => $art
		)
	};
	
	if ($@) {
		die "WPSOWORKS::Profilo error: $@";
	}
	
	get_logger()->info("nome: ".$profilo->nome());
	get_logger()->info("cognome: ".$profilo->cognome());
	get_logger()->info("email: ".$profilo->email());
	get_logger()->info("nome_utente: ".$profilo->nome_utente());
	get_logger()->info("gruppi: ".Dumper($profilo->gruppi()));
	get_logger()->info("telefoni: ".Dumper($profilo->telefoni()));
	get_logger()->info("is_progettista: ".$profilo->is_progettista());
	get_logger()->info("is_progettista_cp: ".$profilo->is_progettista_cp());
	get_logger()->info("is_assistente_tecnico_civile: ".$profilo->is_assistente_tecnico_civile());
	get_logger()->info("is_city_manager: ".$profilo->is_city_manager());
	get_logger()->info("is_area_manager: ".$profilo->is_area_manager());
    get_logger()->info("is_coordinatore_assistente_tecnico_civile: ".$profilo->is_coordinatore_at_civile());
    get_logger()->info("is_coordinatore_assistente_tecnico_giunzioni: ".$profilo->is_coordinatore_at_giunzioni());
    get_logger()->info("is_assistente_tecnico_giunzioni: ".$profilo->is_assistente_tecnico_giunzioni());
    get_logger()->info("is_supporto_progettazione: ".$profilo->is_supporto_progettazione());
	get_logger()->info("is_referente_permessi_ap: ".$profilo->is_referente_permessi_ap());
    get_logger()->info("is_referente_permessi_building: ".$profilo->is_referente_permessi_building());
    get_logger()->info("is_contabile: ".$profilo->is_contabile());
	get_logger()->info("is_plan_and_program: ".$profilo->is_plan_and_program());
    get_logger()->info("is_quality_service_assurance: ".$profilo->is_quality_service_assurance());
	get_logger()->info("is_focal_point: ".$profilo->is_focal_point());
	get_logger()->info("is_coordinatore_delivery: ".$profilo->is_coordinatore_delivery());
    get_logger()->info("societa_appartenenza: ".Dumper($profilo->societa_appartenenza));
}

1;
