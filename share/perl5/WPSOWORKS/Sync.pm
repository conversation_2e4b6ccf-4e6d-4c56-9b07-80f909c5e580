package WPSOWORKS::Sync;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use SIRTI::Reports;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};

	my $self = bless( {}, $class );

	# Controlli sui parametri
	
	die(__x('Missing mandatory param {paramname}', paramname => 'WPSOWORKS')) unless defined $params->{WPSOWORKS};
	die(__x('Param {paramname} must be of type {type}', paramname => 'WPSOWORKS', type => 'WPSOWORKS')) if ref($params->{WPSOWORKS}) ne 'WPSOWORKS';
	
	die(__x('Missing mandatory param {paramname}', paramname => 'CUSTOMER_ID')) unless defined $params->{CUSTOMER_ID};
	die(__x('Param {paramname} must be of type {type}', paramname => 'CUSTOMER_ID', type => 'SCALAR')) if ref($params->{CUSTOMER_ID});
	
	die(__x('Missing mandatory param {paramname}', paramname => 'CONTRACT_ID')) unless defined $params->{CONTRACT_ID};
	die(__x('Param {paramname} must be of type {type}', paramname => 'CONTRACT_ID', type => 'SCALAR')) if ref($params->{CONTRACT_ID});
	
	die(__x('Missing mandatory param {paramname}', paramname => 'SYNC_TYPE')) unless defined $params->{SYNC_TYPE};
	die(__x('Param {paramname} must be of type {type}', paramname => 'SYNC_TYPE', type => 'SCALAR')) if ref($params->{SYNC_TYPE});
	
	$self->{WPSOWORKS} = $params->{WPSOWORKS};
	
	$self->{CUSTOMER_ID} = $params->{CUSTOMER_ID};
	$self->{CONTRACT_ID} = $params->{CONTRACT_ID};
	
	$self->{SYNC_TYPE} = $params->{SYNC_TYPE};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{SIRTI_REPORTS} = SIRTI::Reports->instance(DB => $self->_db());
	
	my $sql = "
		select column_name
		from all_tab_columns at
			join user_synonyms us on us.table_name = at.table_name
			join user_synonyms us2 on us2.table_owner = at.owner
		where 1=1
		  and us.synonym_name = ".$self->_db()->quote($self->{SYNC_TYPE})."
		  and us2.synonym_name = us.synonym_name
		  and column_name not in ('runId','historicizingRunId')
		order by column_id
	";
	my $columns = '"'.join('","', $self->_db()->fetch_minimalized($sql)).'"';
	
	$self->{PREPARE} = {
		 INSERT_RUN_ID => $self->_art()->_create_prepare(__PACKAGE__.'_INSERT_RUN_ID', qq/
			INSERT
			INTO SYNC_RUNS
			  (
			    "runId",
			    "customerId",
			    "contractId",
			    "projectId",
			    "runDate",
			    "sinfoLastModified",
			    "syncType"
			  )
			  VALUES
			  (
			    ?,
			    ?,
			    ?,
			    ?,
			    to_date(?, ?),
			    to_date(?, ?),
			    ?
			  )
		/)
		,GET_SINFO_MODIFIED_DATES => $self->_art()->_create_prepare(__PACKAGE__.'_GET_SINFO_MODIFIED_DATES_'.$self->{SYNC_TYPE}, qq{
			select to_char(sinfoLastModified, ?) sinfoModifiedDates from (
			  select
			     distinct mtr."sinfoLastModified" sinfoLastModified
			  from $self->{SYNC_TYPE} mt
			    join SYNC_RUNS mtr on mtr."runId" = mt."runId" and mtr."syncType" = ?
			  where 1=1
			    and mtr."customerId" = ?
			    and mtr."contractId" = ?
			    and mtr."projectId" = ?
			  order by 1 desc
			)
			where 1=1
			and rownum < ?
		})
		,HISTORICIZE_DATA => $self->_art()->_create_prepare(__PACKAGE__.'_HISTORICIZE_DATA_'.$self->{SYNC_TYPE}, qq/
			UPDATE $self->{SYNC_TYPE}
			SET "historicizingRunId" = ?
			WHERE "runId" = (
			  select max(m."runId")
			  from
			    $self->{SYNC_TYPE} m
			      join SYNC_RUNS mr on mr."runId" = m."runId" and mr."syncType" = ?
			  where 1=1
			    and m."historicizingRunId" is null
			    and mr."customerId" = ?
			    and mr."contractId" = ?
			    and mr."projectId" = ?
			)
		/),
		,CHECK_PREVIOUS_RUN_DIFF => $self->_art()->_create_prepare(__PACKAGE__.'_CHECK_PREVIOUS_RUN_DIFF_'.$self->{SYNC_TYPE}, qq/
			select count(1) from (
			  (
			    select $columns from $self->{SYNC_TYPE} where "runId" = ?
			    minus
			    select $columns from $self->{SYNC_TYPE} where "historicizingRunId" = ?
			  )
			  union
			  (
			    select $columns from $self->{SYNC_TYPE} where "historicizingRunId" = ?
			    minus
			    select $columns from $self->{SYNC_TYPE} where "runId" = ?
			  )
			)
		/)
	};
	
	return $self;
}

sub _wpsoworks { shift->{WPSOWORKS} }

sub _art { shift->_wpsoworks()->art() }

sub _db { shift->_art()->_dbh() }

sub _logger { shift->{LOGGER} }

sub _prepare { shift->{PREPARE} }

sub _sirti_reports { shift->{SIRTI_REPORTS} }

sub _get_customer_id { shift->{CUSTOMER_ID} }

sub _get_contract_id { shift->{CONTRACT_ID} }

sub _get_sync_type { shift->{SYNC_TYPE} }

# inserisce le informazioni sull'ultimo campionamento
# PROJECT_ID => Id del progetto
# RUN_ID => data in cui è stata invocata la rotta su Sinfo in formato API::ART::DEFAULT_DATE_FORMAT
# SINFO_LAST_UPDATE => data di ultima modifica comunicata da SINFO tramite chiamata alla rotta
#   HEAD /sirti/api/private/sinfo/customers/{customerId}/contracts/{contractId}/design/projects/{projectId}/{sync_type}
#   in formato API::ART::DEFAULT_DATE_FORMAT
sub _sample_run {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,RUN_DATE	=> { isa => 'SCALAR' }
					,SINFO_LAST_UPDATE => { isa => 'SCALAR' }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $run_id = $self->_db->fetch_minimalized("select seq_SYNC_RUNS.nextval from dual");
	
	my @bind_params = (
		 $run_id
		,$self->_get_customer_id
		,$self->_get_contract_id
		,$params{PROJECT_ID}
		,$params{RUN_DATE}
		,$self->_art()->get_default_date_format()
		,$params{SINFO_LAST_UPDATE}
		,$self->_art()->get_default_date_format()
		,$self->_get_sync_type
	);
	
	unless ($self->_prepare()->{INSERT_RUN_ID}->execute(@bind_params)) {
		my $message = $self->_db()->get_errormessage();
		$self->_art()->last_error(__x("Unable to insert sample run: {message}", message => $message));
		return undef;
	}
	
	return $run_id;
}

# restituisce la data di ultima modifica su Sinfo delle informazioni già campionate
# in formato API::ART::DEFAULT_DATE_FORMAT
# al primo run restituirebbe null, per semplificare la gestione faccio restituire 1/1/1970
# PROJECT_ID => Id del progetto
sub _get_sinfo_last_update {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $dates = $self->get_sinfo_modified_dates(PROJECT_ID => $params{PROJECT_ID}, LIMIT => 1);
	push @{$dates}, $self->_art()->format_date('19700101000000','YYYYMMDDHH24MISS')
		unless scalar @{$dates};
	return $dates->[0];
}

# inserisce le informazioni sull'ultimo campionamento
# PROJECT_ID => Id del progetto
# RUN_ID => data in cui è stata invocata la rotta su Sinfo in formato API::ART::DEFAULT_DATE_FORMAT
# DATA => informazioni come documentate nella rotta
#   GET /sirti/api/private/sinfo/customers/{customerId}/contracts/{contractId}/design/projects/{projectId}/{sync_type}
# FORCE: indica di salvare i dati anche se sono uguali al run precedente
# se inserisce i dati ritorna 1, se non li inserisce in quanto sono uguali ai precedenti ritorna 0, in caso di errore ritorna undef
sub _do_work {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,RUN_ID	=> { isa => 'SCALAR' }
					,PREPARE_ID => { isa => 'SCALAR' }
					,BIND_PARAMS => { isa => 'ARRAY' }
					,FORCE		=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,OPTIONAL => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	$self->_db()->do("SAVEPOINT WPSOWORKS__SYNFO_SYNC__DOWORK");
	
	my @h_bind_params = (
		 $params{RUN_ID}
		,$self->_get_sync_type
		,$self->_get_customer_id
		,$self->_get_contract_id
		,$params{PROJECT_ID}
	);
	
	unless ($self->_prepare()->{HISTORICIZE_DATA}->execute(@h_bind_params)) {
		my $message = $self->_db()->get_errormessage();
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNFO_SYNC__DOWORK");
		$self->_art()->last_error(__x("Unable to historicize {what}: {message}", what => $self->_get_sync_type, message => $message));
		return undef;
	}
	
	for my $bind_params (@{$params{BIND_PARAMS}}) {
		
		unless ($self->_prepare()->{$params{PREPARE_ID}}->execute(@{$bind_params})) {
			my $message = $self->_db()->get_errormessage();
			$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNFO_SYNC__DOWORK");
			$self->_art()->last_error(__x("Unable to insert data: {message}", message => $message));
			return undef;
		}
	
	}
	
	my $ret = $self->_prepare()->{CHECK_PREVIOUS_RUN_DIFF}->fetchall_arrayref($params{RUN_ID}, $params{RUN_ID}, $params{RUN_ID}, $params{RUN_ID});
	unless (defined $ret) {
		my $message = $self->_db()->get_errormessage();
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNFO_SYNC__DOWORK");
		$self->_art()->last_error(__x("Unable to check previous run: {message}", message => $message));
		return undef;
	}
	
	if(!$params{FORCE} && $ret->[0]->[0] == 0) {
		# i dati sono uguali al run precedente
		# non faccio nulla a meno che è impostato il parametro FORCE
		$self->_logger()->debug(__("Data are the same than previous run: nothing to insert"));
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNFO_SYNC__DOWORK");
		return 0;
	}
	
	return 1;
}

# Importa i dati relativi al progetto
# PROJECT_ID => Id del progetto
# IS_FATAL => puntatore a uno scalare che verrà valorizzato con 0 se l'errore non è fatale, 1 altrimenti
# FORCE => indica di salvare i dati anche se sono uguali al run precedente
# DATA => serve per importare un set di dati già noto. Deve essere un HASH con le chiavi SINFO_LAST_MODIFIED e DATA
sub importa {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,IS_FATAL	=> { isa => 'SCALAR' }
				}
				,OPTIONAL => {
					 FORCE	=> { isa => 'SCALAR', list => [ 0, 1 ] }
					 ,DATA => { isa => 'HASH'}
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	$params{FORCE} = 0
		unless defined $params{FORCE};
	
	${$params{IS_FATAL}} = 0;
	
	my $sysdate = $self->_art()->get_sysdate();
	
	$self->_logger()->debug(__('Fetching Sinfo last modified date for the project'));
	my @ret;
	
	if (defined $params{DATA}){
		$ret[0] = 1; # imposto per superare check
		$ret[2]->{"last-modified"} = $params{DATA}->{SINFO_LAST_MODIFIED};
	} else {
		@ret = $self->_wpsoworks()->invoke_sinfo_rest(
			 RESOURCE => sprintf($ENV{"SINFO_".$self->_get_sync_type()."_RESOURCE"}, $self->_get_customer_id(), $self->_get_contract_id(), $params{PROJECT_ID})
			,IS_FATAL => $params{IS_FATAL}
			,METHOD => 'HEAD'
		);
	}
	
	return undef
		unless defined $ret[0];
	
	my $last_modified = eval { $self->_art()->get_date_from_rfc7232_date($ret[2]->{"last-modified"}); };
	if($@) {
		$self->_art()->last_error(__x("{date} is not a valid rfc7232 date", date => $ret[2]->{"last-modified"}));
		return undef;
	}
	
	$self->_db()->do("SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
	
	$self->_logger()->debug(__('Inserting sample entry'));
	my $run_id = $self->_sample_run(PROJECT_ID => $params{PROJECT_ID}, RUN_DATE => $sysdate, SINFO_LAST_UPDATE => $last_modified);
	unless(defined $run_id) {
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
		return undef;
	}

	$self->_logger()->debug(__('Verifing if Sinfo last modified date is greater than our last sample enty'));
	if($params{FORCE} || $self->_art()->format_date($last_modified, 'YYYYMMDDHH24MISS') gt $self->_art()->format_date($self->_get_sinfo_last_update(PROJECT_ID => $params{PROJECT_ID}), 'YYYYMMDDHH24MISS')) {
		
		$self->_logger()->info(__('Sinfo last modified date is greater than our last sample entry or force enabled: fetching data from Sinfo'));
		
		my $ret = [];
		
		if (defined $params{DATA}){
			$ret = $params{DATA}->{DATA};
		} else {
			$ret = $self->_wpsoworks()->invoke_sinfo_rest(
				 RESOURCE => sprintf($ENV{"SINFO_".$self->_get_sync_type()."_RESOURCE"}, $self->_get_customer_id(), $self->_get_contract_id(), $params{PROJECT_ID})
				,IS_FATAL => $params{IS_FATAL}
				,METHOD => 'GET'
			);
		}
		
		unless(defined $ret) {
			$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
			return undef;
		}
		
		$self->_logger()->info(__x('Getting {what} info from Sinfo', what => $self->_get_sync_type()));
		my $done_work = $self->_do_work(PROJECT_ID => $params{PROJECT_ID}, RUN_ID => $run_id, DATA => $ret, FORCE => $params{FORCE});
		unless(defined $done_work) {
			$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
			return undef;
		}

		# se _do_work ritorna 0 vuol dire che i dati sono uguali al run precedente
		# non salvo il run a meno che è impostato il parametro FORCE
		if(!$params{FORCE} && $done_work == 0) {
			$self->_logger()->info(__("Data are the same than previous run: I don't save sample run"));
			$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
		}

	} else {
		$self->_db()->do("ROLLBACK TO SAVEPOINT WPSOWORKS__SYNC_TYPE__IMT");
		$self->_logger()->info(__('Sinfo last modified date is not greater than our last sample entry: nothing to do'));
	}
	
	return 1;
}

# restituisce le date degli ultimi aggiornamenti su Sinfo relativi al progetto in ordine decrescente
# PROJECT_ID => Id del progetto
# LIMIT => restituisce solo le ultime entry
sub get_sinfo_modified_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROJECT_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,OPTIONAL   => {
					 LIMIT	=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$params{LIMIT} = 5 unless defined $params{LIMIT};
	
	my @bind_params = (
		 $self->_art()->get_default_date_format()
		,$self->_get_sync_type
		,$self->_get_customer_id
		,$self->_get_contract_id
		,$params{PROJECT_ID}
		,$params{LIMIT}+1
	);
	
	return [ map { $_->[0] } @{$self->_prepare()->{GET_SINFO_MODIFIED_DATES}->fetchall_arrayref(@bind_params)} ];
}

# restituisce un nome file dove sono elencati i dati relativi al report
# OUTPUT_FORMAT => formato a scelta tra json, xlsx e csv, default json
# DIR => directory temporanea dove salvare il file, default $TMPDIR
# JSON_RETURN_STRING => se richiesto formato json restituisce una stringa json anziché un nome file, default false
sub report {
	my $self = shift;
	return $self->_wpsoworks()->report(@_);
}

1;
