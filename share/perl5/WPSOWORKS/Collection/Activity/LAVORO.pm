package WPSOWORKS::Collection::Activity::LAVORO;

use strict;
use warnings;

use JSON;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use MIME::Base32;

use API::ART::Collection::Activity;
use WPSOWORKS;
use WPSOWORKS::Collection::System::CAVO;
use WPSOWORKS::Collection::System::NODO;
use WPSOWORKS::Collection::System::CANTIERE;
use WPSOWORKS::Collection::System::LAVORO;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{CollSystemCavo} = WPSOWORKS::Collection::System::CAVO->new(ART => $self->{ART});
	
	$self->{CollSystemNodo} = WPSOWORKS::Collection::System::NODO->new(ART => $self->{ART});
	
	$self->{CollSystemCantiere} = WPSOWORKS::Collection::System::CANTIERE->new(ART => $self->{ART});
	
	$self->{CollSystemLavoro} = WPSOWORKS::Collection::System::LAVORO->new(ART => $self->{ART});
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_system_cavo{shift->{CollSystemCavo}}

sub _get_coll_system_nodo{shift->{CollSystemNodo}}

sub _get_coll_system_cantiere{shift->{CollSystemCantiere}}

sub _get_coll_system_lavoro{shift->{CollSystemLavoro}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_wpsoworks{shift->{WPSOWORKS}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $profilo = $self->_get_wpsoworks()->profilo();
	return undef unless defined $profilo;
	
	#verifica sugli operatori abilitati all'apertura di un'attività di LAVOR
	if (
		## gruppi OpenFiber
		! $profilo->is_assistente_tecnico_civile
		&&
		! $profilo->is_coordinatore_at_civile
		&&
		! $profilo->is_assistente_tecnico_giunzioni
		&&
		! $profilo->is_coordinatore_at_giunzioni
		&&
		! $profilo->is_plan_and_program
		## gruppi FC
		&&
		! $profilo->is_assistente_tecnico
	) {
		$self->_art()->last_error(__("User is not enabled to open the activity"));
		return undef;
	}
	
	# prime verifico i parametri uguali per tutte le gestioni
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"workType"			=> { isa => 'SCALAR', list => ['Civil', 'Cable', 'Splice', 'Network', 'NetworkFibercop', 'ROE', 'PreeMaintenance','CorrMaintenance', 'ExtrMaintenance', 'PTE', 'PreeMaintenance01', 'ExtrMaintenance01']}
					,"workingGroupCode"	=> { isa => 'SCALAR' }
					,"details"			=> { isa => 'ARRAY' }
				}
				,OPTIONAL	=> {
					"maker"							=> { isa => 'SCALAR', list => ['Team', 'Subcontract'] }
					,"team"							=> { isa => 'HASH' }
					,"subcontractInfo"				=> { isa => 'HASH' }
					,"description"					=> { isa => 'SCALAR' }
					,"reference"					=> { isa => 'HASH' }
					,"invalidatePreviousReferences"	=> { isa => 'SCALAR', list => [ 0, 1 ] }
					,"accountingOperation"			=> { isa => 'SCALAR', list => [ 0, 1 ] }
					,"cannotCloseWork"				=> { isa => 'SCALAR', list => [ 0, 1 ] }
					,"cannotCloseWorkReason"		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	unless (scalar @{$params{"details"}}){
		$self->_art()->last_error(__x("Param {param} can not be empty", param => 'details'));
		return undef;
	}

	if ($params{accountingOperation} && $params{workType} !~/^(Network|NetworkFibercop)$/){
		$self->_art()->last_error(__x("Accounting operation not available for {workType}", workType => $params{workType}));
		return undef;
	}

	$params{reference} = {} unless exists $params{reference};

	my %create_params = (
		ACTIVITY_TYPE_NAME		=> 'LAVORO'
		, PROPERTIES			=> {
			"customerId"		=> $params{"customerId"}
			,"contractId"		=> $params{"contractId"}
			,"workingGroupCode"	=> $params{"workingGroupCode"}
		}
	);

	for ('cannotCloseWork', 'cannotCloseWorkReason'){
		$create_params{PROPERTIES}->{$_} = $params{$_} if defined $params{$_};
	}

	# verifico se il lavoro è integrato con il fieldService (solo se non è un operazione contabile)
	unless ($params{accountingOperation}){
		my $fieldService = $self->_get_wpsoworks()->get_field_service(
			customerId	=> $params{customerId},
			contractId	=> $params{contractId},
			maker		=> $params{maker},
			workType	=> $params{workType},
			details		=> $params{details}
		);

		return undef
			unless defined $fieldService;

		if (exists $fieldService->{NAME}){
			$create_params{PROPERTIES}->{fieldService} = $fieldService->{NAME};
		}  else {
			# se non è integrato con fieldService è obbligatorio che ci sia il teamId
			if ($params{maker} eq 'Team' && !defined $params{team}->{teamId}){
				$self->_art()->last_error(__("It is not possible to assign work to a generic working group code"));
				return undef;
			}
		}
	} else { # se è un operazione contabile è obbligatorio che ci sia la squadra o il subappalto
		if ($params{maker} eq 'Team' && !defined $params{team}->{teamId}){
			$self->_art()->last_error(__("It is not possible to assign an accounting operation without team"));
			return undef;
		}
		if ($params{maker} eq 'Subcontract' && !defined $params{subcontractInfo}->{subContractCode}){
			$self->_art()->last_error(__("It is not possible to assign an accounting operation without subcontract"));
			return undef;
		}
	}

	for my $ref (keys %{$params{reference}}){
		$create_params{PROPERTIES}->{$ref} = $params{reference}->{$ref};
	}
	
	if ($params{workType} =~ /^(Civil|Cable|Splice)$/){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"cadastralCode"	=> { isa => 'SCALAR' }
						,"popId"		=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		#decodifico popId
		$params{"pop"} = decode_base32($params{"popId"});
		
		$create_params{PROPERTIES}->{cadastralCode} = $params{"cadastralCode"};
		$create_params{PROPERTIES}->{pop} = $params{"pop"};
		$create_params{PROPERTIES}->{popId} = $params{"popId"};
	}

	if ($params{workType} =~ /^(Network|NetworkFibercop|ROE|PreeMaintenance|CorrMaintenance|ExtrMaintenance|PTE)$/){
		$create_params{PROPERTIES}->{accountingOperation} = $params{"accountingOperation"};
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"flagFIR"	=> { isa => 'SCALAR', list => [0, 1] }
					}
					,IGNORE_EXTRA_PARAMS => 1
			);
			
		if ($params{workType} =~ /^(Network|NetworkFibercop|ROE|PTE)$/){
			if (!defined $params{"assetId"}){
				$self->_art()->last_error(__x("Missing mandatory param {param}", param => 'assetId'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			if ($params{workType} =~/^(Network|NetworkFibercop|PTE)$/ && ref ($params{"assetId"}) eq 'ARRAY'){
				$self->_art()->last_error(__x("For {param} {value} {param1} can have only one value", param => 'workType', value => $params{workType}, param1 => 'assetId'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			if ($params{workType} =~ /^(ROE)$/){
				$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							 ERRMSG		=> \$errmsg
							,PARAMS		=> \%params
							,MANDATORY	=> {
								"assetId"	=> { isa => 'ARRAY' },
								"oneForAll"	=> { isa => 'SCALAR', list => [0,1] },
							}
							,OPTIONAL	=> {}
							,IGNORE_EXTRA_PARAMS => 1
					);
				# verifico che tutti gli id passati siano effettivamente solo numerici
				for my $as (@{$params{assetId}}){
					$self->_art()->last_error(__x("assetId must be an integer"))
						&& return undef
							if $as !~ /^\d+$/;
				}
			} elsif ($params{workType} =~ /^(PTE)$/){
				$create_params{PROPERTIES}->{invalidatePreviousReferences} = $params{invalidatePreviousReferences}
					if $params{invalidatePreviousReferences};
			}
		} elsif ($params{workType} =~ /^(PreeMaintenance|CorrMaintenance|ExtrMaintenance|PreeMaintenance01|ExtrMaintenance01)$/){
			### FIXME: capire anche gestione metadati per allegati
			if (!defined $params{"maintenanceId"}){
				$self->_art()->last_error(__x("Missing mandatory param {param}", param => 'maintenanceId'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		}
	}
	
	if ($params{workType} =~ /^(Civil|Network|NetworkFibercop|PreeMaintenance|CorrMaintenance|ExtrMaintenance|PTE|PreeMaintenance01|ExtrMaintenance01)$/){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"description"	=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {}
					,IGNORE_EXTRA_PARAMS => 1
			);
	} elsif ($params{workType} =~ /^(ROE)$/){
		if (!defined $params{description} && !defined $params{assetDescription}){
			$self->_art()->last_error(__x("At least one param between {param} and {param1} must be defined", param => 'description', param1 => 'assetDescription'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		} elsif (defined $params{description} && defined $params{assetDescription}){
			$self->_art()->last_error(__x("Only one param between {param} and {param1} must be defined", param => 'description', param1 => 'assetDescription'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		} elsif (defined $params{description}){
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"description"	=> { isa => 'SCALAR' }
						}
						,OPTIONAL	=> {}
						,IGNORE_EXTRA_PARAMS => 1
				);
		} else { # defined $params{assetDescription}
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"assetDescription"	=> { isa => 'ARRAY' }
						}
						,OPTIONAL	=> {}
						,IGNORE_EXTRA_PARAMS => 1
				);
			# se passata l'assetDescription deve esserci un valore per ogni assetId
			if (scalar @{$params{assetDescription}} != scalar @{$params{assetId}}){
				$self->_art()->last_error(__x("Params {param} and {param1} must have the same size", param => 'assetId', param1 => 'assetDescription'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		}
	}
	
	$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
	
	my @results;
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
	
	my $collection;
	
	# se e' nella gestione FC creo l'attività sul sistema di tipo LAVORO
	if ($params{workType} eq 'Cable'){
		$collection = $self->_get_coll_system_cavo();
	} elsif ($params{workType} eq 'Splice'){
		$collection = $self->_get_coll_system_nodo();
	} elsif ($params{workType} eq 'Civil'){
		$collection = $self->_get_coll_system_cantiere();
	} elsif ($params{workType} =~ /^(Network|NetworkFibercop|ROE|PreeMaintenance|CorrMaintenance|ExtrMaintenance|PTE|PreeMaintenance01|ExtrMaintenance01)$/){
		$collection = $self->_get_coll_system_lavoro();
	}
	
	# verifico che il maker sia quello conosciuto
	if (defined $params{maker}){
	
		if ($params{maker} eq 'Team'){
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"team"	=> { isa => 'HASH' }
						}
						,OPTIONAL	=> {}
						,IGNORE_EXTRA_PARAMS => 1
			);
			
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%{$params{team}}
						,MANDATORY	=> {
						}
						,OPTIONAL	=> {
							"slaStart"	=> { isa => 'SCALAR' },
							"slaEnd"	=> { isa => 'SCALAR' }
						}
						,IGNORE_EXTRA_PARAMS => 1
			);
			$create_params{PROPERTIES}->{maker} = $params{maker};
			for ('slaStart','slaEnd') {
				$create_params{PROPERTIES}->{$_} = $params{team}->{$_} if defined $params{team}->{$_};
			}
		} else {
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"subcontractInfo"	=> { isa => 'HASH' }
						}
						,OPTIONAL	=> {}
						,IGNORE_EXTRA_PARAMS => 1
			);
			
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%{$params{subcontractInfo}}
						,MANDATORY	=> {
							"subContractName"	=> { isa => 'SCALAR' }
							,"subContractCode"	=> { isa => 'SCALAR' }
						}
						,OPTIONAL	=> {
							"startPlannedDate"	=> { isa => 'SCALAR' }
							,"endPlannedDate"	=> { isa => 'SCALAR' }
						}
						,IGNORE_EXTRA_PARAMS => 1
			);
			if ( !defined $params{subcontractInfo}->{startPlannedDate} ) {
				if ( defined $params{subcontractInfo}->{endPlannedDate} ) {
					$params{subcontractInfo}->{startPlannedDate} = $params{subcontractInfo}->{endPlannedDate};
				} else {
					$params{subcontractInfo}->{startPlannedDate} = $self->_art->get_iso_date_from_date($self->_art->get_sysdate);
				}
			}
			if ( !defined $params{subcontractInfo}->{endPlannedDate} ) {
				$params{subcontractInfo}->{endPlannedDate} = $params{subcontractInfo}->{startPlannedDate};
			}
			$create_params{PROPERTIES}->{maker} = $params{maker};
			$create_params{PROPERTIES}->{startPlannedDate} = $params{subcontractInfo}->{startPlannedDate};
			$create_params{PROPERTIES}->{endPlannedDate} = $params{subcontractInfo}->{endPlannedDate};
			$create_params{PROPERTIES}->{subContractName} = $params{subcontractInfo}->{subContractName};
			$create_params{PROPERTIES}->{subContractCode} = $params{subcontractInfo}->{subContractCode};
		}
		
	}
	
	if ($params{workType} =~ /^(Cable|Splice)$/){
		# ciclo su tutti i dettagli da creare
		for my $d (@{$params{details}}){
		
			my $create_params_system = $d;
			# aggiungo i parametri cross
			for ("customerId", "contractId", "cadastralCode", "pop", "popId", "workingGroupCode"){
				$create_params_system->{$_} = $params{$_};
			};
			$create_params_system->{ringId} = $d->{"ringId"};
			$create_params_system->{ring} = decode_base32($d->{"ringId"});
			
			# devo creare il sistema: le verifiche sono all'interno di ogni classe sistema
			my $sistema = $collection->crea(%{$create_params_system});
			
			unless (defined $sistema){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			if ($params{workType} eq 'Cable'){
				$create_params{DESCRIPTION} = join("-", $params{workType}, $d->{cableId}, $sistema->property('cableName'));
			} elsif($params{workType} eq 'Splice'){
				$create_params{DESCRIPTION} = join("-", $params{workType}, $d->{networkElementId}, $d->{networkElementName});
			}
			$create_params{SYSTEM_ID} = $sistema->id();
			$create_params{PROPERTIES}->{estimatedDuration} = $d->{estimatedDuration};
			$create_params{PROPERTIES}->{projectId} = $d->{projectId};
			$create_params{PROPERTIES}->{ringId} = $sistema->property("ringId");
			$create_params{PROPERTIES}->{ring} = $sistema->property("ring");
			
			my $activity = $self->_get_coll_activity()->create(%create_params);
		
			unless (defined $activity){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			$self->_logger()->info( __x("Activity {activityId} created for {type} {typeId}", activityId => $activity->id(), type => ($params{workType} eq 'Cable' ? 'cableId' : 'networkElementId' ), typeId => ($params{workType} eq 'Cable' ? $d->{cableId} : $d->{networkElementId} )) );
			push @results, $activity;
		}
	} elsif($params{workType} =~ /^(Civil)$/){
		# verifico che esistano i parametri opzionali obbligatori solo per i lavori civili
		for ('endPlannedDate'){
			if ($params{maker} eq 'Subcontract' && ! defined $create_params{PROPERTIES}->{$_}){
				$self->_art()->last_error(__x("Missing mandatory param {param}", param => $_));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		}
		
		# verifico coerenza date
		if (defined $create_params{PROPERTIES}->{endPlannedDate} && $self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{startPlannedDate}) > $self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{endPlannedDate})){
			$self->_art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'endPlannedDate', date2 => 'startPlannedDate'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		# prevediamo che si possa creare anche più di un cantiere contemporaneamente anche se adesso non 
		# e' previsto
		for my $d(@{$params{details}}){
			# per i lavori civili devo creare un solo sistema cantiere che poi si occuperà di creare i sottosistemi
			my $create_params_system;
			
			# aggiungo i parametri cross
			for ("customerId", "contractId", "cadastralCode", "pop", "popId", "workingGroupCode", "description"){
				$create_params_system->{$_} = $params{$_};
			};
			
			$create_params_system->{details} = $d;
			
			# devo creare il sistema: le verifiche sono all'interno di ogni classe sistema
			my $sistema = $collection->crea(%{$create_params_system});
			
			unless (defined $sistema){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			my $system_property = $sistema->property();
			
			$create_params{DESCRIPTION} = $params{description};
			$create_params{SYSTEM_ID} = $sistema->id();
			$create_params{PROPERTIES}->{estimatedDuration} = $system_property->{estimatedDuration};
			$create_params{PROPERTIES}->{projectId} = $system_property->{projectId};
			$create_params{PROPERTIES}->{ringId} = $system_property->{ringId};
			$create_params{PROPERTIES}->{ring} = $system_property->{ring};
			
			my $activity = $self->_get_coll_activity()->create(%create_params);
		
			unless (defined $activity){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			$self->_logger()->info( __x("Activity {activityId} created for {id}", activityId => $activity->id(), id => $create_params{DESCRIPTION}) );
			push @results, $activity;
		}
	} else {
		
		# verifico le date
		my $today = substr($self->_art()->_dbh()->get_sysdate(), 0,8);
		
		# verifico coerenza date
		if ($params{maker} eq 'Team'){
			
			my $start_date = defined $create_params{PROPERTIES}->{slaStart} ? substr($self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{slaStart}), 0,8): undef;
			my $end_date = defined $create_params{PROPERTIES}->{slaEnd} ? substr($self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{slaEnd}), 0,8) : undef; 
			
			if (defined $start_date && defined $end_date && $start_date > $end_date){
				$self->_art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'slaEnd', date2 => 'slaStart'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		} else {
			my $start_date = defined $create_params{PROPERTIES}->{startPlannedDate} ? substr($self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{startPlannedDate}), 0, 8) : undef;
			my $end_date = defined $create_params{PROPERTIES}->{endPlannedDate} ? substr($self->_art()->get_date_from_iso_date($create_params{PROPERTIES}->{endPlannedDate}), 0, 8) : undef; 
			
			if (defined $start_date && defined $end_date && $start_date > $end_date){
				$self->_art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'endPlannedDate', date2 => 'startPlannedDate'));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		}
		
		my $create_params_system;
		
		# aggiungo i parametri obbligatori
		for ("customerId", "contractId", "workingGroupCode", "description", "workType", "reference"){
			$create_params_system->{$_} = $params{$_};
		};
		
		# questo serve per dare eventualmente visibilità anche al gruppo subappalto
		$create_params_system->{subContractCode} = $create_params{PROPERTIES}->{subContractCode} if exists $create_params{PROPERTIES}->{subContractCode} && defined $create_params{PROPERTIES}->{subContractCode};
		
		if (defined $params{details}){
			$create_params_system->{details} = $params{details};
			for my $d (@{$params{details}}){
				$create_params{PROPERTIES}->{$d->{type}} = 1;
			}
		}
		
		$create_params{PROPERTIES}->{flagFIR} = $params{flagFIR};
		
		# aggiungo i parametri specifici
		if ($params{workType} =~ /^(Network|NetworkFibercop)$/){
			$params{networkId} = $params{assetId};
			$create_params_system->{description} = join("-", $params{customerId}, $params{contractId}, $params{networkId});
			for ("networkId","accountingOperation"){
				$create_params_system->{$_} = $params{$_};
			};
			$create_params_system->{assetId} = [$params{assetId}];
			$create_params{PROPERTIES}->{projectId} = $params{networkId};
		} elsif ($params{workType} =~/^(ROE|PTE)$/){
			$create_params_system->{description} = join("-", $params{customerId}, $params{contractId}, $self->get_db()->get_sysdate());
			for ("networkId"){
				$create_params_system->{$_} = $params{$_} if defined $params{$_}; # dove applicabile
			};
			if ($params{workType} =~/^(PTE)$/){
				$create_params_system->{assetId} = [$params{assetId}];
			}
		} elsif ($params{workType} =~/^(PreeMaintenance|CorrMaintenance|ExtrMaintenance|PreeMaintenance01|ExtrMaintenance01)$/){
			$create_params_system->{description} = join("-", $params{customerId}, $params{contractId}, $self->get_db()->get_sysdate());
			for ("maintenanceId"){
				$create_params_system->{$_} = $params{$_};
			};
		}
		
		my $runs = ($params{workType} eq 'ROE' && ! $params{oneForAll}) ? scalar @{$params{assetId}} : 1;
		
		for (my $i = 0; $i<$runs; $i++){
			
			if ($params{workType} eq 'ROE'){
				if ($params{oneForAll}){
					$create_params_system->{"assetId"} = $params{"assetId"};
				} else {
					$create_params_system->{"assetId"} = [$params{"assetId"}->[$i]];
					delete $create_params{ATTACHMENTS} if $runs > 1;
				}
			}
			
			# devo creare il sistema: le verifiche sono all'interno di ogni classe sistema
			my $sistema = $collection->crea(%{$create_params_system});
			
			unless (defined $sistema){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			my $parent_system_property = $sistema->property();
			
			if ($params{workType} =~ /^(Network|NetworkFibercop|ROE|PreeMaintenance|CorrMaintenance|ExtrMaintenance|PTE|PreeMaintenance01|ExtrMaintenance01)$/){
				if ($params{maker} eq 'Team'){
					
					$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							 ERRMSG		=> \$errmsg
							,PARAMS		=> \%{$params{team}}
							,MANDATORY	=> {
							}
							,OPTIONAL	=> {
								"teamId"	=> { isa => 'SCALAR' }
								,"teamName"	=> { isa => 'SCALAR' }
							}
							,IGNORE_EXTRA_PARAMS => 1
					);
					
					# i campi teamId e teamName o vanno in coppia o non devono essere passati
					if (
						(defined $params{team}->{teamId} && ! defined $params{team}->{teamName})
						||
						(!defined $params{team}->{teamId} && defined $params{team}->{teamName})
					) {
						$self->_art()->last_error(__x("Fields team.teamId and team.TeamName must be both defined or both not defined"));
						$self->_logger()->error( $self->_art()->last_error() );
						return undef;
					}
					
					for ('teamId', 'teamName'){
						$create_params{PROPERTIES}->{$_} = $params{team}->{$_} if defined $params{team}->{$_};
					}
				}
			}
			
			$create_params{DESCRIPTION} = defined $params{description} ? $params{description} : $params{assetDescription}->[$i];
			$create_params{SYSTEM_ID} = $sistema->id();
			$create_params{PROPERTIES}->{projectId} = $params{networkId} if defined $params{networkId};
			$create_params{ID_CUSTOM} = $self->_art()->get_activity_next_id();
			$create_params{CREATION_USER} = $params{operatorLogin}
				if defined $params{operatorLogin};
			
			if (exists $create_params{ATTACHMENTS}){
				my $meta = {
					activityId => $create_params{ID_CUSTOM},
					activityType => $create_params{ACTIVITY_TYPE_NAME},
				};
				$meta->{networkId} = $create_params{PROPERTIES}->{projectId} if exists $create_params{PROPERTIES}->{projectId} && defined $create_params{PROPERTIES}->{projectId};
				$meta->{maintenanceId} = $params{"maintenanceId"} if ($params{workType} =~ /^(PreeMaintenance|CorrMaintenance|ExtrMaintenance|PreeMaintenance01|ExtrMaintenance01)$/);
				if ($params{workType} eq 'ROE'){
					for my $asset (@{$params{"assetId"}}){
						$meta->{"assetId-".$asset} = 1;
					}
				}
				
				for my $attach (@{$create_params{ATTACHMENTS}}){
					unless (ref($attach)){
						$attach = {
							FILENAME => $attach
						};
					}
					$attach->{META} = $meta;
				}
			}

			# nei PTE c'è sempre un solo tipo di lavoro quindi posso utilizzare lo 0esimo
			if (
				$params{maker} eq 'Subcontract' &&
				$params{workType} eq 'PTE' && 
				$params{details}->[0]->{type} =~ /^(updateDatabaseF1|updateDatabaseF2)$/
			){
				$create_params{PROPERTIES}->{takeInCharge} = 0;
			}
			
			my $activity = $self->_get_coll_activity()->create(%create_params);
				
			unless (defined $activity){
				$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_LAVORO_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			$self->_logger()->info( __x("Activity {activityId} created", activityId => $activity->id()) );
			$self->_logger()->info( __x("Activity {activityId} {param} {value}", activityId => $activity->id(), param => 'assetId', value => join (',', @{$activity->system_property('assetId')})) ) if defined $params{'assetId'};
			push @results, $activity;
		}
	}
	return \@results;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"contractId"		=> { isa => 'SCALAR' }
					,"workType"			=> { isa => 'ARRAY', min => 1 }
					,"spWorkType"		=> { isa => 'ARRAY', min => 1 }
					,"workId"			=> { isa => 'ARRAY', min => 1 }
					,"projectId"		=> { isa => 'SCALAR' }
					,"assetId"			=> { isa => 'SCALAR' }
					,"cableId"			=> { isa => 'SCALAR' }
					,"ref00"			=> { isa => 'SCALAR' }
					,"ref01"			=> { isa => 'SCALAR' }
					,"maintenanceId"	=> { isa => 'SCALAR' }
					,"SORT"				=> { isa => 'ARRAY' }
					,"LIMIT"			=> { isa => 'SCALAR' }
					,"SKIP"				=> { isa => 'SCALAR' }
					,"STATUS_IN"		=> { isa => 'ARRAY' }
					,"survey"			=> { isa => 'SCALAR' }
					,"infrastructure"	=> { isa => 'SCALAR' }
					,"laying"			=> { isa => 'SCALAR' }
					,"junction"			=> { isa => 'SCALAR' }
					,"updateDatabaseF1"	=> { isa => 'SCALAR' }
					,"updateDatabaseF2"	=> { isa => 'SCALAR' }
					,"testApp"			=> { isa => 'SCALAR' }
					,"testOTDR"			=> { isa => 'SCALAR' }
					,"restoration"		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	# NOTA: se il parametro workType contiene dei tipi sistema errati o non collegati
	# al tipo attività LAVORO non scoppia ma restituisce 0 record
	 
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'LAVORO'
		,ACTIVITY_PROPERTIES_EQUAL	=> {
			customerId		=> $params{customerId}
		}
	};
	
	$searchParam->{SYSTEM_TYPE_NAME_IN} = $params{workType} if defined $params{workType};
	$searchParam->{ID_IN} = $params{workId} if defined $params{workId};
	
	for my $k ("contractId", "projectId") {
		$searchParam->{ACTIVITY_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}
	for my $k ("cableId", "maintenanceId", "assetId") {
		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}

	$searchParam->{SYSTEM_PROPERTIES_IN}->{workType} = $params{spWorkType} if defined $params{spWorkType};

	for my $k ("ref00", "ref01", "survey","infrastructure","laying","junction","updateDatabaseF1","updateDatabaseF2","testApp","testOTDR","restoration") {
		$searchParam->{ACTIVITY_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}
	
	for my $k ("SORT", "LIMIT", "SKIP", "STATUS_IN") {
		$searchParam->{$k} = $params{$k} if defined $params{$k};
	}
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $works = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $works){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $works;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOWORKS::Collection::Activity::LAVORO->new(ART => $art);
	
	my $lavori = $coll->crea(
		'attachments' => [],
		'workingGroupCode' => '496654',
		'details' => [
						{
						'type' => 'survey'
						}
					],
		'description' => 'test 3',
		'contractId' => 'FTTH',
		'customerId' => 'TIM',
		'assetId' => '67648',
		'maker' => 'Team',
		'reference' => {
						'ref01' => '63196',
						'ref00' => '63195'
						},
		'ATTACHMENTS' => [],
		'flagFIR' => 0,
		'oneForAll' => 0,
		'workType' => 'PTE',
		'team' => {
		# 			'slaEnd' => '2021-03-03T23:00:00.000Z',
		 			'teamName' => 'BIFFI PAOLO',
		# 			'slaStart' => '2021-03-03T23:00:00.000Z',
					'teamId' => '10087583'
				},
		'cannotCloseWork' => 1,
		'cannotCloseWork' => 'Test',
		'updateDatabaseF1' => 1,
		# 'invalidatePreviousReferences' => 1
	);
	
	if( defined $lavori) {
		get_logger()->info("OK create ".scalar @{$lavori});
		#print STDERR Dumper $lavori;
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
