package WPSOWORKS::Collection::Activity::WZ_LC;

use strict;
use warnings;

use JSON;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;
use WPSOWORKS::Collection::System::WZ;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{CollSystem} = WPSOWORKS::Collection::System::WZ->new(ART => $self->{ART});
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_system{shift->{CollSystem}}

sub _get_coll_activity{shift->{CollActivity}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
					,"permitsAreaId"	=> { isa => 'SCALAR' }
					,"workZoneId"		=> { isa => 'SCALAR' }
					,"name"				=> { isa => 'SCALAR' }
					,"requestDate"		=> { isa => 'SCALAR' }
					,"polygon"			=> { isa => 'HASH' }
					,"centralPoint"		=> { isa => 'SCALAR' }
					,"username"			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"note"				=> { isa => 'SCALAR' }
					,"ATTACHMENTS"		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# la verifica dei parametri è delegata all'oggetto sistema
	
	# cerco se esiste un sistema già aperto con le stesse info
	my $sistemi = $self->_get_coll_system()->cerca(
		customerId		=> $params{customerId}
		,contractId		=> $params{contractId}
		,projectId		=> $params{projectId}
		,permitsAreaId	=> $params{permitsAreaId}
		,workZoneId		=> $params{workZoneId}
	);
	
	unless (defined $sistemi){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemi} > 0){
		$self->_art()->last_error(__x("WorkZone {workZoneId} for permits Area {permitsAreaId}, project {project}, customer_id {customer_id} and contract_id {contract_id} already created!", project => $params{projectId}, customer_id => $params{customerId}, contract_id => $params{contractId}, permitsAreaId => $params{permitsAreaId}, workZoneId => $params{workZoneId}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Act_WZ_LC_crea" );
	
	my $sistema = $self->_get_coll_system()->crea(%params);
	
	unless (defined $sistema){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_WZ_LC_crea" );
		return undef;
	}
	
	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'WZ_LC'
		, DESCRIPTION		=> join("-", $params{customerId}, $params{contractId}, $params{projectId}, $params{permitsAreaId}, $params{workZoneId})
		, PROPERTIES		=> {
			"customerId"		=> $params{"customerId"}
			,"contractId"		=> $params{"contractId"}
			,"projectId"		=> $params{"projectId"}
			,"permitsAreaId"	=> $params{"permitsAreaId"}
			,"workZoneId"		=> $params{"workZoneId"}
			,"name"				=> $params{"name"}
			,"requestDate"		=> $params{"requestDate"}
			,"polygon"			=> $params{"polygon"}
			,"centralPoint"		=> $params{"centralPoint"}
			,"username"			=> $params{"username"}
		}
		, SYSTEM_ID => $sistema->id()
	);
	
	$create_params{PROPERTIES}->{"note"} = $params{"note"} if defined $params{"note"};
	
	$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
	
	my $activity = $self->_get_coll_activity()->create (%create_params);

	unless (defined $activity){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_WZ_LC_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $activity;
}

#sub cerca{
#	my $self = shift;
#	my %params = @_;
#	
#	my $errmsg;
#	$self->_art()->last_error($errmsg)
#		and return undef
#			unless $self->_art()->check_named_params(
#				 ERRMSG		=> \$errmsg
#				,PARAMS		=> \%params
#				,MANDATORY	=> {
#					"customerId"		=> { isa => 'SCALAR' }
#					,"contractId"		=> { isa => 'SCALAR' }
#				}
#				,OPTIONAL	=> {
#					"projectId"			=> { isa => 'SCALAR' }
#					,"permitsAreaId"	=> { isa => 'SCALAR' }
#					,"workZoneId"		=> { isa => 'SCALAR' }
#					,"status"			=> { isa => 'ARRAY' }
#				}
#				,IGNORE_EXTRA_PARAMS => 0
#	);
#	
#	my $searchParam = {
#		ACTIVITY_TYPE_NAME_EQUAL => 'WZ_LC'
#		,SYSTEM_PROPERTIES_EQUAL => {
#			customerId		=> $params{customerId},
#			contractId		=> $params{contractId}
#		}
#	};
#	
#	$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{projectId} = $params{projectId} if defined $params{projectId};
#	$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{permitsAreaId} = $params{permitsAreaId} if defined $params{permitsAreaId};
#	
#	if (defined $params{workZoneId}){
#		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{workZoneId} = $params{workZoneId};
#		$searchParam->{LIMIT} = 1; #per efficienza in quanto attesa sola una attività in questa situazione
#	}
#	
#	if (defined $params{status}){
#		$searchParam->{STATUS_IN} = $params{status};
#	}
#	
#	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
#	my $wzs = $self->_get_coll_activity()->find_object(%{$searchParam});
#	
#	unless (defined $wzs){
#		$self->_logger()->error( $self->_art()->last_error() );
#		return undef;	
#	}
#	
#	return $wzs;
#}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOWORKS::Collection::Activity::WZ_LC->new(ART => $art);
	
	my $wz_lc = $coll->crea(
		"customerId"		=> 'ENEL'
		,"contractId"		=> 'FTTG'
		,"projectId"		=> '862'
		,"permitsAreaId"	=> '123123123'
		,"workZoneId"		=> '20170803141516'
		,"centralPoint"		=> '1231231'
		,"name"				=> 'nome work zone'
		,"username"			=> 'ROOT'
		,"requestDate"		=> '2012-07-14T02:00:00.000000000+02:00'
		,"polygon"			=> {
			"coordinates"	=>
				[
					[
						[38.094453259477,13.3629100224457]
						,[38.0947851644576,13.3624736467743]
						,[38.0954576999267,13.3621301719054]
						,[38.0942749307484,13.3601163194046]
						,[38.0937591912844,13.3606237433778]
						,[38.0941526658676,13.3620887479935]
					]
				],
			"type"=>"Polygon"
		}
	);
	
	if( defined $wz_lc) {
		get_logger()->info("OK: ".$wz_lc->id(). "(".$wz_lc->get_current_status_name().")");
		print STDERR Dumper $wz_lc->property();
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
#	my $wzs = $coll->cerca(
#		"projectId" => "888"
#		, "permitsAreaId" => '3123123'
#		, "contractId" => 'FTTH'
#		, "customerId" => 'ENEL'
#	);
#	
#	if( defined $wzs) {
#		get_logger()->info("OK: trovate ".scalar (@{$wzs}). " attività");
#		for my $wz (@{$wzs}){
#			get_logger()->info("OK: ".$wz->id(). "(".$wz->get_current_status_name().")");
#		}
#		#print STDERR Dumper $ap_lc->property();
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
