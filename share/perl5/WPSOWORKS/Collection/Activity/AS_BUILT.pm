package WPSOWORKS::Collection::Activity::AS_BUILT;

use strict;
use warnings;

use JSON;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;
use WPSOWORKS;
use WPSOWORKS::Collection::System::SUBAPPALTO;
use WPSOWORKS::Collection::System::SQUADRA;
use WPSOWORKS::Collection::Activity::LAVORO;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{CollSystemSubappalto} = WPSOWORKS::Collection::System::SUBAPPALTO->new(ART => $self->{ART});
	
	$self->{CollSystemSquadra} = WPSOWORKS::Collection::System::SQUADRA->new(ART => $self->{ART});
	
	$self->{CollActivityLavoro} = WPSOWORKS::Collection::Activity::LAVORO->new(ART => $self->{ART});
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_system_subappalto{shift->{CollSystemSubappalto}}

sub _get_coll_system_squadra{shift->{CollSystemSquadra}}

sub _get_coll_activity_lavoro{shift->{CollActivityLavoro}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_wpsoworks{shift->{WPSOWORKS}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $profilo = $self->_get_wpsoworks()->profilo();
	return undef unless defined $profilo;
	
	#verifica sugli operatori abilitati all'apertura di un'attività di LAVOR
	if (
		!$profilo->is_assistente_tecnico_civile
		&&
		!$profilo->is_coordinatore_at_civile
		&&
		!$profilo->is_assistente_tecnico_giunzioni
		&&
		!$profilo->is_coordinatore_at_giunzioni
		&&
		!$profilo->is_plan_and_program
	) {
		$self->_art()->last_error(__("User is not enabled to open the activity"));
		return undef;
	}
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					,"description"	=> { isa => 'SCALAR' }
					,"ids"			=> { isa => 'ARRAY' }
				}
				,OPTIONAL	=> {
					"subContractStartWorkDate"	=> { isa => 'SCALAR' }
					,"subContractEndWorkDate"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	#recupero le attività da aggregare
	my $acts = $self->_get_coll_activity_lavoro()->cerca(
		"customerId"	=> $params{"customerId"}
		,"contractId"	=> $params{"contractId"}
		,"workId"		=> $params{"ids"}
	);
	return undef unless defined $acts;
	
	$self->_art()->last_error(__x("Activities {type} not found!", type => 'LAVORO'))
		&& return undef
			unless scalar @{$acts};
	
	my $tipo_gestore;
	my $gestore;
	my $progetto;
	
	# tutte le attività trovate devono avere lo stesso subappalto/squadra e devono afferire allo stesso progetto
	for my $act (@{$acts}){
		my $sys_progetto = $act->system_property('projectId');
		my $act_props = $act->activity_property();
		if (defined $progetto && $sys_progetto ne $progetto){
			$self->_art()->last_error(__x("Activities {type} must have the same project", type => 'LAVORO'));
			return undef;
		}
		my $act_tipo_gestore = defined $act_props->{"teamId"} ? 'Team' : 'Subcontract';
		if (defined $tipo_gestore && $act_tipo_gestore ne $tipo_gestore){
			$self->_art()->last_error(__x("Activities {type} must have the same type of maker", type => 'LAVORO'));
			return undef;
		}
		
		my $actGestore = $act_tipo_gestore eq 'Team' ? $act_props->{"teamId"} : $act_props->{"subContractCode"};
		if (defined $gestore && $actGestore ne $gestore){
			$self->_art()->last_error(__x("Activities {type} must have the same maker", type => 'LAVORO'));
			return undef;
		}
		
		$tipo_gestore = $act_tipo_gestore;
		$gestore = $actGestore;
		$progetto = $sys_progetto;
	}
	
	#ora recupero il relativo sistema che deve già esistere
	my $sistemi;
	my $search_params = {
		"customerId"	=> $params{"customerId"}
		,"contractId"	=> $params{"contractId"}
	}; 
	if ($tipo_gestore eq 'Team'){
		$search_params->{teamId} = $gestore;
		$sistemi = $self->_get_coll_system_squadra()->cerca(%{$search_params});
	} else {
		$search_params->{subContractCode} = $gestore;
		$sistemi = $self->_get_coll_system_subappalto()->cerca(%{$search_params});
	}
	
	return undef unless defined $sistemi;
	
	$self->_art()->last_error(__x("Unable to find system for customerId {customerId} and contractId {contractId}", customerId => $params{"customerId"}, contractId => $params{"contractId"}))
		&& return undef
			unless scalar @{$sistemi};
	
	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'AS_BUILT'
		, PROPERTIES		=> {
			"customerId"	=> $params{"customerId"}
			,"contractId"	=> $params{"contractId"}
			,"ids"			=> encode_json($params{"ids"})
		}
		, SYSTEM_ID => $sistemi->[0]->id()
		, DESCRIPTION => $params{description}
	);
	
	$create_params{PROPERTIES}->{subContractStartWorkDate} = $params{"subContractStartWorkDate"} if defined $params{"subContractStartWorkDate"};
	$create_params{PROPERTIES}->{subContractEndWorkDate} = $params{"subContractEndWorkDate"} if defined $params{"subContractEndWorkDate"};
	
	$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Act_ASB_crea" );
	
	my $activity = $self->_get_coll_activity()->create (%create_params);

	unless (defined $activity){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Act_ASB_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	$self->_logger()->info( __x("Activity {activityId} created", activityId => $activity->id()) );
	
	return $activity;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"asBuiltId"			=> { isa => 'ARRAY', min => 1 }
					,"SORT"				=> { isa => 'ARRAY' }
					,"LIMIT"			=> { isa => 'SCALAR' }
					,"SKIP"				=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'AS_BUILT'
		,SYSTEM_PROPERTIES_EQUAL	=> {
			customerId		=> $params{customerId},
			contractId		=> $params{contractId}
		}
	};
	
	$searchParam->{ID_IN} = $params{asBuiltId} if defined $params{asBuiltId};
	
	for my $k ("SORT", "LIMIT", "SKIP") {
		$searchParam->{$k} = $params{$k} if defined $params{$k};
	}
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $asBuilts = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $asBuilts){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $asBuilts;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOWORKS::Collection::Activity::AS_BUILT->new(ART => $art);
	
	my $as_built = $coll->crea(
		"customerId"		=> 'ENEL'
		,"contractId"		=> 'FTTH'
		,"ids" => [6344,6345]
	);
	
	if( defined $as_built) {
		get_logger()->info("OK create ".$as_built->id());
		#print STDERR Dumper $as_built;
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
