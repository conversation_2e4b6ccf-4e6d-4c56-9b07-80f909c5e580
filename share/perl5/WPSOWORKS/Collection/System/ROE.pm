package WPSOWORKS::Collection::System::ROE;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::ROE";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;

	$self = $self->SUPER::new(ART => $params->{ART});
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' },
					"contractId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"roeId"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $search_params = {
		customerId => $params{customerId},
		contractId => $params{contractId},
	};
	
	$search_params->{roeId} = $params{roeId} if defined $params{roeId};
	
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['ROE']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					customerId				=> { isa => 'SCALAR' }
					,contractId				=> { isa => 'SCALAR' }
					,roeId					=> { isa => 'SCALAR' }
					,__projectGroupName__	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					__wgGroupName__		=> { isa => 'ARRAY' } #chiave per i gruppi wg che potrebbe essere vuota
				} # gli opzionali per scelta non vengono elencati
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$params{DETAILS} = [] if ! defined $params{DETAILS};
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_ROE_crea" );
	
	my $description = $params{roeId};
	
	my $groups = [
		'ADMIN',
		$params{__projectGroupName__}
	];
	
	push @{$groups}, @{$params{__wgGroupName__}} if defined $params{__wgGroupName__};
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'ROE'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => $description
		, GROUPS => $groups
		, PROPERTIES => \%params
	);
	
	my $system = $self->create (%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_ROE_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOCORE::Collection::System::ROE->new(ART => $art);
	
	my $obj = $coll->crea(
#		"requestType"		=> 'As-Built'
#		,"requestContext" 	=> 'Cables'
#		,"description"		=> 'ENEL'
#		,"requestor"		=> 'works'
#		,"asBuiltId"		=> '144311'            
	);
	
	if( defined $obj) {
		get_logger()->info("OK: ".$obj->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
