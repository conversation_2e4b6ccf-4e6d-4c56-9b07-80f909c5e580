package WPSOWORKS::Collection::System::SQUADRA;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::SQUADRA";

sub new {
	my ($class, @args) = @_;
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(@args);
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					,"teamId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my %p = (
		PROPERTIES => {
			customerId	=> $params{customerId}
			,contractId	=> $params{contractId}
			,teamId		=> $params{teamId}
		}
	);
	
	return $self->_cerca(%p);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['SQUADRA']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					,"teamId"		=> { isa => 'SCALAR' }
					,"teamName"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# cerco se esiste un sistema aperto per la squadra
	my $sistemiSquadra = $self->cerca(
		customerId	=> $params{customerId},
		contractId	=> $params{contractId},
		teamId		=> $params{teamId}
	);
	
	unless (defined $sistemiSquadra){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemiSquadra} != 0){
		$self->_art()->last_error(__x("Team {teamId} already present!", teamId => $params{teamId}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_SQU_crea" );
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'SQUADRA'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"teamId"}, $params{"teamName"})
		# NOTA: il gruppo USER da visibilità a tutti: lo consideriameno accettabile in quanto
		#       l'unica attività di tipo AS_BUILT associata non ha alcun dato riservato
		, GROUPS => ['ADMIN', 'USER']
		, PROPERTIES => {
			"customerId"	=> $params{"customerId"}
			,"contractId"	=> $params{"contractId"}
			,"teamId"		=> $params{"teamId"}
			,"teamName"		=> $params{"teamName"}
		}
	);
	
	my $system = $self->create (%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_SQU_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOWORKS::Collection::System::SQUADRA->new(ART => $art);
	
	my $sub = $coll->crea(
		"contractId" => "FTTH"
		, "customerId" => 'ENEL'
		, "teamId"	=> '889'
		, "teamName"	=> 'PAPERINO'
	);
	
	if( defined $sub) {
		get_logger()->info("OK: ".$sub->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
