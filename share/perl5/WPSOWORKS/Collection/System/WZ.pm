package WPSOWORKS::Collection::System::WZ;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::WZ";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub _ap { shift->{AP} }

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
					,"permitsAreaId"	=> { isa => 'SCALAR' }
					,"workZoneId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	return $self->_cerca(
		PROPERTIES => {
			"customerId"		=> $params{customerId}
			,"contractId"		=> $params{contractId}
			,"projectId"		=> $params{projectId}
			,"permitsAreaId"	=> $params{permitsAreaId}
			,"workZoneId"		=> $params{workZoneId}
		}
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['WZ']
		, ACTIVE			=> 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
					,"permitsAreaId"	=> { isa => 'SCALAR' }
					,"workZoneId"		=> { isa => 'SCALAR' }
					,"name"				=> { isa => 'SCALAR' }
					,"requestDate"		=> { isa => 'SCALAR' }
					,"polygon"			=> { isa => 'HASH' }
					,"centralPoint"		=> { isa => 'SCALAR' }
					,"username"			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# verifico che la polygon sia un JSON
	eval {
		$params{polygon} = encode_json($params{polygon});
	};
	if ($@){
		my $msg = __x('Invalid param {name}: {error}', name => __('polygon'), error => $@ );
		$self->_art()->last_error($msg);
		$self->_logger()->error($msg);
		return undef;
	}
	
	# verifico le date
	unless ($self->_art()->get_date_from_iso_date($params{requestDate})){
		my $msg = __x('Invalid param {name}: {error}', name => __('requestDate'), error => $self->_art()->last_error() );
		$self->_art()->last_error($msg);
		$self->_logger()->error($msg);
		return undef;
	}
	
	# verifico l'utenza
	unless ($self->_art()->test_user_name($params{username})){
		my $msg = __x('Invalid param {name}: {error}', name => __('username'), error => __x("{username} not found", username => $params{username}) );
		$self->_art()->last_error($msg);
		$self->_logger()->error($msg);
		return undef;
	}
	
	# cerco se esiste un sistema aperto per l'area_peremsso sul progetto passato
	my $sistemiWZ = $self->cerca(
		"customerId"		=> $params{customerId}
		,"contractId"		=> $params{contractId}
		,"projectId"		=> $params{projectId}
		,"permitsAreaId"	=> $params{permitsAreaId}
		,"workZoneId"		=> $params{workZoneId}
	);
	
	unless (defined $sistemiWZ){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	if (scalar @{$sistemiWZ}){
		$self->_art()->last_error(__x("workZone {workZoneId} for project {project}, customer_id {customer_id}, contract_id {contract_id} and permitsAreaId {permitsAreaId} already present!", permitsAreaId => $self->_ap()->property('permitsAreaId'), project => $self->_ap()->property('projectId'), customer_id => $self->_ap()->property('customerId'), contract_id => $self->_ap()->property('contractId'). workZoneId => $params{"workZoneId"}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
	
	# il sistema deve avere come visiblità ADMIN, CITY_city1 e PROJECT_ cust1_cont1_prog1
	# il gruppo PROJECT lo posso creare direttamente mentre il gruppo CITY mi verrà restitutito
	# successivamente da un demone dedicato
	my $projectGroupName = 'PROJECT_'.$params{"customerId"}."_".$params{"contractId"}."_".$params{"projectId"};
	# se il gruppo non esiste lo creo
	unless ($self->_art()->test_group_name($projectGroupName)){
		my $projectGroup = $self->_art()->create_group(
			NAME => $projectGroupName,
			DESCRIPTION => $projectGroupName,
			IS_AUTOGENERATED => 1
		);
	
		unless (defined $projectGroup){
			$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
	}
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'WZ'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"customerId"}, $params{"contractId"}, $params{"projectId"}, $params{"permitsAreaId"}, $params{workZoneId}) 
		, GROUPS => ['ADMIN',$projectGroupName]
		, PROPERTIES => {
			"customerId"	=> $params{"customerId"},
			"contractId"	=> $params{"contractId"},
			"projectId"		=> $params{"projectId"},
			"permitsAreaId"	=> $params{"permitsAreaId"},
			"workZoneId"	=> $params{"workZoneId"},
			"name"			=> $params{"name"},
			"requestDate"	=> $params{"requestDate"},
			"polygon"		=> $params{"polygon"},
			"centralPoint"	=> $params{"centralPoint"},
			"username"		=> $params{"username"},
		}
	);
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my $raData = {
		WHAT => {
			METHODS => ['get_cityId']
		},
		SEARCH_PARAMS => {
			PROPERTIES => {
				customerId => $params{"customerId"},
				contractId => $params{"contractId"},
				projectId => $params{"projectId"},
				permitsAreaId => $params{"permitsAreaId"}
			}
		}
	};
	
	$raData = encode_json($raData);
	if ($@){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		my $msg = __x('Invalid raData: {error}', error => $@ );
		$self->_art()->last_error($msg);
		$self->_logger()->error($msg);
		return undef;
	}
	
	my $ra = eval {
		$system->get_sender()->get_info_permits_area(
			SOURCE_REF => $system->id(),
			DATA => {
				data => $raData
			}
		)
	};
	
	if ($@){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		my $message = __x("Unable to send request to AP: {error}", error => $@);
		$self->_art()->last_error($message);
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::WZ->new(ART => $art);
	
	my $wz = $coll->crea(
		"customerId"		=> 'ENEL'
		,"contractId"		=> 'FTTG'
		,"projectId"		=> '862'
		,"permitsAreaId"	=> '123123123'
		,"workZoneId"		=> '20170803141516'
		,"centralPoint"		=> '1231231'
		,"name"				=> 'nome work zone'
		,"username"			=> 'ROOT'
		,"requestDate"		=> '2012-07-14T02:00:00.000000000+02:00'
		,"polygon"			=> {
			"coordinates"	=>
				[
					[
						[38.094453259477,13.3629100224457]
						,[38.0947851644576,13.3624736467743]
						,[38.0954576999267,13.3621301719054]
						,[38.0942749307484,13.3601163194046]
						,[38.0937591912844,13.3606237433778]
						,[38.0941526658676,13.3620887479935]
					]
				],
			"type"=>"Polygon"
		}
	);
	
	if( defined $wz) {
		get_logger()->info("OK: ".$wz->id(). " ". $wz->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
