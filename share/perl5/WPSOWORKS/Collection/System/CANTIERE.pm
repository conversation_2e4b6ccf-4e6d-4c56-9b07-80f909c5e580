package WPSOWORKS::Collection::System::CANTIERE;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;
use MIME::Base32;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use WPSOWORKS;
use WPSOWORKS::Collection::System::PROJECT;

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::CANTIERE";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{collProject} = WPSOWORKS::Collection::System::PROJECT->new(ART => $params->{ART});
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _get_coll_project { shift->{collProject} }

sub _wpsoworks { shift->{WPSOWORKS} }

sub get_db{shift->_art()->_dbh()}

#sub cerca{
#	my $self = shift;
#	my %params = @_;
#	
#	my $errmsg;
#	$self->_art()->last_error($errmsg)
#		and return undef
#			unless $self->_art()->check_named_params(
#				 ERRMSG		=> \$errmsg
#				,PARAMS		=> \%params
#				,MANDATORY	=> {
#					"customerId"		=> { isa => 'SCALAR' }
#					,"contractId"		=> { isa => 'SCALAR' }
#					,"projectId"		=> { isa => 'SCALAR' }
#				}
#				,OPTIONAL	=> {
#					"cableId"			=> { isa => 'SCALAR' }
#					,"fromNetworkElementId"			=> { isa => 'SCALAR' }
#				}
#				,IGNORE_EXTRA_PARAMS => 1
#	);
#	
#	my $search_params = {
#		"customerId"	=> $params{customerId}
#		,"contractId"	=> $params{contractId}
#		,"projectId"	=> $params{projectId}
#	};
#	
#	for ('cableId', 'fromNetworkElementId'){
#		$search_params->{$_} = $params{$_} if defined $params{$_};
#	}
#	
#	return $self->_cerca(
#		PROPERTIES => $search_params
#	);
#}
#
#sub _cerca{
#	my $self = shift;
#	my %params = @_;
#	
#	# imposto i filtri relativi al progetto
#	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
#	
#	# cerco i sistemi aperti con property
#	my $systems = $self->find_object(
#		SYSTEM_TYPE_NAME	=> ['CAVO']
#		, ACTIVE			=> 1
#		, SHOW_ONLY_WITH_VISIBILITY => 1
#		, %params
#	);
#	
#	unless (defined $systems){
#		$self->_logger()->error( $self->_art()->last_error() );
#		return undef;
#	}
#	
#	return $systems;
#}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"			=> { isa => 'SCALAR' }
					,"contractId"			=> { isa => 'SCALAR' }
					,"workingGroupCode"		=> { isa => 'SCALAR' }
					,"cadastralCode"		=> { isa => 'SCALAR' }
					,"pop"					=> { isa => 'SCALAR' }
					,"popId"				=> { isa => 'SCALAR' }
					,"details"				=> { isa => 'HASH' }
					,"description"			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> $params{details}
				,MANDATORY	=> {
					"permitsAreaId"	=> { isa => 'SCALAR' }
					,"projectId"	=> { isa => 'SCALAR' }
					,"ringId"		=> { isa => 'SCALAR' }
					,"macroTasks"	=> { isa => 'ARRAY' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $projectId = $params{details}->{projectId};
	my $permitsAreaId = $params{details}->{permitsAreaId};
	my $ringId = $params{details}->{ringId};
	my $children;
	my $managed;
	#verifico la struttura dei macroTasks
	for my $d (@{$params{"details"}->{macroTasks}}){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%{$d}
					,MANDATORY	=> {
						"estimatedDuration"	=> { isa => 'SCALAR' }
						,"category"				=> { isa => 'SCALAR' }
						,"toAssignQuantity"		=> { isa => 'SCALAR' }
						,"unitOfMeasure"		=> { isa => 'SCALAR' }
						,"subCategory"			=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		# verifico eventuali duplicati
		if (exists $managed->{$params{customerId}}->{$params{"contractId"}}->{$projectId}->{$d->{category}}->{$d->{subCategory}}){
			$self->_art()->last_error(__x("Duplicate record for category {category}, subCategory {subCategory}", category => $d->{category}, subCategory => $d->{subCategory}));
			return undef;
		} else {
			$managed->{$params{customerId}}->{$params{"contractId"}}->{$projectId}->{$d->{category}}->{$d->{subCategory}} = 1;
		} 
	}
	
	# recupero il relativo sistema PROJECT che deve essere già presente a sistema
	my $projects = $self->_get_coll_project()->cerca(
		customerId => $params{"customerId"}
		,contractId => $params{"contractId"}
		,projectId => $projectId
	);
	return undef unless defined $projects;
	
	if (scalar @{$projects} == 0){
		$self->_art()->last_error(__x("No project {projectId} found for customer {customerId} and contract {contractId}", projectId => $params{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$projects} > 1){
		$self->_art()->last_error(__x("Found {n} projects with id {projectId} for customer {customerId} and contract {contractId}", n => scalar @{$projects}, projectId => $params{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my $project_properties = $projects->[0]->property();
	
	#dato che ho trovato il progetto posso utilizzare il dato tecnico sistema per recuperare il cityId
	
	my $cityGroupName = 'CITY_'.$project_properties->{'cityId'};
	
	# il sistema deve avere come visiblità ADMIN, CITY_city1 e PROJECT_ cust1_cont1_prog1
	my $projectGroupName = 'PROJECT_'.$params{"customerId"}."_".$params{"contractId"}."_".$projectId;
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'CANTIERE'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => $params{"description"},
		, GROUPS => ['ADMIN' ,$projectGroupName, $cityGroupName]
		, PROPERTIES => {
			"requestType"		=> 'CANTIERE', #cablatura che serve per l'interazione con il GW
			"customerId"		=> $params{"customerId"},
			"contractId"		=> $params{"contractId"},
			"projectId"			=> $projectId,
			"permitsAreaId"		=> $permitsAreaId,
			"ringId"			=> $ringId,
			"ring"				=> decode_base32($ringId),
			"workingGroupCode"	=> $params{"workingGroupCode"},
			"cityId"			=> $project_properties->{'cityId'},
			"cadastralCode" 	=> $params{"cadastralCode"},
			"pop"				=> $params{"pop"},
			"popId"				=> $params{"popId"},
		}
	);

	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_CANT_crea" );
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_CANT_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	# una volta creato il sistema CANTIERE creo i sistemi MACROLAVORO e li faccio diventare figli del sistema CANTIERE
	for my $d (@{$params{"details"}->{"macroTasks"}}){
		
		my $macrolavoro_create = {
			"customerId"		=> $params{"customerId"},
			"contractId"		=> $params{"contractId"},
			"projectId"			=> $projectId,
			"permitsAreaId"		=> $permitsAreaId,
			"ringId"			=> $ringId,
			"ring"				=> decode_base32($ringId),
			"estimatedDuration"	=> $d->{"estimatedDuration"},
			"category"			=> $d->{"category"},
			"subCategory"		=> $d->{"subCategory"},
			"toAssignQuantity"	=> $d->{"toAssignQuantity"},
			"unitOfMeasure"		=> $d->{"unitOfMeasure"},
			"CANTIERE"			=> $system
		};
		
		# devo creare il sistema: le verifiche sono all'interno di ogni classe sistema
		my $sistema_macrolavoro = $system->add_macrotask(%{$macrolavoro_create});
		
		unless (defined $sistema_macrolavoro){
			$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_CANT_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::CANTIERE->new(ART => $art);
	
#	my $cerca = $coll->cerca(
#		"customerId"	=> 'ENEL'
#		,"contractId"	=> 'FTTH'
#		,"projectId"	=> '1003'
#		,"fromNetworkElementId" => 1028
#	);
#	if( defined $cerca) {
#		get_logger()->info("OK: trovati ".scalar @{$cerca}. " cavi");
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	
	my $cavo = $coll->crea(
		"customerId"	=> 'ENEL'
		,"contractId"	=> 'FTTH'
		,"projectId"	=> '1003'
		,"cadastralCode"	=> "F839"
		,"workingGroupCode" => 131333
		,"pop" => 'NA - FUORIGROTTA'
		,"popId"	=> "GAZCALJAJZAS2RSVJ5JESR2SJ5KFIQI"
		,"ring" => 131333
		,"ringId"	=> "GEYUK"
		,"category"		=> 'SCAVO'
		,"subCategory"	=> 'SCAVO nessuna specializzazione'
		,"unitOfMeasure"	=> 'mt'
		,"estimatedDuration"	=> 50
	);
	
	if( defined $cavo) {
		get_logger()->info("OK: ".$cavo->id(). " ". $cavo->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
