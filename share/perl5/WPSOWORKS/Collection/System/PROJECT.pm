package WPSOWORKS::Collection::System::PROJECT;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::PROJECT";

sub new {
	my ($class, @args) = @_;
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(@args);
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"projectId"			=> { isa => 'SCALAR' },
					"cadastralCode"		=> { isa => 'SCALAR' },
					"pop"				=> { isa => 'SCALAR' },
					"ring"				=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my %p = (
		PROPERTIES => {
			customerId		=> $params{customerId}
			,contractId	=> $params{contractId}
		}
	);
	
	for my $t ('projectId', 'cadastralCode', 'pop', 'ring'){
		$p{PROPERTIES}->{$t} = $params{$t} if defined $params{$t};
	}
	
	return $self->_cerca(%p);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['PROJECT']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"					=> { isa => 'SCALAR' }
					,"contractId"					=> { isa => 'SCALAR' }
					,"projectId"					=> { isa => 'SCALAR' }
					,"cadastralCode"				=> { isa => 'SCALAR' }
					,"projectGroupName"				=> { isa => 'SCALAR' }
					,"projectGroupDescription"		=> { isa => 'SCALAR' }
					,"project4RPBGroupName"			=> { isa => 'SCALAR' }
					,"project4RPBGroupDescription"	=> { isa => 'SCALAR' }
					,"cityGroupName"				=> { isa => 'SCALAR' }
					,"cityId"						=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"pfpId"		=> { isa => 'SCALAR' }
					,"centralId"	=> { isa => 'SCALAR' }
					,"ring"		=> { isa => 'SCALAR' }
					,"pop"			=> { isa => 'SCALAR' }
					,"city"		=> { isa => 'SCALAR' }
					,"pfpName"		=> { isa => 'SCALAR' }
					,"province"	=> { isa => 'SCALAR' }
					,"country"		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# cerco se esiste un sistema aperto per il progetto
	my $sistemiProgetto = $self->cerca(
		customerId			=> $params{customerId},
		contractId			=> $params{contractId},
		projectId			=> $params{projectId},
		cadastralCode	=> $params{cadastralCode}
	);
	
	unless (defined $sistemiProgetto){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemiProgetto} != 0){
		$self->_art()->last_error(__x("Project {project} for customer_id {customer_id} and contract_id {contract_id} already present!", project => $params{projectId}, customer_id => $params{customerId}, contract_id => $params{contractId}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_PRJ_crea" );
	
	# prima di creare il progetto devo creare i relativi gruppi
	my $projectGroup = $self->_art()->create_group(
		NAME => $params{projectGroupName},
		DESCRIPTION => $params{projectGroupDescription},
		IS_AUTOGENERATED => 1
	);
	
	unless (defined $projectGroup){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_PRJ_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	my $project4RPBGroup = $self->_art()->create_group(
		NAME => $params{project4RPBGroupName},
		DESCRIPTION => $params{project4RPBGroupDescription},
		IS_AUTOGENERATED => 1
	);
	
	unless (defined $project4RPBGroup){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_PRJ_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'PROJECT'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"customerId"}, $params{"contractId"}, $params{"projectId"})
		, GROUPS => ['ADMIN', $params{projectGroupName}, $params{project4RPBGroupName}, $params{cityGroupName}]
		, PROPERTIES => {
			"customerId"			=> $params{"customerId"}
			,"contractId"			=> $params{"contractId"}
			,"projectId"			=> $params{"projectId"}
			,"cadastralCode"		=> $params{"cadastralCode"}
			,"cityId"				=> $params{"cityId"}
		}
	);
	
	for ("pfpId", "centralId", "ring", "pop", "city", "pfpName", "province", "country"){
		$create_params{PROPERTIES}->{$_} = $params{$_} if defined $params{$_};
	}
	
	my $system = $self->create (%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_PRJ_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOWORKS::Collection::System::PROJECT->new(ART => $art);
	
	my $ap = $coll->crea(
		"contractId" => "FTTH"
		, "customerId" => 'ENEL'
		, "projectId"	=> '889'
		, "projectGroupName" => 'PROJECT_ENEL_FTTH_889'
		, "projectGroupDescription" => 'PROJECT_ENEL_FTTH_889'
		, "project4RPBGroupName" => 'PROJECT4RPB_ENEL_FTTH_889'
		, "project4RPBGroupDescription" => 'PROJECT4RPB_ENEL_FTTH_889'
		, "cityId" => '2246'
		, "cadastralCode" => 'A794'
		, "cityGroupName" => 'CITY_2246'
		, "pfpId"		=> '889'
		, "centralId"	=> 'idCentrale'
		, "ring"		=> 'w3'
		, "pop"			=> 'PA - ROCCA'
		, "city"		=> 'PALERMO'
		, "pfpName"		=> 'PEM1'
		, "province"	=> 'PA'
		, "country"		=> 'ITALIA'
	);
	
	if( defined $ap) {
		get_logger()->info("OK: ".$ap->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
