package WPSOWORKS::Collection::System::MACROLAVORO;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

use WPSOWORKS;
use WPSOWORKS::Sync::MACROTASKS;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::MACROLAVORO";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});

	$self->{ART} = $params->{ART};
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _wpsoworks { shift->{WPSOWORKS} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
					,"category"			=> { isa => 'SCALAR' }
					,"subCategory"		=> { isa => 'SCALAR' }
					,"unitOfMeasure"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $search_params = {
		"customerId"		=> $params{customerId}
		,"contractId"		=> $params{contractId}
		,"projectId"		=> $params{projectId}
		,"category"			=> $params{category}
		,"subCategory"		=> $params{subCategory}
		,"unitOfMeasure"	=> $params{unitOfMeasure}
	};
	
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['MACROLAVORO']
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"			=> { isa => 'SCALAR' }
					,"contractId"			=> { isa => 'SCALAR' }
					,"projectId"			=> { isa => 'SCALAR' }
					,"estimatedDuration"	=> { isa => 'SCALAR' }
					,"category"				=> { isa => 'SCALAR' }
					,"subCategory"			=> { isa => 'SCALAR' }
					,"toAssignQuantity"		=> { isa => 'SCALAR' }
					,"unitOfMeasure"		=> { isa => 'SCALAR' }
					,"ring"					=> { isa => 'SCALAR' }
					,"ringId"				=> { isa => 'SCALAR' }
					,"permitsAreaId"		=> { isa => 'SCALAR' }
					,"CANTIERE"				=> { isa => undef, inherits => [ 'WPSOWORKS::System::CANTIERE' ] }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $props_cantiere = $params{CANTIERE}->property();
	
	# verifico che se l'unita' di misura e' 'nr' il valore di toAssignQuantity deve essere un intero
	if ($params{unitOfMeasure} eq 'nr' && $params{toAssignQuantity} !~/^\d+$/){
		$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, projectId {projectId}, category {category}, subCategory {subCategory}, toAssignQuantity {toAssignQuantity}: toAssignQuantity must be an integer", customerId => $props_cantiere->{customerId}, contractId => $props_cantiere->{contractId}, projectId => $props_cantiere->{projectId}, category => $params{category}, subCategory => $params{subCategory}, toAssignQuantity => $params{toAssignQuantity}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	
	# recupero i sistemi MACROLAVORO dello stesso tipo per verificare se la quantità assegnata è compatibile con quella già assegnata
	# che non deve mai superare la quantità da fare
	
	my $mt = eval {
		WPSOWORKS::Sync::MACROTASKS->new(
			 WPSOWORKS => $self->_wpsoworks()
			,CUSTOMER_ID => $props_cantiere->{customerId}
			,CONTRACT_ID => $props_cantiere->{contractId}
		)
	};
	
	$self->_art()->last_error("WPSOWORKS::Sync::MACROTASKS error: $@")
		&& return undef
			if ($@);
	
	my $search_params = {
		"projectId"			=> [$props_cantiere->{projectId}]
		,"permitsAreaId"		=> [$params{permitsAreaId}]
		,"category"				=> [$params{category}]
		,"subCategory"			=> [$params{subCategory}]
	};
	
	my $macrolavori = $mt->search(%{$search_params});
	return undef unless defined $macrolavori;
	
	if (scalar @{$macrolavori} > 1){
		$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, projectId {projectId}, category {category}, subCategory {subCategory}: found {value} records. Please contact system administrator", customerId => $props_cantiere->{customerId}, contractId => $props_cantiere->{contractId}, projectId => $props_cantiere->{projectId}, category => $params{category}, subCategory => $params{subCategory}, value => scalar @{$macrolavori}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$macrolavori} == 1){
		$macrolavori->[0]->{toDoQuantity} =~ s/,/\./;
		if ($params{toAssignQuantity} > $macrolavori->[0]->{toDoQuantity}){
			$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, projectId {projectId}, category {category}, subCategory {subCategory}: maximum assignable quantity {value}", customerId => $props_cantiere->{customerId}, contractId => $props_cantiere->{contractId}, projectId => $props_cantiere->{projectId}, category => $params{category}, subCategory => $params{subCategory}, value => $macrolavori->[0]->{toDoQuantity}));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}	
	} elsif (scalar @{$macrolavori} == 0){
		$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, projectId {projectId}, category {category}, subCategory {subCategory}: unable to find record. Please contact system administrator", customerId => $props_cantiere->{customerId}, contractId => $props_cantiere->{contractId}, projectId => $props_cantiere->{projectId}, category => $params{category}, subCategory => $params{subCategory}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	# i gruppi sono gli stessi del CANTIERE associato
	my $groups = $params{CANTIERE}->info('GROUPS');
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'MACROLAVORO'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"category"}, $params{"toAssignQuantity"}, $params{"unitOfMeasure"}) 
		, GROUPS => $groups
		, PROPERTIES => {
			"customerId"		=> $params{"customerId"},
			"contractId"		=> $params{"contractId"},
			"projectId"			=> $params{"projectId"},
			"estimatedDuration"	=> $params{"estimatedDuration"},
			"category"			=> $params{"category"},
			"subCategory"		=> $params{"subCategory"},
			"toAssignQuantity"	=> $params{"toAssignQuantity"},
			"unitOfMeasure"		=> $params{"unitOfMeasure"},
			"ring"				=> $params{"ring"},
			"ringId"			=> $params{"ringId"},
			"permitsAreaId"		=> $params{"permitsAreaId"}
		}
	);

	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_MACRO_crea" );
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_MACRO_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	# se ho creato il sistema lo faccio adottare
	my $adozione = $params{CANTIERE}->adopt_children(CHILDREN => [$system]);

	unless (defined $adozione){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_MACRO_crea" );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::CANTIERE->new(ART => $art);
	
#	my $cerca = $coll->cerca(
#		"customerId"	=> 'ENEL'
#		,"contractId"	=> 'FTTH'
#		,"projectId"	=> '1003'
#		,"fromNetworkElementId" => 1028
#	);
#	if( defined $cerca) {
#		get_logger()->info("OK: trovati ".scalar @{$cerca}. " cavi");
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	
	my $cavo = $coll->crea(
		"customerId"	=> 'ENEL'
		,"contractId"	=> 'FTTH'
		,"projectId"	=> '1003'
		,"cadastralCode"	=> "F839"
		,"workingGroupCode" => 131333
		,"pop" => 'NA - FUORIGROTTA'
		,"popId"	=> "GAZCALJAJZAS2RSVJ5JESR2SJ5KFIQI"
		,"ring" => 131333
		,"ringId"	=> "GEYUK"
		,"category"		=> 'SCAVO'
		,"subCategory"	=> 'SCAVO nessuna specializzazione'
		,"unitOfMeasure"	=> 'mt'
		,"estimatedDuration"	=> 50
	);
	
	if( defined $cavo) {
		get_logger()->info("OK: ".$cavo->id(). " ". $cavo->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
