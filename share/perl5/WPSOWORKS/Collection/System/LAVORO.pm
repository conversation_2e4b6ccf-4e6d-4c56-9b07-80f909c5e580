package WPSOWORKS::Collection::System::LAVORO;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

use WPSOWORKS;
use WPSOWORKS::Collection::System::NETWORK;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::LAVORO";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{collNetwork} = WPSOWORKS::Collection::System::NETWORK->new(ART => $params->{ART});
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _wpsoworks { shift->{WPSOWORKS} }

sub _get_coll_network { shift->{collNetwork} }

sub get_db{shift->_art()->_dbh()}

#sub cerca{
#	my $self = shift;
#	my %params = @_;
#	
#	my $errmsg;
#	$self->_art()->last_error($errmsg)
#		and return undef
#			unless $self->_art()->check_named_params(
#				 ERRMSG		=> \$errmsg
#				,PARAMS		=> \%params
#				,MANDATORY	=> {
#					"customerId"		=> { isa => 'SCALAR' }
#					,"contractId"		=> { isa => 'SCALAR' }
#					,"projectId"		=> { isa => 'SCALAR' }
#				}
#				,OPTIONAL	=> {
#					"cableId"			=> { isa => 'SCALAR' }
#					,"fromNetworkElementId"			=> { isa => 'SCALAR' }
#				}
#				,IGNORE_EXTRA_PARAMS => 1
#	);
#	
#	my $search_params = {
#		"customerId"	=> $params{customerId}
#		,"contractId"	=> $params{contractId}
#		,"projectId"	=> $params{projectId}
#	};
#	
#	for ('cableId', 'fromNetworkElementId'){
#		$search_params->{$_} = $params{$_} if defined $params{$_};
#	}
#	
#	return $self->_cerca(
#		PROPERTIES => $search_params
#	);
#}
#
#sub _cerca{
#	my $self = shift;
#	my %params = @_;
#	
#	# imposto i filtri relativi al progetto
#	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
#	
#	# cerco i sistemi aperti con property
#	my $systems = $self->find_object(
#		SYSTEM_TYPE_NAME	=> ['CAVO']
#		, ACTIVE			=> 1
#		, SHOW_ONLY_WITH_VISIBILITY => 1
#		, %params
#	);
#	
#	unless (defined $systems){
#		$self->_logger()->error( $self->_art()->last_error() );
#		return undef;
#	}
#	
#	return $systems;
#}
#
sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"workingGroupCode"	=> { isa => 'SCALAR' }
					,"description"		=> { isa => 'SCALAR' }
					,"workType"			=> { isa => 'SCALAR', list => [ 'Network', 'NetworkFibercop', 'ROE', 'PreeMaintenance','CorrMaintenance', 'ExtrMaintenance', 'PTE', 'PreeMaintenance01', 'ExtrMaintenance01' ] }
					,"details"			=> { isa => 'ARRAY' }
				}
				,OPTIONAL	=> {
					"subContractCode"		=> { isa => 'SCALAR' }
					,"accountingOperation"	=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'LAVORO'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => $params{"description"}
		, PROPERTIES => {
			"requestType"					=> $params{"workType"}, #cablatura che serve per l'interazione con il GW
			"customerId"					=> $params{"customerId"},
			"contractId"					=> $params{"contractId"},
			"workingGroupCode"				=> $params{"workingGroupCode"},
			"workType"						=> $params{"workType"},
		}
	);
	
	if ($params{workType} =~/^(Network|NetworkFibercop|ROE|PTE)$/){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"assetId"			=> { isa => 'ARRAY' }
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		$create_params{PROPERTIES}->{assetId} = $params{"assetId"};
	} elsif ($params{workType} =~/^(PreeMaintenance|CorrMaintenance|ExtrMaintenance|PreeMaintenance01|ExtrMaintenance01)$/){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"maintenanceId"		=> { isa => 'SCALAR' }
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		$create_params{PROPERTIES}->{maintenanceId} = $params{"maintenanceId"};
	}
	
	my $parent_system;
	
	# per ogni dettaglio effettuo le verifiche
	for my $d (@{$params{details}}){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> $d
					,MANDATORY	=> {
						"type"		=> {
							isa => 'SCALAR',
							list => [
								'updateDatabase',
								'test',
								'junction',
								'civil',
								'cableLaying',
								'survey',
								'updateDatabaseF1',
								'updateDatabaseF2',
								'opticalConnectionOSU',
								'opticalConnectionOLT',
								'restoration',
								'design',
								'patchCord',
								'pathSurvey',
								'installationPlaceSurvey',
								'installationReview',
								'worksManning',
								'measurements',
								'quarterSummary',
								'defectWithDisservice',
								'defectWithoutDisservice',
								'infrastructure',
								'laying',
								'testApp',
								'testOTDR',
								'generic',
								'planning'
							]
						}
					}
					,OPTIONAL	=> {}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		# per ogni dettaglio aggiunto il dato tecnico relativo
		$create_params{PROPERTIES}->{$d->{type}} = 1;
	}
	
	# verifico i parametri obbligatorio per le varie gestioni
	if ($params{workType} =~/^(Network|NetworkFibercop)$/){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"networkId"		=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		$create_params{PROPERTIES}->{networkId} = $params{networkId};
		# per compatiblità deve avere anche projectId = networkId
		$create_params{PROPERTIES}->{projectId} = $params{networkId};
		$create_params{PROPERTIES}->{accountingOperation} = $params{accountingOperation} if $params{accountingOperation};
		
		# recupero il relativo sistema NETWORK che deve essere già presente a sistema
		my $networks = $self->_get_coll_network()->cerca(
			customerId => $params{"customerId"}
			,contractId => $params{"contractId"}
			,networkId => $params{"networkId"}
		);
		return undef unless defined $networks;
		
		if (scalar @{$networks} == 0){
			$self->_art()->last_error(__x("No network {networkId} found for customer {customerId} and contract {contractId}", networkId => $params{"networkId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		} elsif (scalar @{$networks} > 1){
			$self->_art()->last_error(__x("Found {n} network with id {networkId} for customer {customerId} and contract {contractId}", n => scalar @{$networks}, networkId => $params{"networkId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		$parent_system = $networks->[0];
		
		my $orig_children_class = $WPSOWORKS::System::NETWORK::DEFAULT_CHILDREN_CLASS;
		$WPSOWORKS::System::NETWORK::DEFAULT_CHILDREN_CLASS = "WPSOWORKS::System::LAVORO";
		
		# dalla network recupero tutti i sistemi figlio di tipo LAVORO
		my $children = $parent_system->get_children(SYSTEM_TYPE_NAME => ['LAVORO']);
		$WPSOWORKS::System::NETWORK::DEFAULT_CHILDREN_CLASS = $orig_children_class;
		unless (defined $children){
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		$create_params{PROPERTIES}->{externalSequence} = scalar @{$children} == 0 ? '10' : ((scalar @{$children})+1)*10;
		
	} elsif ($params{workType} eq 'ROE'){
		# per i ROE non può esistere un lavoro aperto dello stesso tipo
		for my $d (@{$params{details}}){
			# per ogni asset devo verificarlo
			for my $asset (@{$create_params{PROPERTIES}->{assetId}}){
				
				my $sistemi_aperti = $self->cerca(
					"customerId"	=> $params{"customerId"},
					"contractId"	=> $params{"contractId"},	
					"workType"		=> $create_params{PROPERTIES}->{workType},
					"assetId"		=> $asset,
					"type"			=> $d->{type}
				);
				return undef unless defined $sistemi_aperti;
				if (scalar @{$sistemi_aperti}){
					#$self->_art()->last_error(__x("Found work ongoing of type {workType} for ROE {id}", workType => $d->{type}, id => $sistemi_aperti->[0]->id()));
					$self->_art()->last_error(__x("Found work ongoing of type {workType} for ROE {id}", workType => $d->{type}, id => $asset));
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
			}
		}
		
		if (defined $params{networkId}){
			$create_params{PROPERTIES}->{networkId} = $params{networkId};
			# per compatiblità deve avere anche projectId = networkId
			$create_params{PROPERTIES}->{projectId} = $params{networkId};
		}
	}
	
	$create_params{GROUPS} = [
		'ADMIN',
		'PM_'.$params{"customerId"}.'_'.$params{"contractId"},
		'CL_'.$params{"workingGroupCode"},
		'REMOTE_AUTOMATION',
		'REMOTE_EVENT',
	];
	
	# aggiungo l'eventuale gruppo del subappalto
	if (defined $params{subContractCode}){
		my $serviceGroupName = 'SERVICE_'.$params{subContractCode};
		# se il gruppo esiste già sull'istanza lo aggiunto alla visibilità del LAVORO
		if ($self->_art()->test_group_name($serviceGroupName)){
			push @{$create_params{GROUPS}}, $serviceGroupName unless grep {$_ eq $serviceGroupName} @{$create_params{GROUPS}};
		}
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_LAVORO_c" );
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_LAVORO_c" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	if ($params{workType} =~/^(Network|NetworkFibercop)$/){
		# l'oggetto creato deve diventare figlio
		unless (defined $parent_system->adopt_children(CHILDREN => [$system])){
			$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_LAVORO_c" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
	}
	
	return $system;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					, "workType"	=> { isa => 'SCALAR' }
					, "type"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"assetId"			=> { isa => 'SCALAR' }
					, "maintenanceId"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	# NOTA: se il parametro workType contiene dei tipi sistema errati o non collegati
	# al tipo attività LAVORO non scoppia ma restituisce 0 record
	 
	my $searchParam = {
		 SYSTEM_TYPE_NAME	=> ['LAVORO'],
		 ACTIVE				=> 1
	};
	
	for my $k ("customerId", "contractId", "workType") {
		$searchParam->{PROPERTIES}->{$k} = $params{$k};
	}
	for my $k ("assetId", "maintenanceId") {
		$searchParam->{PROPERTIES}->{$k} = $params{$k} if defined $params{$k};
	}
	
	$searchParam->{PROPERTIES}->{$params{type}} = 1;
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $works = $self->find_object(%{$searchParam});
	
	unless (defined $works){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $works;
}


if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::LAVORO->new(ART => $art);
	
#	my $cerca = $coll->cerca(
#		"customerId"	=> 'ENEL'
#		,"contractId"	=> 'FTTH'
#		,"projectId"	=> '1003'
#		,"fromNetworkElementId" => 1028
#	);
#	if( defined $cerca) {
#		get_logger()->info("OK: trovati ".scalar @{$cerca}. " cavi");
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	
	my $lavoro = $coll->crea(
		"customerId"	=> 'ENEL'
		,"contractId"	=> 'FTTH'
#		,"projectId"	=> '1003'
#		,"cableId"		=> '2617459'
#		,"workingGroupCode" => 131333
#		,"ring" => 131333
#		,"pop" => 'NA - FUORIGROTTA'
#		,"cadastralCode"	=> "F839"
#		,"popId"	=> "GAZCALJAJZAS2RSVJ5JESR2SJ5KFIQI"
#		,"ringId"	=> "GEYUK"
#		,"workingGroupCode" => 131333
	);
	
	if( defined $lavoro) {
		get_logger()->info("OK: ".$lavoro->id(). " ". $lavoro->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
