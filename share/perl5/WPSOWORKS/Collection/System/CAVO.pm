package WPSOWORKS::Collection::System::CAVO;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

use WPSOWORKS;
use WPSOWORKS::Collection::System::PROJECT;
use WPSOWORKS::Sync::CAVI;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::CAVO";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{collProject} = WPSOWORKS::Collection::System::PROJECT->new(ART => $params->{ART});
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _wpsoworks { shift->{WPSOWORKS} }

sub _get_coll_project { shift->{collProject} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"cableId"			=> { isa => 'SCALAR' }
					,"fromNetworkElementId"			=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $search_params = {
		"customerId"	=> $params{customerId}
		,"contractId"	=> $params{contractId}
		,"projectId"	=> $params{projectId}
	};
	
	for ('cableId', 'fromNetworkElementId'){
		$search_params->{$_} = $params{$_} if defined $params{$_};
	}
	
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['CAVO']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"			=> { isa => 'SCALAR' }
					,"contractId"			=> { isa => 'SCALAR' }
					,"cadastralCode"		=> { isa => 'SCALAR' }
					,"pop"					=> { isa => 'SCALAR' }
					,"popId"				=> { isa => 'SCALAR' }
					,"ring"					=> { isa => 'SCALAR' }
					,"ringId"				=> { isa => 'SCALAR' }
					,"cableId"				=> { isa => 'SCALAR' }
					,"workingGroupCode"		=> { isa => 'SCALAR' }
					,"projectId"			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# recupero le informazioni del cavo
	
	my $mt = eval {
		WPSOWORKS::Sync::CAVI->new(
			 WPSOWORKS => $self->_wpsoworks()
			,CUSTOMER_ID => $params{"customerId"}
			,CONTRACT_ID => $params{"contractId"}
		)
	};
	
	$self->_art()->last_error("WPSOWORKS::Sync::CAVI error: $@")
		&& return undef
			if ($@);
	
	my $search_params = {
		"cableId"		=> [$params{"cableId"}]
		,"projectId"	=> [$params{"projectId"}]
	};
	
	my $cavi = $mt->search(%{$search_params});
	return undef unless defined $cavi;
	
	if (scalar @{$cavi} > 1){
		$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, cableId {cableId}: found {value} records. Please contact system administrator", customerId => $params{"customerId"}, contractId => $params{"contractId"}, cableId => $params{"cableId"}, value => scalar @{$cavi}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$cavi} == 0){
		$self->_art()->last_error(__x("Customer {customerId}, contract {contractId}, cableId {cableId}: unable to find record. Please contact system administrator", customerId => $params{"customerId"}, contractId => $params{"contractId"}, cableId => $params{"cableId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$cavi} == 1){
		# verifico le chiavi integer
		for (
			'fromStockLength'
			,'toStockLength'
		) {
			if (defined $cavi->[0]->{$_} && $cavi->[0]->{$_} !~/^\d+$/){
				$self->_art()->last_error(__x("{param} must be an integer", param => $_));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
		}
		
		# converto le chiavi numeriche
		for (
			'plannedFacadeLength'
			,'doneUndergroundLength'
			,'verticalLength'
			,'fromStockLength'
			,'horizontalLength'
			,'doneAerialLength'
			,'plannedUndergroundLength'
			,'plannedAerialLength'
			,'estimatedDuration'
			,'doneHandmaidLength'
			,'doneFacadeLength'
			,'toStockLength'
		) {
			$cavi->[0]->{$_} =~ s/,/\./ if defined $cavi->[0]->{$_};
		}
	}
	
	# recupero il relativo sistema PROJECT che deve essere già presente a sistema
	my $projects = $self->_get_coll_project()->cerca(
		customerId => $params{"customerId"}
		,contractId => $params{"contractId"}
		,projectId => $cavi->[0]->{"projectId"}
	);
	return undef unless defined $projects;
	
	if (scalar @{$projects} == 0){
		$self->_art()->last_error(__x("No project {projectId} found for customer {customerId} and contract {contractId}", projectId => $cavi->[0]->{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$projects} > 1){
		$self->_art()->last_error(__x("Found {n} projects with id {projectId} for customer {customerId} and contract {contractId}", n => scalar @{$projects}, projectId => $cavi->[0]->{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my $project_properties = $projects->[0]->property();
	
	#dato che ho trovato il progetto posso utilizzare il dato tecnico sistema per recuperare il cityId
	
	my $cityGroupName = 'CITY_'.$project_properties->{'cityId'};
	
	# il sistema deve avere come visiblità ADMIN, CITY_city1 e PROJECT_ cust1_cont1_prog1
	my $projectGroupName = 'PROJECT_'.$params{"customerId"}."_".$params{"contractId"}."_".$cavi->[0]->{"projectId"};
	
	# verifico che non ci sia già un sistema cavo aperto
	my $cables = $self->cerca(
		"customerId"	=> $params{"customerId"},
		"contractId"	=> $params{"contractId"},
		"projectId"		=> $cavi->[0]->{"projectId"},
		"cableId"		=> $params{"cableId"}
	);
	return undef unless defined $cables;
	
	if (scalar @{$cables}){
		$self->_art()->last_error(__x("Cable {cableId} already present for customer {customerId}, contract {contractId} and project {projectId}", cableId => $params{"cableId"}, projectId => $cavi->[0]->{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'CAVO'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"cableId"}, $cavi->[0]->{"cableName"})
		, GROUPS => ['ADMIN' ,$projectGroupName, $cityGroupName]
		, PROPERTIES => {
			"requestType"		=> 'CAVO', #cablatura che serve per l'interazione con il GW
			"customerId"		=> $params{"customerId"},
			"contractId"		=> $params{"contractId"},
			"cityId"			=> $project_properties->{'cityId'},
			"cadastralCode" 	=> $params{"cadastralCode"},
			"pop"				=> $params{"pop"},
			"popId"				=> $params{"popId"},
			"ring"				=> $params{"ring"},
			"ringId"			=> $params{"ringId"},
			"cableId"			=> $params{"cableId"},
			"workingGroupCode"	=> $params{"workingGroupCode"},
			"pop"				=> $project_properties->{"pop"},
			"pfpName"			=> $project_properties->{"pfpName"},
			"projectId"			=> $cavi->[0]->{"projectId"},
			"cableName"			=> $cavi->[0]->{"cableName"},
			"cableType"			=> $cavi->[0]->{"cableType"},
			"unitOfMeasure"		=> $cavi->[0]->{"unitOfMeasure"},
			"estimatedDuration"	=> $cavi->[0]->{"estimatedDuration"},
		}
	);
	
	# campi opzionali
	for (
		"plannedAerialLength"
		,"potential"
		,"fromNetworkElementId"
		,"toNetworkElementName"
		,"plannedFacadeLength"
		,"plannedUndergroundLength"
		,"toNetworkElementId"
		,"fromNetworkElementName"
		,"fromNetworkElementType"
		,"toNetworkElementType"
		,"fromNetworkElementGeoLocation"
		,"toNetworkElementGeoLocation"
		,"plannedToStockLength"
		,"plannedFromStockLength"
	){
		$create_params{PROPERTIES}->{$_} = $cavi->[0]->{$_} if defined $cavi->[0]->{$_};
	}
	
	# alcune info oltre ad essere opzionali sono anche da rimappare
	my $remap_param = {
		fromStockLength => 'plannedFromStockLength'
		, toStockLength => 'plannedToStockLength'
	};
	for my $k (keys %{$remap_param}){
		$create_params{PROPERTIES}->{$remap_param->{$k}} = $cavi->[0]->{$k} if defined $cavi->[0]->{$k};
	}
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_CAVO_crea" );
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_CAVO_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::CAVO->new(ART => $art);
	
#	my $cerca = $coll->cerca(
#		"customerId"	=> 'ENEL'
#		,"contractId"	=> 'FTTH'
#		,"projectId"	=> '1003'
#		,"fromNetworkElementId" => 1028
#	);
#	if( defined $cerca) {
#		get_logger()->info("OK: trovati ".scalar @{$cerca}. " cavi");
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	
	my $cavo = $coll->crea(
		"customerId"	=> 'ENEL'
		,"contractId"	=> 'FTTH'
		,"projectId"	=> '1003'
		,"cableId"		=> '2617459'
		,"workingGroupCode" => 131333
		,"ring" => 131333
		,"pop" => 'NA - FUORIGROTTA'
		,"cadastralCode"	=> "F839"
		,"popId"	=> "GAZCALJAJZAS2RSVJ5JESR2SJ5KFIQI"
		,"ringId"	=> "GEYUK"
		,"workingGroupCode" => 131333
	);
	
	if( defined $cavo) {
		get_logger()->info("OK: ".$cavo->id(). " ". $cavo->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
