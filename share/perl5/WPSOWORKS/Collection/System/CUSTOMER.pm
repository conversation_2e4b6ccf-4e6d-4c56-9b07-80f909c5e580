package WPSOWORKS::Collection::System::CUSTOMER;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::CUSTOMER";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
	
	# Controlli sui parametri
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
				}
				,OPTIONAL	=> {
					"customerId"		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $search_params = {};
		
	$search_params->{customerId} = $params{customerId} if defined $params{customerId};
			
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['CUSTOMER']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 customerId		=> { isa => 'SCALAR' }
					,customerName	=> { isa => 'SCALAR' }
					,context		=> { isa => 'SCALAR' } # l'elenco dei lavori possibile è gestito dal metodo WPSOWORKS::System::CUSTOMER::add_context
				}
				,OPTIONAL => {
					 backgroundURL	=> { isa => 'SCALAR' }
					,logoURL		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	# verifico se esiste un customer con lo stesso customerId
	my $customers = $self->cerca( customerId => $params{customerId} );
	return undef
		unless defined $customers;
	
	$self->art()->last_error(__("customerId already present!"))
		&& return undef
			if scalar @{$customers};
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'CUSTOMER'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => $params{customerId}
		, GROUPS => ['ADMIN']
		, PROPERTIES => {
			 "customerId"			=> $params{customerId}
			,"customerName"			=> $params{customerName}
		}
	);
	
	$create_params{PROPERTIES}->{backgroundURL}	= $params{"backgroundURL"}	if defined $params{"backgroundURL"};
	$create_params{PROPERTIES}->{logoURL}		= $params{"logoURL"}		if defined $params{"logoURL"};
	
	$self->_art()->_dbh()->do( "savepoint WPSOWORKS_Cll_Sys_CUSTOMER_crea" );
	
	my $system = $self->create(%create_params);
	return undef
		unless defined $system;
	
	# aggiungo il gruppo relativo al contesto per la corretta visibilità
	unless ($system->add_context(context => $params{"context"})){
		$self->_art()->last_error( "Impossibile aggiungere il gruppo al sistema: ". $self->_art()->last_error());
		$self->_art()->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_CUSTOMER_crea" );
		return undef;
	}
	
	$self->_logger()->debug("Info: ".Dumper($system->info()));
	$self->_logger()->debug("Properties: ".Dumper($system->property()));
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	#eval "use WPSOWORKS::System::CONTRACT;";

	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::CUSTOMER->new(ART => $art);
	
	my $customer = $coll->crea(
		"customerId"		=> "TELECOM"
	   ,"customerName"	 	=> "Open Fiber"
	);
	
	if( defined $customer) {
		get_logger()->info("OK: creato customer con ID $customer");
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
#	my $aps = $coll->cerca(
#		"permitsAreaId" => '1'
#	);
#	
#	if( defined $aps) {
#		get_logger()->info("OK: trovate ".scalar (@{$aps}). " attività");
#		for my $ap (@{$aps}){
#			get_logger()->info("OK: ".$ap->id(). "(".$ap->name().")");
#		}
#		#print STDERR Dumper $ap_lc->property();
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
