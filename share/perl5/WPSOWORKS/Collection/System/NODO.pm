package WPSOWORKS::Collection::System::NODO;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );


use WPSOWORKS::Collection::System::PROJECT;

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOWORKS::System::NODO";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOWORKS::LIB::' . __PACKAGE__ );
	
	$self->{collProject} = WPSOWORKS::Collection::System::PROJECT->new(ART => $params->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _get_coll_project { shift->{collProject} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					,"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"networkElementId"			=> { isa => 'SCALAR' }
#					,"fromNetworkElementId"			=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $search_params = {
		"customerId"	=> $params{customerId}
		,"contractId"	=> $params{contractId}
		,"projectId"	=> $params{projectId}
	};
	
	for ('networkElementId'){
		$search_params->{$_} = $params{$_} if defined $params{$_};
	}
	
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['NODO']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"			=> { isa => 'SCALAR' }
					,"contractId"			=> { isa => 'SCALAR' }
					,"projectId"			=> { isa => 'SCALAR' }
					,"cadastralCode"		=> { isa => 'SCALAR' }
					,"pop"					=> { isa => 'SCALAR' }
					,"popId"				=> { isa => 'SCALAR' }
					,"ring"					=> { isa => 'SCALAR' }
					,"ringId"				=> { isa => 'SCALAR' }
					,"networkElementId"		=> { isa => 'SCALAR' }
					,"networkElementName"	=> { isa => 'SCALAR' }
					,"networkElementType"	=> { isa => 'SCALAR' }
					,"workingGroupCode"		=> { isa => 'SCALAR' }
					,"latitude"				=> { isa => 'SCALAR' }
					,"longitude"			=> { isa => 'SCALAR' }
					,"estimatedDuration"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# recupero il relativo sistema PROJECT che deve essere già presente a sistema
	my $projects = $self->_get_coll_project()->cerca(
		customerId => $params{"customerId"}
		,contractId => $params{"contractId"}
		,projectId => $params{"projectId"}
	);
	return undef unless defined $projects;
	
	if (scalar @{$projects} == 0){
		$self->_art()->last_error(__x("No project {projectId} found for customer {customerId} and contract {contractId}", projectId => $params{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	} elsif (scalar @{$projects} > 1){
		$self->_art()->last_error(__x("Found {n} projects with id {projectId} for customer {customerId} and contract {contractId}", n => scalar @{$projects}, projectId => $params{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my $project_properties = $projects->[0]->property();
	
	#dato che ho trovato il progetto posso utilizzare il dato tecnico sistema per recuperare il cityId
	
	my $cityGroupName = 'CITY_'.$project_properties->{'cityId'};
	
	# il sistema deve avere come visiblità ADMIN, CITY_city1 e PROJECT_ cust1_cont1_prog1
	my $projectGroupName = 'PROJECT_'.$params{"customerId"}."_".$params{"contractId"}."_".$params{"projectId"};
	
	# verifico che non ci sia già un sistema cavo aperto
	my $nets = $self->cerca(
		"customerId"		=> $params{"customerId"},
		"contractId"		=> $params{"contractId"},
		"projectId"			=> $params{"projectId"},
		"networkElementId"	=> $params{"networkElementId"}
	);
	return undef unless defined $nets;
	
	if (scalar @{$nets}){
		$self->_art()->last_error(__x("Network element {networkElementId} already present for customer {customerId}, contract {contractId} and project {projectId}", networkElementId => $params{"networkElementId"}, projectId => $params{"projectId"}, customerId => $params{"customerId"}, contractId => $params{"contractId"}));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	
	$self->get_db()->do( "savepoint WPSOWORKS_Cll_Sys_NODO_crea" );
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'NODO'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOWORKS'
		, DESCRIPTION => join("-", $params{"networkElementId"}, $params{"networkElementName"}) 
		, GROUPS => ['ADMIN' ,$projectGroupName, $cityGroupName]
		, PROPERTIES => {
			"requestType"			=> 'GIUNZIONE', #cablatura che serve per l'interazione con il GW
			"customerId"			=> $params{"customerId"},
			"contractId"			=> $params{"contractId"},
			"projectId"				=> $params{"projectId"},
			"cityId"				=> $project_properties->{'cityId'},
			"cadastralCode" 		=> $params{"cadastralCode"},
			"pop"					=> $params{"pop"},
			"popId"					=> $params{"popId"},
			"ring"					=> $params{"ring"},
			"ringId"				=> $params{"ringId"},
			"networkElementId"		=> $params{"networkElementId"},
			"networkElementName"	=> $params{"networkElementName"},
			"networkElementType"	=> $params{"networkElementType"},
			"latitude"				=> $params{"latitude"},
			"longitude"				=> $params{"longitude"},
			"estimatedDuration"		=> $params{"estimatedDuration"},
			"workingGroupCode"		=> $params{"workingGroupCode"},
			"pfpName"				=> $project_properties->{"pfpName"},
			"pop"					=> $project_properties->{"pop"}
		}
	);
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_NODO_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	#eval "use WPSOWZ::System::AP;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOWORKS::Collection::System::NODO->new(ART => $art);
	
	my $cerca = $coll->cerca(
		"customerId"	=> 'ENEL'
		,"contractId"	=> 'FTTH'
		,"projectId"	=> '1003'
		,"fromNetworkElementId" => 1028
	);
	if( defined $cerca) {
		get_logger()->info("OK: trovati ".scalar @{$cerca}. " cavi");
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	
	my $cavo = $coll->crea(
		"customerId"			=>	'ENEL'
		,"contractId"			=> 'FTTH'
		,"projectId"			=> '1003'
		,"workingGroupCode"		=> 131333
		,"ring"					=> 131333
		,"pop"					=> 'NA - FUORIGROTTA'
		,"cadastralCode"				=> "F839"
		,"popId"				=> "GAZCALJAJZAS2RSVJ5JESR2SJ5KFIQI"
		,"ringId"				=> "GEYUK"
		,"networkElementId"		=> '123123123'
		,"networkElementName"	=> '123123123'
		,"networkElementType"	=> 'CAVETTO'
		,"latitude"				=> '40.5555'
		,"longitude"			=> '1.5555'
		,"estimatedDuration"	=> 100
	);
	
	if( defined $cavo) {
		get_logger()->info("OK: ".$cavo->id(). " ". $cavo->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
