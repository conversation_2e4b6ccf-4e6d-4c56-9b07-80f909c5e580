package WPSOWORKS::MQ::Sender::System::WZ;

use strict;
use warnings;

use SIRTI::ART::RemoteActivity::Source;

use base 'SIRTI::Base::Singleton';

#override metodi Class::Singleton
sub _new_instance {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	die 'Missing mandatory param DB' unless $params{DB};
	
	$self->{WPSOWORKS_AP_SOURCE_SERVICE}			= 'ENFTTH_WORKS';
	$self->{WPSOWORKS_AP_SOURCE_CONTEXT}			= 'WORK_ZONE';
	$self->{WPSOWORKS_AP_TARGET_SERVICE}			= 'ENFTTH_AP';
	$self->{WPSOWORKS_AP_TARGET_CONTEXT}			= 'AREA_PERMESSI';
	$self->{WPSOWORKS_AP_SESSION_DESCRIPTION}	= 'WPSOWORKS - Comunicazioni verso AreaPermessi per recuperare info';
	
	$self->{DB} = $params{DB};
	
	return $self;	
}

sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}
#fine override metodi Class::Singleton

sub get_info_permits_area  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_works_ap->insert(
		EVENT 		=> 'GET_INFO',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
		NEED_ACK	=> 1,
	);
}

sub _get_ra_works_ap {
	my $self = shift;
	
	$self->{RA_WORKS_AP} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SOURCE_SERVICE			=> $self->{WPSOWORKS_AP_SOURCE_SERVICE}
		,SOURCE_CONTEXT			=> $self->{WPSOWORKS_AP_SOURCE_CONTEXT}
		,TARGET_SERVICE			=> $self->{WPSOWORKS_AP_TARGET_SERVICE}
		,TARGET_CONTEXT			=> $self->{WPSOWORKS_AP_TARGET_CONTEXT}
		,SESSION_DESCRIPTION	=> $self->{WPSOWORKS_AP_SESSION_DESCRIPTION}
	) unless defined $self->{RA_WORKS_AP};
	
	return $self->{RA_WORKS_AP};
}

1;

