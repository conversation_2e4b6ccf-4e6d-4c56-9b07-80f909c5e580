package WPSOWORKS::MQ::Sender::Activity::LAVORO;

use strict;
use warnings;

use SIRTI::ART::RemoteActivity::Source;

use base 'SIRTI::Base::Singleton';

#override metodi Class::Singleton
sub _new_instance {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	die 'Missing mandatory param DB' unless $params{DB};
	
	$self->{WPSOWORKS_GW_SOURCE_SERVICE}		= 'ENFTTH_WORKS';
	$self->{WPSOWORKS_GW_SOURCE_CONTEXT}		= 'LAVORI';
	$self->{WPSOWORKS_GW_TARGET_SERVICE}		= 'ENFTTH_WORKSGW';
	$self->{WPSOWORKS_GW_TARGET_CONTEXT}		= 'LAVORI';
	$self->{WPSOWORKS_GW_SESSION_DESCRIPTION}	= 'WPSOWORKS - Comunicazioni verso GW per richiesta nuovo lavoro';
	
	$self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_SOURCE_CONTEXT}		= 'LAVORO';
	$self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_TARGET_CONTEXT}		= 'RENDICONTAZIONE';
	$self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_SESSION_DESCRIPTION}	= 'WPSOWORKS - Comunicazioni a SiNFO relative all\'aggiornamento della rendicontazione';
	
	$self->{WPSOWORKS_CORE_NOTIFY_SOURCE_SERVICE}		= 'ENFTTH_WORKS';
	$self->{WPSOWORKS_CORE_NOTIFY_SOURCE_CONTEXT}		= 'LAVORI';
	$self->{WPSOWORKS_CORE_NOTIFY_TARGET_SERVICE}		= 'ENFTTH_CORE';
	$self->{WPSOWORKS_CORE_NOTIFY_TARGET_CONTEXT}		= 'LAVORI';
	$self->{WPSOWORKS_CORE_NOTIFY_SESSION_DESCRIPTION}	= 'WPSOWORKS - Comunicazioni movimentazioni lavoro';
	
	$self->{DB} = $params{DB};
	
	return $self;	
}

sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}
#fine override metodi Class::Singleton

sub new_work  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_works_gw->insert(
		EVENT 		=> 'NEW',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
		NEED_ACK	=> 1,
	);
}

sub cancel_work  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_works_gw->insert(
		EVENT 		=> 'CANCEL',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
		NEED_ACK	=> 1,
	);
}

sub update_work  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_works_gw->insert(
		EVENT 		=> 'UPDATE',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA}
	);
}

sub update_accounting  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_wpworks_sinfo_update_rendicontazione->insert(
		EVENT 		=> 'UPDATE_ACCOUNTING',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
		NEED_ACK	=> $params{NEED_ACK},
	);
}

sub notify_work_open  {
	my $self = shift;
	my %params = @_;

	$params{TYPE} = $params{TYPE} eq 'NETWORKFIBERCOP' ? 'NETWORK' :
			$params{TYPE} eq 'PREEMAINTENANCE01' ? 'PREEMAINT01' :
			$params{TYPE} eq 'EXTRMAINTENANCE01' ? 'EXTRMAINT01' : $params{TYPE};
	
	return $self->_get_ra_works_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_OPEN',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub notify_work_close_ok  {
	my $self = shift;
	my %params = @_;

	$params{TYPE} = $params{TYPE} eq 'NETWORKFIBERCOP' ? 'NETWORK' :
			$params{TYPE} eq 'PREEMAINTENANCE01' ? 'PREEMAINT01' :
			$params{TYPE} eq 'EXTRMAINTENANCE01' ? 'EXTRMAINT01' : $params{TYPE};
	
	my $p = {
		EVENT 			=> $params{TYPE}.'_NOTIFY_CLOSE_OK',
		SOURCE_REF		=> $params{SOURCE_REF},
		DATA			=> $params{DATA},
	};
	
	$p->{SCHEDULE_DATE} = $params{SCHEDULE_DATE} if defined $params{SCHEDULE_DATE};
	
	return $self->_get_ra_works_core_notify->insert(%{$p});
}

sub notify_work_close_ko  {
	my $self = shift;
	my %params = @_;

	$params{TYPE} = $params{TYPE} eq 'NETWORKFIBERCOP' ? 'NETWORK' :
			$params{TYPE} eq 'PREEMAINTENANCE01' ? 'PREEMAINT01' :
			$params{TYPE} eq 'EXTRMAINTENANCE01' ? 'EXTRMAINT01' : $params{TYPE};
	
	return $self->_get_ra_works_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_CLOSE_KO',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub notify_work_update  {
	my $self = shift;
	my %params = @_;

	$params{TYPE} = $params{TYPE} eq 'NETWORKFIBERCOP' ? 'NETWORK' :
			$params{TYPE} eq 'PREEMAINTENANCE01' ? 'PREEMAINT01' :
			$params{TYPE} eq 'EXTRMAINTENANCE01' ? 'EXTRMAINT01' : $params{TYPE};
	
	return $self->_get_ra_works_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_UPDATE',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub _get_ra_works_gw {
	my $self = shift;
	
	$self->{RA_WORKS_GW} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SOURCE_SERVICE			=> $self->{WPSOWORKS_GW_SOURCE_SERVICE}
		,SOURCE_CONTEXT			=> $self->{WPSOWORKS_GW_SOURCE_CONTEXT}
		,TARGET_SERVICE			=> $self->{WPSOWORKS_GW_TARGET_SERVICE}
		,TARGET_CONTEXT			=> $self->{WPSOWORKS_GW_TARGET_CONTEXT}
		,SESSION_DESCRIPTION	=> $self->{WPSOWORKS_GW_SESSION_DESCRIPTION}
	) unless defined $self->{RA_WORKS_GW};
	
	return $self->{RA_WORKS_GW};
}

sub _get_ra_wpworks_sinfo_update_rendicontazione {
	my $self = shift;
	
	$self->{RA_SINFO_UPDATE_RENDICONTAZIONE} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SOURCE_CONTEXT			=> $self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_SOURCE_CONTEXT}
		,TARGET_CONTEXT			=> $self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_TARGET_CONTEXT}
		,SESSION_DESCRIPTION	=> $self->{WPWORKS_SINFO_UPDATE_RENDICONTAZIONE_SESSION_DESCRIPTION}
	) unless defined $self->{RA_SINFO_UPDATE_RENDICONTAZIONE};
	
	return $self->{RA_SINFO_UPDATE_RENDICONTAZIONE};
}

sub _get_ra_works_core_notify {
	my $self = shift;
	
	$self->{RA_WORKS_CORE_NOTIFY} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SOURCE_SERVICE			=> $self->{WPSOWORKS_CORE_NOTIFY_SOURCE_SERVICE}
		,SOURCE_CONTEXT			=> $self->{WPSOWORKS_CORE_NOTIFY_SOURCE_CONTEXT}
		,TARGET_SERVICE			=> $self->{WPSOWORKS_CORE_NOTIFY_TARGET_SERVICE}
		,TARGET_CONTEXT			=> $self->{WPSOWORKS_CORE_NOTIFY_TARGET_CONTEXT}
		,SESSION_DESCRIPTION	=> $self->{WPSOWORKS_CORE_NOTIFY_SESSION_DESCRIPTION}
	) unless defined $self->{RA_WORKS_CORE_NOTIFY};
	
	return $self->{RA_WORKS_CORE_NOTIFY};
}

1;

