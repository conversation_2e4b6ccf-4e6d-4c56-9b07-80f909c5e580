package WPSOWORKS::MQ::Consumer::FiberConstructionSync;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOWORKS::Collection::System::NETWORK;
use WPSOWORKS::Collection::System::ROE;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'NEW_GROUP',
		'NOTIFY'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	
	$self->{CollNetwork} = WPSOWORKS::Collection::System::NETWORK->new(ART => $self->art());
	
	$self->{CollROE} = WPSOWORKS::Collection::System::ROE->new(ART => $self->art());	
	
	return $self;
}

sub _get_coll_network { shift->{CollNetwork} }

sub _get_coll_roe { shift->{CollROE} }

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	
	# normalizzo ad ARRAY
	$data->{"__wgGroupName__"} = ref $data->{"__wgGroupName__"} ? $data->{"__wgGroupName__"} : [$data->{"__wgGroupName__"}];
	if (exists $data->{"__serviceGroupName__"}){
		$data->{"__serviceGroupName__"} = ref $data->{"__serviceGroupName__"} ? $data->{"__serviceGroupName__"} : [$data->{"__serviceGroupName__"}];
	}
	my $coll;
	my $key;
	
	if ($event->get_event_name() eq 'NOTIFY'){
		my $type = $data->{__TYPE__};
		if ($type eq 'NETWORK'){
			$coll = $self->_get_coll_network();
			$key = "networkId";
		} elsif ($type eq 'ROE'){
			$coll = $self->_get_coll_roe();
			$key = "roeId"; 
		} else {
			$message = __x("Unknown param {param} {type}", param => '__TYPE__', type => $type);
			$self->logger()->error($message);
			return $self->consume( FETCH_RESULT => 1, STATUS => 1, TARGET_REF => 'KO', REASON => $message );
		}
		
		my $search_params = {
			"customerId"	=> $data->{"customerId"}
			,"contractId"	=> $data->{"contractId"}
			,$key			=> $event->get_source_ref()
		};
	
		# devo verificare se e' gia' presente
		my $objs = $coll->cerca(%{$search_params});
		unless ($objs){
			$message = __x("Unable to search system of type {type}: {error}", type => $type, error => $self->art()->last_error());
			$self->logger()->warn( $message );
			return $self->skip( REASON => $message );
		}
		
		# se ne trovo piu' di uno e' un bug
		if (scalar @{$objs} > 1){
			$message = __x("Found more than one system of type {type} for {key} {value}", type => $type, key => $key, value => $event->get_source_ref());
			$self->logger()->warn( $message );
			return $self->skip( REASON => $message );
		} elsif (scalar @{$objs} == 1){ # devo aggiornare
			my $obj = $objs->[0];
			
			my $updateParams = $data;
			
			unless ($obj->set_property(PROPERTIES => $updateParams)){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => $type, id => $obj->id(), error => $self->art()->last_error());
				$self->logger()->warn( $message );
				return $self->skip( REASON => $message );
			}
			
			my @admin_groups = ('ADMIN', 'ROOT');
			
			# recupero i gruppi
			my $system_groups = $obj->info('GROUPS');
			
			my @del_groups;
			for my $g (@{$system_groups}){
				push @del_groups, $g unless grep {$_ eq $g} @admin_groups;
			}
			
			# rimuovo tutti i gruppi
			unless($obj->delete_groups(@del_groups)){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => $type, id => $obj->id(), error => $self->art()->last_error());
				$self->logger()->warn( $message );
				return $self->skip( REASON => $message );
			}
			
			#
			my $groups_to_add = [
				$data->{"__projectGroupName__"}
			];
			
			push @{$groups_to_add}, @{$data->{"__wgGroupName__"}} if defined $data->{"__wgGroupName__"};
			push @{$groups_to_add}, @{$data->{"__serviceGroupName__"}} if defined $data->{"__serviceGroupName__"};
			
			# assegno tutti i gruppi
			unless($obj->set_groups(@{$groups_to_add})){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => $type, id => $obj->id(), error => $self->art()->last_error());
				$self->logger()->warn( $message );
				return $self->skip( REASON => $message );
			}
			
			$message = __x("System of type {type} with id {id} updated", type => $obj->info('SYSTEM_TYPE_NAME'), id => $obj->id());
			$self->logger()->info($message);
			return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $obj->id(), REASON => $message );
			
		} else { # devo creare 
			
			delete $data->{__TYPE__};
			
			my $createParams = $data;
			
			my $obj = $coll->crea(%{$createParams});
			unless ($obj){
				$message = __x("Unable to create system of type {type}: {error}", type => $type, error => $self->art()->last_error());
				$self->logger()->warn( $message );
				return $self->skip( REASON => $message );
			}
			
			$message = __x("System of type {type} created with id {id}", type => $obj->info('SYSTEM_TYPE_NAME'), id => $obj->id());
			$self->logger()->info($message);
			return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $obj->id(), REASON => $message );
		}
	} else {
		
		unless (defined $data->{"groups"}){
			$message = __x("Missing required data 'groups'");
			$self->logger()->warn( $message );
			return $self->skip( REASON => $message );
		}
		
		my $groups = eval{ from_json($data->{"groups"}) };
		if ($@){
			$message = __x("Bad data 'groups': ".$@);
			$self->logger()->warn( $message );
			return $self->skip( REASON => $message );
		}
		
		if (ref($groups) ne 'ARRAY'){
			$message = __x("Bad data 'groups': must be an ARRAY");
			$self->logger()->warn( $message );
			return $self->skip( REASON => $message );
		}
		
		for my $g (@{$groups}){
			
			for my $c ('name', 'description'){
				unless (defined $g->{$c}){
					$message = __x("Missing info group: {info}", info => $c);
					$self->logger()->warn( $message );
					return $self->skip( REASON => $message );
				}
			}
			
			my $createParams = {
				NAME => $g->{"name"}
				,DESCRIPTION => $g->{"description"}
				,IS_AUTOGENERATED => 1
			};
			
#			my $group = $self->art()->create_group(%{$createParams});
#		
#			unless ($group){
#				$message = __x("Unable to create group {groupName}: {error}", groupName => $g->{"name"}, error => $self->art()->last_error());
#				$self->logger()->warn( $message );
#				return $self->skip( REASON => $message );
#			}
#			
#			$message = __x("Group {groupName} created with id {id}", groupName => $g->{"name"}, id => $group);
			$message = __x("Creation of group {groupName} deferred to Service Management", groupName => $g->{name});
			$self->logger()->info($message);
		}
		return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	}
}

sub finish
{

}
1;

