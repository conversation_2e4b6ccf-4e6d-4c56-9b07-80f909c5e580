package WPSOWORKS::MQ::Consumer::ProjectCitiesGroupCreazione;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOWORKS::Collection::System::PROJECT;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'NEW',
		'NEW_GROUP'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollProject} = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	
	if ($event->get_event_name() eq 'NEW'){
		
		# devo verificare se il progetto e' gia' presente
		my $projects = $self->{CollProject}->cerca(
			"customerId"	=> $data->{"customerId"}
			,"contractId"	=> $data->{"contractId"}
			,"projectId"	=> $event->get_source_ref()
		);
		unless ($projects){
			$message = __x("Unable to search system of type {type}: {error}", type => 'PROJECT', error => $self->art()->last_error());
			$self->logger()->error( $message );
			return $self->skip( REASON => $message );
		}
		
		# se ne trovo piu' di uno e' un bug
		if (scalar @{$projects} > 1){
			$message = __x("Found more than one system of type {type} for {key} {value}", type => 'PROJECT', key => 'projectId', value => $event->get_source_ref());
			$self->logger()->error( $message );
			return $self->skip( REASON => $message );
		} elsif (scalar @{$projects} == 1){ # devo aggiornare
			my $project = $projects->[0];
			
			my $updateParams = {
				"projectId"	=> $event->get_source_ref() #settato per avere sempre almeno una chiave
			};
			
			$updateParams->{pfpId} = $data->{"pfpId"} if defined $data->{"pfpId"};
			$updateParams->{centralId} = $data->{"centralId"} if defined $data->{"centralId"};
			$updateParams->{ring} = $data->{"ring"} if defined $data->{"ring"};
			$updateParams->{pop} = $data->{"pop"} if defined $data->{"pop"};
			$updateParams->{city} = $data->{"city"} if defined $data->{"city"};
			$updateParams->{pfpName} = $data->{"pfpName"} if defined $data->{"pfpName"};
			$updateParams->{province} = $data->{"province"} if defined $data->{"province"};
			$updateParams->{country} = $data->{"country"} if defined $data->{"country"};
			$updateParams->{warnings} = (ref($data->{"warnings"}) eq 'ARRAY' ? $data->{"warnings"} : [$data->{"warnings"}]) if defined $data->{"warnings"};
			
			unless ($project->set_property(PROPERTIES => $updateParams)){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => 'PROJECT', id => $project->id(), error => $self->art()->last_error());
				$self->logger()->error( $message );
				return $self->skip( REASON => $message );
			}
			
			my @admin_groups = ('ADMIN', 'ROOT');
			
			# recupero i gruppi
			my $system_groups = $project->info('GROUPS');
			
			my @del_groups;
			for my $g (@{$system_groups}){
				push @del_groups, $g unless grep {$_ eq $g} @admin_groups;
			}
			
			# rimuovo tutti i gruppi
			unless($project->delete_groups(@del_groups)){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => 'PROJECT', id => $project->id(), error => $self->art()->last_error());
				$self->logger()->error( $message );
				return $self->skip( REASON => $message );
			}
			
			# assegno tutti i gruppi
			unless($project->set_groups($data->{"projectGroupName"}, $data->{"project4RPBGroupName"}, $data->{"cityGroupName"})){
				$message = __x("Unable to update system of type {type} with id {id}: {error}", type => 'PROJECT', id => $project->id(), error => $self->art()->last_error());
				$self->logger()->error( $message );
				return $self->skip( REASON => $message );
			}
			
			$message = __x("System of type {type} with id {id} updated", type => $project->info('SYSTEM_TYPE_NAME'), id => $project->id());
			$self->logger()->info($message);
			return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $project->id(), REASON => $message );
			
		} else { # devo creare 
		
			my $createParams = {
				customerId						=> $data->{"customerId"}
				,contractId						=> $data->{"contractId"}
				,projectId						=> $event->get_source_ref()
				,cadastralCode					=> $data->{"cadastralCode"}
				,projectGroupName				=> $data->{"projectGroupName"}
				,projectGroupDescription		=> $data->{"projectGroupDescription"}
				,project4RPBGroupName			=> $data->{"project4RPBGroupName"}
				,project4RPBGroupDescription	=> $data->{"project4RPBGroupDescription"}
				,cityGroupName					=> $data->{"cityGroupName"}
				,cityId							=> $data->{"cityId"}
			};
			
			$createParams->{pfpId} = $data->{"pfpId"} if defined $data->{"pfpId"};
			$createParams->{centralId} = $data->{"centralId"} if defined $data->{"centralId"};
			$createParams->{ring} = $data->{"ring"} if defined $data->{"ring"};
			$createParams->{pop} = $data->{"pop"} if defined $data->{"pop"};
			$createParams->{city} = $data->{"city"} if defined $data->{"city"};
			$createParams->{pfpName} = $data->{"pfpName"} if defined $data->{"pfpName"};
			$createParams->{province} = $data->{"province"} if defined $data->{"province"};
			$createParams->{country} = $data->{"country"} if defined $data->{"country"};
			$createParams->{warnings} = (ref($data->{"warnings"}) eq 'ARRAY' ? $data->{"warnings"} : [$data->{"warnings"}]) if defined $data->{"warnings"};
			
			my $project = $self->{CollProject}->crea(%{$createParams});
			unless ($project){
				$message = __x("Unable to create system of type {type}: {error}", type => 'PROJECT', error => $self->art()->last_error());
				$self->logger()->error( $message );
				return $self->skip( REASON => $message );
			}
			
			$message = __x("System of type {type} created with id {id}", type => $project->info('SYSTEM_TYPE_NAME'), id => $project->id());
			$self->logger()->info($message);
			return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $project->id(), REASON => $message );
		}
	} else {
		
		unless (defined $data->{"groups"}){
			$message = __x("Missing required data 'groups'");
			$self->logger()->error( $message );
			return $self->skip( REASON => $message );
		}
		
		my $groups = eval{ from_json($data->{"groups"}) };
		if ($@){
			$message = __x("Bad data 'groups': ".$@);
			$self->logger()->error( $message );
			return $self->skip( REASON => $message );
		}
		
		if (ref($groups) ne 'ARRAY'){
			$message = __x("Bad data 'groups': must be an ARRAY");
			$self->logger()->error( $message );
			return $self->skip( REASON => $message );
		}
		
		for my $g (@{$groups}){
			
			for my $c ('name', 'description'){
				unless (defined $g->{$c}){
					$message = __x("Missing info group: {info}", info => $c);
					$self->logger()->error( $message );
					return $self->skip( REASON => $message );
				}
			}
			
			my $createParams = {
				NAME => $g->{"name"}
				,DESCRIPTION => $g->{"description"}
				,IS_AUTOGENERATED => 1
			};
			
			my $group = $self->art()->create_group(%{$createParams});
		
			unless ($group){
				$message = __x("Unable to create group {groupName}: {error}", groupName => $g->{"name"}, error => $self->art()->last_error());
				$self->logger()->error( $message );
				return $self->skip( REASON => $message );
			}
			
			$message = __x("Group {groupName} created with id {id}", groupName => $g->{"name"}, id => $group);
			$self->logger()->info($message);
		}
		return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	}
}

sub finish
{

}
1;

