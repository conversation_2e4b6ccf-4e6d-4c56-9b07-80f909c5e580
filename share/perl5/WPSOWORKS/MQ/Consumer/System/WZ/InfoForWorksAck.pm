package WPSOWORKS::MQ::Consumer::System::WZ::InfoForWorksAck;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOWORKS;
use WPSOWORKS::System::WZ;

use base 'SIRTI::Queue::EventAckConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'GET_INFO'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event_ack()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT_ACK};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __("Working RA_ID ack...") );
	$self->logger()->info( __x("{type}: {value}", type => 'TYPE', value => ( $event->is_global() ? __x("GLOBAL") : __x("TARGET ACK") )) );
	
	if ( $event->is_global() )
	{
		return $self->consume( NOTE => __('Unnecessary'));
	}
	
	
	#$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_event()->get_source_ref() ) );
	
	my $wz = WPSOWORKS::System::WZ->new(ART => $art, ID => $event->get_event()->get_source_ref());

	unless (defined $wz){
		$message = __x("Unable to retrieve system: {error}", error => $self->art()->last_error());
		$self->logger()->error( $message );
		return $self->skip();
	}
	
	if ($event->get_event()->get_status()){
		$message = __x("ACK KO: {error}", error => $event->get_event()->get_reason());
		$self->logger()->error( $message );
		return $self->skip();
	}
	
	$art->_dbh()->do( "savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
	
	my $results = decode_json($event->get_ack_data()->{results});
	
	#ci attendiamo un unico risultato
	if (scalar @{$results} == 0){
		$art->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		$message = __x("ACK KO: {error}", error => __("cityId not found"));
		$self->logger()->error( $message );
		return $self->skip();	
	} elsif (scalar @{$results} > 1){
		$art->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		$message = __x("ACK KO: {error}", error => __("Found more than one cityId"));
		$self->logger()->error( $message );
		return $self->skip();	
	}
	
	unless($wz->set_property(PROPERTIES => {cityId => $results->[0]})){
		$art->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		$message = __x("ACK KO: {error}", error => __("Unable to set property cityId"));
		$self->logger()->error( $message );
		return $self->skip();
	}
	
	#una volta recuperato il cityId se non esiste il gruppo CITY_cityId lo creo altrimenti utilizzo quello che c'è
	my $cityGroupName = 'CITY_'.$results->[0];
	unless ($art->test_group_name($cityGroupName)){
		my $cityGroup = $art->create_group(
			NAME => $cityGroupName,
			DESCRIPTION => $cityGroupName,
			IS_AUTOGENERATED => 1
		);
	
		unless (defined $cityGroup){
			$art->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
			$message = __x("ACK KO: {error}", error => __x("Unable to create city group: {error}", error => $art->last_error()));
			$self->logger()->error( $message );
			return $self->skip();
		}	
	}
	
	if ($wz->set_groups($cityGroupName)){
		$message = __x("WorkZone {workZoneId} with id {id} updated!", workZoneId => $wz->property()->{workZoneId}, id => $wz->id());
		$self->logger()->info($message);
		return $self->consume( FETCH_RESULT => 1 );
	} else {
		$art->_dbh()->do( "rollback to savepoint WPSOWORKS_Cll_Sys_WZ_crea" );
		$message = __x("ACK KO: Unable to add group {group} to system {id}: {error}", group => $cityGroupName, id => wz->id(), error => $art->last_error());
		$self->logger()->error( $message );
		return $self->skip();
	}
}

sub finish
{

}
1;

