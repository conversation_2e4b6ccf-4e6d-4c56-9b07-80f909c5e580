package WPSOWORKS::MQ::Consumer::Activity::LAVORO::GWRichiestaAck;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use API::ART::APP::Activity::LAVORO;

use base 'SIRTI::Queue::EventAckConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		"NEW",
		"CANCEL"
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	return $self;	
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event_ack()
{
	my $self = shift;
	my %params = @_;
	my $event	= $params{EVENT_ACK};
	my $art		= $self->art();
	
	$self->logger()->info( __("Working RA_ID ack...") );
    $self->logger()->info( __x("{type}: {value}", type => 'TYPE', value => ( $event->is_global() ? __x("GLOBAL") : __x("TARGET ACK") )) );
    
	if ( $event->is_global() ) 
	{
		return $self->consume( NOTE => __('Unnecessary'));
	}
	
	my $activity_id = $event->get_event()->get_source_ref();
	
	my $activity = API::ART::APP::Activity::LAVORO->new( ART => $art , ID => $activity_id);
	
	unless ($activity){
		my $message = __x("Error retrieving activity {id}: {error}", id => $activity_id, error => $art->last_error());
		$self->logger()->error($message);
		return $self->skip();
	}
	
	my $ret;
	
	if ($event->get_event()->get_status()){
		
		if ($event->get_event()->get_event_name() eq 'NEW'){
			$ret = $activity->step(
				ACTION => 'ANOMALIA'
				,PROPERTIES => {
					"planningServiceResult"			=>	$event->get_ack_data()->{"planningServiceResult"},
					"planningServiceCode"			=>	$event->get_ack_data()->{"planningServiceCode"},
					"planningServiceDescription"	=>	$event->get_ack_data()->{"planningServiceDescription"}
				}
			);
		} else { # 'CANCEL'
			$ret = $activity->step(
				ACTION => 'RICHIESTA_ANNULLAMENTO_KO'
				,PROPERTIES => {
					"planningServiceResult"			=>	$event->get_ack_data()->{"planningServiceResult"},
					"planningServiceCode"			=>	$event->get_ack_data()->{"planningServiceCode"},
					"planningServiceDescription"	=>	$event->get_ack_data()->{"planningServiceDescription"}
				}
			);
		}
	} else {
		if ($event->get_event()->get_event_name() eq 'NEW'){
			$ret = $activity->step(
				ACTION => 'CONFERMA'
				,PROPERTIES => {
					"planningServiceResult"			=>	$event->get_ack_data()->{"planningServiceResult"},
					"planningServiceDescription"	=>	$event->get_ack_data()->{"planningServiceDescription"},
					"planningServiceId"				=>	$event->get_ack_data()->{"planningServiceId"},
					"planningServiceCode"			=>	$event->get_ack_data()->{"planningServiceCode"},
				}
			);
		} else { # 'CANCEL'
			$ret = $activity->step(
				ACTION => 'RICHIESTA_ANNULLAMENTO_OK'
			);
		}
	}
	
	unless ($ret){
		my $message = __x("Error working activity {id}: {error}", id => $activity->id(), error => $self->art()->last_error());
		$self->logger()->error($message);
		return $self->skip();
	}
	
	my $message = __x("Successfully worked activity {id}: current status {status}", id => $activity->id(), status => $activity->get_current_status_name());
	$self->logger()->info($message);
	return $self->consume( NOTE => $message);
}

sub finish
{

}
1;
