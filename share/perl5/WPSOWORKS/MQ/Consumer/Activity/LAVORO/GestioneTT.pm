package WPSOWORKS::MQ::Consumer::Activity::LAVORO::GestioneTT;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOWORKS::Collection::System::PROJECT;
use API::ART::APP::Activity::AS_BUILT;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'CLOSE'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollProjects} = WPSOWORKS::Collection::System::PROJECT->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	for my $p ('customerId', 'contractId', 'projectId', 'asBuiltId', 'result') {
		unless(defined $data->{$p}) {
			$message = __x("Missing mandatory parameter {param}", param => $p);
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		}
	}
	
	if($data->{result} !~/^(OK|KO)$/) {
		$message = __x("Param {param} must be {values}", param => 'result', values => 'OK|KO');
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	
	# verifico che il progetta esista
	my $projects = $self->{CollProjects}->cerca(
		customerId	=>	$data->{"customerId"},
		contractId	=>	$data->{"contractId"},
		projectId	=>	$data->{"projectId"},
	);
	
	unless (defined $projects){
		$self->logger()->error($art->last_error());
		return $self->skip( REASON => $art->last_error() );
	}
	
	if (scalar @{$projects} != 1){
		$message = __x("Found {n} projects for projectId {projectId}", n => scalar @{$projects}, projectId => $data->{"projectId"});
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	
	
	
	#istanzio l'oggetto as_built
	my $act = eval{API::ART::APP::Activity::AS_BUILT->new(ART => $art, ID => $data->{"asBuiltId"})};
	if ($@ || ! defined $act){
		$message = $art->last_error()||$@;
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	
	#recupero le attività figlie: se non ci sono o sono chiuse è un'anomalia
	my $children = $act->get_children(ACTIVITY_TYPE_NAME => ['LAVORO']);
	unless (defined $children){
		$message = $art->last_error();
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	
	for my $ch (@{$children}){
		if ($ch->is_closed()){
			$art->last_error(__x("Activity {id} already closed",id => $ch->id()));
			$message = $art->last_error();
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		}
		# verifico che le attività figlie appartengano tutte al progetto
		if ($ch->system_property('projectId') ne $data->{projectId}){
			$message = __x("WorkId {workId} not associated to project {projectId}", workId => $ch->id(), projectId => $data->{projectId});
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		}
	}
	
	$art->_dbh()->do( "savepoint WPSOWORKS_MQ_Cons_LAV_TT" );
	
	my $step;
	if ($data->{result} eq 'OK'){
		$step = $act->step(
			ACTION => 'CHIUSURA_TT_OK'
		)
	} else { # $data->{result} eq 'KO'
		$step = $act->step(
			ACTION => 'CHIUSURA_TT_KO',
			PROPERTIES => {
				ttKOreason => $data->{ttKOreason}
			}
		)
	}

	unless (defined $step){
		$art->_dbh()->do( "rollback to savepoint WPSOWORKS_MQ_Cons_LAV_TT" );
		$message = $art->last_error();
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	$message = __x("Successfully worked activity {id}: current status {status}", id => $act->id(), status => $act->get_current_status_name());
	$self->logger()->info($message);
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	
}

sub finish
{

}
1;

