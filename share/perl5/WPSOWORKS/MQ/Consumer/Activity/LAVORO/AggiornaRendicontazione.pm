package WPSOWORKS::MQ::Consumer::Activity::LAVORO::AggiornaRendicontazione;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use WPSOWORKS;
use API::ART::APP::Activity::LAVORO;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'UPDATE_ACCOUNTING'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	# la verifica dell'attività è solo per consistenza anche perchè le activity_property potrebbero essere cambiate
	my $activity = API::ART::APP::Activity::LAVORO->new(ART => $art, ID => $event->get_source_ref());

	unless (defined $activity){
		$message = __x("Unable to retrieve activity: {error}", error => $self->art()->last_error());
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	if ($activity->info('SYSTEM_TYPE_NAME') ne $event->get_data()->{type}){
		$message = __x("Mismatch bewteen type {type} and id {id}", type => $event->get_data()->{type}, id => $activity->id());
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	my $type;
	my $value;
	my $resource;
	my $body;
	my $is_fatal;
	if ($event->get_data()->{type} eq 'CAVO'){
		$type = 'cableId';
		$value = $event->get_data()->{cableId};
		$resource = sprintf($ENV{SINFO_CAVO_RESOURCE}, $event->get_data()->{'customerId'}, $event->get_data()->{'contractId'}, $event->get_data()->{'projectId'}, $event->get_data()->{'cableId'});
		$body = {
			fromNetworkElementId => $event->get_data()->{'fromNetworkElementId'}
			, toNetworkElementId => $event->get_data()->{'toNetworkElementId'}
			, workId => $event->get_data()->{'workId'}
		};
		for ('doneUndergroundLength','doneHandmaidLength','doneAerialLength','doneFacadeLength','toStockLength','fromStockLength'){
			$body->{$_} = $event->get_data()->{$_} if defined $event->get_data()->{$_}; 
		}
	}
	
	my $sinfo = $self->{WPSOWORKS}->invoke_sinfo_rest(
		METHOD => 'PUT'
		, RESOURCE => $resource
		, BODY => $body
		, IS_FATAL => \$is_fatal
	);
	
	if ($is_fatal){
		$message = $art->last_error();
		$self->logger()->fatal( $message );
		die;
	}	
	unless (defined $sinfo){
		$message = __x("Error calling WS Sinfo: {error}", error => $self->art()->last_error());
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	$message = __x("Update accounting for id {id} and {type} {value} done!", id => $activity->id(), type => $type, value => $value);
	$self->logger()->info($message);
	return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $activity->id(), REASON => $message );
}

sub finish
{

}
1;

