package WPSOWORKS::MQ::Consumer::Activity::LAVORO::GWRichiestaAvanzamento;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use API::ART::APP::Activity::LAVORO;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'RESPONSE'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	
	my $activity_id = $data->{activityId};
	
	my $activity = API::ART::APP::Activity::LAVORO->new( ART => $art , ID => $activity_id);
	
	unless ($activity){
		my $message = __x("Error retrieving activity {id}: {error}", id => $activity_id, error => $art->last_error());
		$self->logger()->error( $message );
		return $self->skip( REASON => $message );
	}
	
	# se l'attività non è in ANNULLATO provo lo step altrimenti eseguo il fetch ko
	if ($activity->get_current_status_name() ne 'ANNULLATO'){
		my $ret = $activity->step(
			ACTION => 'AVANZAMENTO'
			,PROPERTIES => $data
		);
		
		unless ($ret){
			my $message = __x("Error working activity {id}: {error}", id => $activity->id(), error => $self->art()->last_error());
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		}
		
		$message = __x("Successfully worked activity {id}: current status {status}", id => $activity->id(), status => $activity->get_current_status_name());
		$self->logger()->info($message);
		
		return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	} else {
		$message = __x("Activity {id} in status {status}: unable to process OUTBOUND messages", id => $activity->id(), status => $activity->get_current_status_name());
		$self->logger()->info($message);
		
		return $self->consume( FETCH_RESULT => 1, STATUS => 1, TARGET_REF => 'KO', REASON => $message );
	}
}

sub finish
{

}
1;

