package API::ART::APP::Activity::AS_BUILT;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

sub check_subContract_work_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"subContractStartWorkDate"	=> { isa => 'SCALAR' }
					,"subContractEndWorkDate"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# effettuo check date
	if ($self->art()->get_date_from_iso_date($params{subContractStartWorkDate}) > $self->art()->get_date_from_iso_date($params{subContractEndWorkDate})){
		$self->art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'subContractEndWorkDate', date2 => 'subContractStartWorkDate'));
		return undef;
	}
	
	return 1;
	
}

1;
