package API::ART::APP::Activity::LAVORO;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOWORKS;
use WPSOWORKS::MQ::Sender::Activity::LAVORO;
use WPSOWORKS::Collection::System::SUBAPPALTO;
use WPSOWORKS::Collection::System::SQUADRA;
use WPSOWORKS::Collection::Activity::LAVORO;
use WPSOWORKS::Sync::MACROTASKS;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_SYSTEM_CLASS = {
	CANTIERE => 'WPSOWORKS::System::CANTIERE',
	CAVO => 'WPSOWORKS::System::CAVO',
	NODO => 'WPSOWORKS::System::NODO',
	LAVORO => 'WPSOWORKS::System::LAVORO',
};

sub _wpsoworks {
	my $self = shift;
	
	return $self->{WPSOWORKS} if defined $self->{WPSOWORKS};
	
	$self->{WPSOWORKS} = WPSOWORKS->new(ART => $self->art());
	return $self->{WPSOWORKS};
}

sub get_db { shift->art()->_dbh() }

sub get_team_info {
	my $self = shift;
	my %params = @_;

	return $self->_wpsoworks->get_team_info(%params);
}

sub get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOWORKS::MQ::Sender::Activity::LAVORO->instance(DB => $self->get_db());
	return $self->{SENDER};
}

sub get_coll_subappalto {
	my $self = shift;
	
	return $self->{collSubappalto} if defined $self->{collSubappalto};
	
	$self->{collSubappalto} = WPSOWORKS::Collection::System::SUBAPPALTO->new(ART => $self->art());
	return $self->{collSubappalto};
}

sub get_coll_squadra {
	my $self = shift;
	
	return $self->{collSquadra} if defined $self->{collSquadra};
	
	$self->{collSquadra} = WPSOWORKS::Collection::System::SQUADRA->new(ART => $self->art());
	return $self->{collSquadra};
}

sub get_coll_lavoro {
	my $self = shift;
	
	return $self->{collLavoro} if defined $self->{collLavoro};
	
	$self->{collLavoro} = WPSOWORKS::Collection::Activity::LAVORO->new(ART => $self->art());
	return $self->{collLavoro};
}

sub check_sla_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"slaStart"	=> { isa => 'SCALAR' }
					,"slaEnd"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# effettuo check date
	if ($self->art()->get_date_from_iso_date($params{slaStart}) > $self->art()->get_date_from_iso_date($params{slaEnd})){
		$self->art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'slaEnd', date2 => 'slaStart'));
		return undef;
	}
	
	return 1;
	
}

sub check_work_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"startWorkDate"	=> { isa => 'SCALAR' }
					,"endWorkDate"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# effettuo check date
	if ($self->art()->get_date_from_iso_date($params{startWorkDate}) > $self->art()->get_date_from_iso_date($params{endWorkDate})){
		$self->art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'endWorkDate', date2 => 'startWorkDate'));
		return undef;
	}
	
	return 1;
	
}

sub check_subContract_work_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"subContractStartWorkDate"	=> { isa => 'SCALAR' }
					,"subContractEndWorkDate"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# effettuo check date
	if ($self->art()->get_date_from_iso_date($params{subContractStartWorkDate}) > $self->art()->get_date_from_iso_date($params{subContractEndWorkDate})){
		$self->art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'subContractEndWorkDate', date2 => 'subContractStartWorkDate'));
		return undef;
	}
	
	return 1;
	
}

sub check_planned_dates {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"startPlannedDate"	=> { isa => 'SCALAR' }
					,"endPlannedDate"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# effettuo check date
	if ($self->art()->get_date_from_iso_date($params{startPlannedDate}) > $self->art()->get_date_from_iso_date($params{endPlannedDate})){
		$self->art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'endPlannedDate', date2 => 'startPlannedDate'));
		return undef;
	}
	
	return 1;
	
}

sub update{
	my $self = shift;
	my %params = @_;
	
	# per ora l'update è possibile solo per le attivita legati ad un sistema di tipo CANTIERE
	if ($self->system()->info('SYSTEM_TYPE_NAME') eq 'CANTIERE') {
		# solo in certi stati e' possibile aggiornare l'attivita
		$self->art()->last_error(__x("Activities in status {status} can not be updated", status => $self->get_current_status_name()))
			&& return undef
				if ($self->get_current_status_name() !~/^(IN_LAVORAZIONE_SUBAPPALTO)$/);
		
		return $self->step(
			ACTION => 'MODIFICA',
			DESCRIPTION => 'Modifica composizione cantiere',
			PROPERTIES => \%params
		);
		
	} else {
		$self->art()->last_error(__x("Only activities with system type {type} can be updated", type => 'CANTIERE'));
		return undef;
	}
	
	
	return 1;
}

sub get_last_action{
	my $self = shift;

	my $history = $self->history();
	return undef unless $history;

	# recupero l'ultimo step che ha effettuato questa azione
	my $transition = $history->last();
	return undef unless (defined $transition);

	return $transition->action_name();
}

sub get_macro_tasks_sync{
	my $self = shift;
	
	$self->art()->last_error(__("Only activity of type construction site can call macro tasks"))
		&& return undef
			if $self->system()->info('SYSTEM_TYPE_NAME') ne 'CANTIERE';
	
	return $self->{MacroTasksSync} if defined $self->{MacroTasksSync};
	
	my $sp = $self->system_property();
	
	$self->{MacroTasksSync} = WPSOWORKS::Sync::MACROTASKS->new(
		 WPSOWORKS => $self->_wpsoworks()
		,CUSTOMER_ID => $sp->{customerId}
		,CONTRACT_ID => $sp->{contractId}
	);
	
	return $self->{MacroTasksSync};
}

sub get_macro_tasks_summary {
	my $self = shift;
	
	$self->art()->last_error(__("Only activity of type construction site can call macro tasks"))
		&& return undef
			if $self->system()->info('SYSTEM_TYPE_NAME') ne 'CANTIERE';
	
	my $sp = $self->system_property();
	
	my $query = '
		with acts as (
			select 
			  dts5.valore "category"
			  , dts6.valore "subCategory"
			  , replace(dts7.valore,\'.\',\',\') "toAssignQuantity"
			  , dts8.valore "unitOfMeasure"
			  , dts9.valore "estimatedDuration"
			  , vml."toDoQuantity"
			from v_attivita vatt
			  join v_sistemi s on s.id = vatt.id_sistema
			  join v_dt_sistemi dts on dts.id_sistema = s.id
			  join v_dt_sistemi dts2 on dts2.id_sistema = s.id
			  join sistemi_relazione sr on sr.a = s.id and sr.tipo = \'0\'
			  join v_sistemi s2 on s2.id = sr.b
			  join v_dt_sistemi dts3 on dts3.id_sistema = s2.id
			  join v_dt_sistemi dts4 on dts4.id_sistema = s2.id
			  join v_dt_sistemi dts5 on dts5.id_sistema = s2.id
			  join v_dt_sistemi dts6 on dts6.id_sistema = s2.id
			  join v_dt_sistemi dts7 on dts7.id_sistema = s2.id
			  join v_dt_sistemi dts8 on dts8.id_sistema = s2.id
			  join v_dt_sistemi dts9 on dts9.id_sistema = s2.id
			  join v_macrotasks_lavori vml on
			    vml."customerId" = dts.valore
			    and vml."contractId" = dts2.valore
			    and vml."projectId" = dts3.valore
			    and vml."permitsAreaId" = dts4.valore
			    and vml."category" = dts5.valore
			    and vml."subCategory" = dts6.valore
			where vatt.nome_tipo_Attivita = \'LAVORO\' 
			  and s.tipo = \'CANTIERE\'
			  and dts.nome = \'customerId\'
			  and dts2.nome = \'contractId\'
			  and s2.tipo = \'MACROLAVORO\'
			  and dts3.nome = \'projectId\'
			  and dts4.nome = \'permitsAreaId\'
			  and dts5.nome = \'category\'
			  and dts6.nome = \'subCategory\'
			  and dts7.nome = \'toAssignQuantity\'
			  and dts8.nome = \'unitOfMeasure\'
			  and dts9.nome = \'estimatedDuration\'
			  and dts.valore = '.$self->get_db()->quote($sp->{"customerId"}).'
			  and dts2.valore = '.$self->get_db()->quote($sp->{"contractId"}).'
			  and vatt.id = '.$self->id().'
		)
		select 
		  "category"
		  ,"subCategory"
		  ,"toDoQuantity"
		  ,to_number("toAssignQuantity") "toAssignQuantity"
		  ,"unitOfMeasure"
		  ,cast ("estimatedDuration" as FLOAT(126)) "estimatedDuration"
		From acts
		union all
		select mt."category"
		  ,mt."subCategory"
		  ,mt."toDoQuantity"
		  ,null "toAssignQuantity"
		  ,mt."unitOfMeasure"
		  ,mt."estimatedDuration"
		From v_macrotasks_lavori mt
		where mt."customerId" = '.$self->get_db()->quote($sp->{"customerId"}).'
			and mt."contractId" = '.$self->get_db()->quote($sp->{"contractId"}).'
		  and mt."projectId" = '.$sp->{"projectId"}.'
		  and mt."permitsAreaId" = '.$sp->{"permitsAreaId"}.'
		  and (mt."category", mt."subCategory") not in (
		    select acts."category", acts."subCategory" from acts
		  )
		  and mt."toDoQuantity" != 0 -- questa condizione serve per non far uscire le categorie per cui non si deve fare piu\' nulla
		order by "category",
		  "subCategory"
	';
	
	my $columns = [
		{
		  NAME => "category",
		  HEADER => __("Category"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "subCategory",
		  HEADER => __("Subcategory"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "toDoQuantity",
		  HEADER => __("To do quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "toAssignQuantity",
		  HEADER => __("To assign quantity"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number"
		},
		{
		  NAME => "unitOfMeasure",
		  HEADER => __("UoM"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "string"
		},
		{
		  NAME => "estimatedDuration",
		  HEADER => __("Estimated duration"),
		  JSON_CHECKBOX_FILTER => 0,
		  TYPE => "number",
		}
	];
	
	return $self->_wpsoworks()->report(JSON_RETURN_STRING => 0, QUERY => $query, COLUMNS => $columns);
}

sub update_accounting {
	#aggiorno SiNFO relativamente alla rendicontazione
	my $self = shift;
	
	my $data = {
		type => $self->info('SYSTEM_TYPE_NAME')
	};
	
	if ($data->{type} eq 'CAVO'){
		my $sp = $self->system_property();
		$data->{customerId} = $sp->{customerId};
		$data->{contractId} = $sp->{contractId};
		$data->{projectId} = $sp->{projectId};
		$data->{cableId} = $sp->{cableId};
		$data->{workId} = $self->id();
		$data->{fromNetworkElementId} = $sp->{fromNetworkElementId};
		$data->{toNetworkElementId} = $sp->{toNetworkElementId};
		$data->{doneUndergroundLength} = $sp->{doneUndergroundLengthV};
		$data->{doneHandmaidLength} = $sp->{doneHandmaidLengthV};
		$data->{doneAerialLength} = $sp->{doneAerialLengthV};
		$data->{doneFacadeLength} = $sp->{doneFacadeLengthV};
		$data->{fromStockLength} = $sp->{doneFromStockLengthV};
		$data->{toStockLength} = $sp->{doneToStockLengthV};
	}
	
	return $self->get_sender()->update_accounting(
		SOURCE_REF => $self->id()
		, DATA => $data
	);
}

sub can_do_automatic_accounting {
	my $self = shift;
	my %params = @_;
	
	# 1. verifico che il tipo sistema possa fare la rendicontazione
	my $system_types_enabled = [
		'CAVO'
	];
	
	my $st = $self->info('SYSTEM_TYPE_NAME');
	unless (grep {$_ eq $st} @{$system_types_enabled}){
		$self->art()->last_error(__x("Automatic accounting not enabled for system type {type}", type => $st));
		return undef;
	}
	
	# 2. verifico che l'attività sia nello stato corretto
	my $status_enabled = [
		'IN_LAVORAZIONE_SIRTI'
	];
	
	my $stato = $self->get_current_status_name();
	unless (grep {$_ eq $stato} @{$status_enabled}){
		$self->art()->last_error(__x("Automatic accounting not enabled for activity in status {status}", status => $stato));
		return undef;
	}
	
	my $ap = $self->activity_property();
	
	# 3. se e' presente il dta accountingDone vuol dire che la rendicontazione automatica e' gia' stata fatta
	if ($ap->{'accountingDone'}){
		$self->art()->last_error(__("Automatic accounting already done!"));
		return undef;
	}
	
	# 4. verifico i parametri obbligatori
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"planningServiceId"	=> { isa => 'SCALAR' }
					,"accountingUser"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $remap = {
		planningServiceId => 'planningServiceId'
		,accountingUser => 'teamName'
	};
    
	for ("planningServiceId", "accountingUser"){
		# NB: il next si tratta di un'anomalia  ma lo accettiamo per evitare blocchi
		next unless defined $ap->{$remap->{$_}};
		$self->art()->last_error(__x("Param {param} with value {value} not related to activity {id}", param => $_, value => $params{$_}, id => $self->id()))
			&& return undef
				if $ap->{$remap->{$_}} ne $params{$_};
	}
	
	return 1;
}

sub can_do_accounting_validation {
	my $self = shift;
	my %params = @_;
	
	# 1. verifico che il tipo sistema possa fare la rendicontazione
	my $system_types_enabled = [
		'CAVO'
	];
	
	my $st = $self->info('SYSTEM_TYPE_NAME');
	unless (grep {$_ eq $st} @{$system_types_enabled}){
		$self->art()->last_error(__x("Accounting validation not enabled for system type {type}", type => $st));
		return undef;
	}
	
	# 2. verifico che l'attività sia nello stato corretto
	my $status_enabled = [
		'ATTESA_RENDICONTAZIONE'
		,'IN_LAVORAZIONE_SUBAPPALTO'
	];
	
	my $stato = $self->get_current_status_name();
	unless (grep {$_ eq $stato} @{$status_enabled}){
		$self->art()->last_error(__x("Accounting validation not enabled for activity in status {status}", type => $stato));
		return undef;
	}
	
	return 1;
}

sub get_data_for_notify_works{
	my $self = shift;
	my $system_property = $self->system_property();
	my $property = $self->activity_property();

	my $data = {
		SOURCE_REF => $self->id()
		, TYPE => uc($system_property->{workType})
		, DATA => {
			ACTIVITY_ID				=> $self->id(),
			ACTIVITY				=> to_json($self->dump()),
			STATUS					=> $self->get_current_status_name(),
			customerId				=> $property->{customerId},
			contractId				=> $property->{contractId},
			updateDatabase			=> $system_property->{updateDatabase},
			test					=> $system_property->{test},
			junction				=> $system_property->{junction},
			civil					=> $system_property->{civil},
			cableLaying				=> $system_property->{cableLaying},
			survey					=> $system_property->{survey},
			updateDatabaseF1		=> $system_property->{updateDatabaseF1},
			updateDatabaseF2		=> $system_property->{updateDatabaseF2},
			opticalConnectionOSU	=> $system_property->{opticalConnectionOSU},
			opticalConnectionOLT	=> $system_property->{opticalConnectionOLT},
			restoration				=> $system_property->{restoration},
			planning				=> $system_property->{planning},
			design					=> $system_property->{design},
			patchCord				=> $system_property->{patchCord},
			infrastructure			=> $system_property->{infrastructure},
			laying					=> $system_property->{laying},
			testApp					=> $system_property->{testApp},
			testOTDR				=> $system_property->{testOTDR},
			generic					=> $system_property->{generic},
		}
	};
	
	$data->{DATA}->{projectId} = $property->{projectId} if defined $property->{projectId};
	$data->{DATA}->{assetId} = $system_property->{assetId} if defined $system_property->{assetId};
	$data->{DATA}->{maintenanceId} = $system_property->{maintenanceId} if defined $system_property->{maintenanceId};
	
	# aggiungo il codice del subappalto se presente per gestire correttamente i permessi su network
	$data->{DATA}->{SUBCONTRACT_CODE} = $property->{subContractCode} if defined $property->{subContractCode};

	return $data;
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub step(){
	my $self = shift;
	my %params = @_;
	
	# per ora i meta-allegati li mettiamo solo se tipo_sistema LAVORO
	if (exists $params{ATTACHMENTS} && $self->info('SYSTEM_TYPE_NAME') eq 'LAVORO'){
		my $sys_props = $self->system_property('projectId', 'assetId','maintenanceId','workType');
		my $meta = {
			activityId => $self->id(),
			activityType => $self->info('ACTIVITY_TYPE_NAME'),
		};
		$meta->{maintenanceId} = $sys_props->{"maintenanceId"} if (defined $sys_props->{'workType'} && $sys_props->{'workType'} =~ /^(PreeMaintenance|CorrMaintenance|ExtrMaintenance|PreeMaintenance01|ExtrMaintenance01)$/);
		$meta->{networkId} = $sys_props->{'projectId'} if defined $sys_props->{'projectId'};
		if (defined $sys_props->{'workType'} && $sys_props->{'workType'} =~ /^(PTE|NetworkFibercop)$/){
			$meta->{cabinetId} = $self->activity_property('ref00');
			if ($sys_props->{'workType'} =~ /^(PTE)$/){
				$meta->{pteId} = $self->activity_property('ref01');
			}
		}
		for my $asset (@{$sys_props->{'assetId'}}){
			$meta->{"assetId-".$asset} = 1;
		}
		
		$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
			&& return undef 
				if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
		for my $att (@{$params{ATTACHMENTS}}){
			if (!ref ($att)){
				# lo trasformo in una struttura per gestire i meta
				$att = {
					FILENAME => $att,
					META => $meta
				};
			} elsif (ref($att) eq 'HASH'){
				# aggiungo la chiave META se non esiste
				if (!exists $att->{META}){
					$att->{META} = $meta
				} else {
					# aggiungo le chiavi sovrascrivendole se già presenti
					for my $k (keys %{$meta}){
						$att->{META}->{$k} = $meta->{$k};
					}
				}
				
			} else {
				$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
				return undef; 
			}
		}
	}
	
	return $self->SUPER::step(%params);
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $lavoro = eval{API::ART::APP::Activity::LAVORO->new(ART => $art, ID => 11860)};
    
    if ($@){
        die "API::ART::APP::Activity::LAVORO error: $@";
    }
    
    my $account = $lavoro->can_do_automatic_accounting(
        accountingUser => 'pippo'
        ,planningServiceId => '12345'
    );
    
	unless (defined $account) {
		get_logger()->error("can_do_automatic_accounting error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("can_do_automatic_accounting ok");
	}
	
#	$art->save();
}

1;
