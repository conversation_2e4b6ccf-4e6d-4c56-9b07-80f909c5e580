package API::ART::APP::Activity::AS_BUILT::Binding::Action::APERTURA_TT_KO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};
	
	#devo recuperare tutte le attività figlie e farle steppare
	my $children = $activity->get_children();
	return undef unless defined $children;
	
	for my $ch (@{$children}){
		return undef unless defined $ch->step(
			ACTION => 'ANNULLAMENTO_AS_BUILT'
			, PROPERTIES => {
				ttKOreason => __("Unable to open TT")
			}
		);
		
		#procedo alla disadozione
		return undef unless defined $activity->disinherit_children( CHILDREN => [$ch] );
		
	}
	
	
	return 1;
}

1;
