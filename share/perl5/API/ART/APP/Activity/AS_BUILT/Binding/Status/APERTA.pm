package API::ART::APP::Activity::AS_BUILT::Binding::Status::APERTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use API::ART::APP::Activity::LAVORO;

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $activity_property = $activity->activity_property();
	
	# verifico le date subcontracts se presenti
	if (defined $activity_property->{subContractStartWorkDate} || defined $activity_property->{subContractEndWorkDate}){
		return undef unless defined $activity->check_subContract_work_dates(
			subContractStartWorkDate => $activity_property->{subContractStartWorkDate},
			subContractEndWorkDate => $activity_property->{subContractEndWorkDate},
		);
	}
	
	my $ids = decode_json($activity->activity_property('ids'));
	
	# recupero gli id_attivita LAVORO da adottare e poi steppare
	for my $actId (@{$ids}){
		
		my $act = eval{API::ART::APP::Activity::LAVORO->new(ART => $self->art(), ID => $actId)};
		if ($@){
			$self->art()->last_error($@);
			return undef;
		}
		return undef unless defined $act;
		
		return undef unless defined $activity->adopt_children( CHILDREN => [$act] );
		
		my $props = {
			asBuiltId => $activity->id()
		};
		my $action = 'CARICAMENTO_AS_BUILT';
		
		if ($act->activity_property('maker') eq 'Subcontract') {
			# se è nello stato IN_LAVORAZIONE_SUBAPPALTO vuol dire che non ha mai fatto lo stadio di AS_BUILT
			# se è nello stato ATTESA_RENDICONTAZIONE ha già fatto un giro e quindi deve fare un'azione diversa
			if ($act->get_current_status_name() eq 'IN_LAVORAZIONE_SUBAPPALTO'){
				$action = 'FINE_LAVORI_SUBAPPALTO';
			}
			$props->{"subContractStartWorkDate"} = $activity_property->{subContractStartWorkDate};
			$props->{"subContractEndWorkDate"} = $activity_property->{subContractEndWorkDate};
			
		}
		
		return undef unless defined $act->step(
			ACTION => $action,
			PROPERTIES => $props
		);
	}
	
	return $activity->step(
		ACTION => 'RICHIESTA_APERTURA_TT'
	);
	
};

1;
