package API::ART::APP::Activity::LAVORO::Binding::Action::_CHECK_OC;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity, $action) = @_;
	
	my $system_type_name = $activity->cached_info('SYSTEM_TYPE_NAME');
	
	my $enableGroups = ['ROOT', 'ADMIN'];
	
	if ($system_type_name eq 'LAVORO'){ #gestisco FC
		
		# gestisco la presa in carico per i service
		if (
			defined $action
			&&
			$action !~ /^(PRESA_IN_CARICO)$/
			&&
			defined $activity->activity_property('takeInCharge')
			&&
			$activity->activity_property('takeInCharge') == 0
			&&
			!$activity->_wpsoworks()->profilo()->is_admin()
			&&
			$activity->_wpsoworks()->profilo()->is_service()
		){
			$self->art()->last_error(__x("Action {action} not available for service without take in charge", action => $action));
			return undef;
		}

		push @{$enableGroups}, 'AT';
		push @{$enableGroups}, 'AT03';
		push @{$enableGroups}, 'SERVICE';
		push @{$enableGroups}, 'REMOTE_AUTOMATION';
		push @{$enableGroups}, 'REMOTE_EVENT';
		
		# verifico se è stato effettivamente inviato a OFSC
		#my $activity_property = $activity->activity_property('planningServiceId');
		
		
		if (defined $action && $action =~ /^(FINE_LAVORI_SUBAPPALTO|INOLTRO_RENDICONTAZIONE|MODIFICA|PIANIFICAZIONE)$/){
			$self->art()->last_error(__x("Action not available for fiber construction"));
			return undef;
		} elsif (defined $action && $action =~ /^(NON_NECESSARIO)$/){
			my $workType = $activity->system_property('workType');
			if ($workType !~ /^(PTE|NetworkFibercop|CorrMaintenance|ExtrMaintenance)$/){
				$self->art()->last_error(__x("Action not available for workType {workType}", workType => $workType));
				return undef;
			}
		} else {
			my $current_status = $activity->get_current_status_name();
			if (defined $action && $action =~ /^(ANNULLAMENTO|RENDICONTAZIONE)$/){
				# se e' gestione squadra e non è stato effettivamente inviato a OFSC
				if ($current_status eq 'IN_LAVORAZIONE_SIRTI'){
					$self->art()->last_error(__x("On field integration not enabled: can't do this action"));
					return undef;
				}
			} elsif (defined $action && $action =~ /^(AVANZAMENTO|FINE_LAVORI_SIRTI|SOSPENSIONE)$/){
				$self->art()->last_error(__x("On field integration not enabled: can't do this action"));
				return undef;
			}
		}
		
	} else { # altre gestioni
		
		push @{$enableGroups}, @{[
			'AT_CIVILE',
			'AT_GIUNZIONI',
			'COORDINATORE_AT_CIVILE',
			'COORDINATORE_AT_GIUNZIONI',
			'PLAN_AND_PROGRAM',
		]};
		
		if (defined $action && $action =~ /^(ANNULLAMENTO_SIRTI_OFFLINE|FINE_LAVORI|NON_NECESSARIO|SOSPENSIONE_LAVORI|RIPRESA_LAVORI|SOSPENSIONE_BD)$/){
			$self->art()->last_error(__x("Can't do this action: available only for fiber construction"));
			return undef;
		}
	}
	
	my $userGroups = $self->art()->user()->groups;
	
	# verifico che abbia almeno un gruppo associato ad FC: è indispensabile per le azioni
	# che sono utilizzate in entrambe le gestione
	my $found = 0;
	for my $enGroup (@{$enableGroups}){
		if (grep {$enGroup eq $self->art()->get_group_name($_)} @{$userGroups}){
			$found = 1; 
			last;
		}
	}
	
	unless ($found){
		$self->art()->last_error(__x("Can't do this action: user not enabled"));
		return undef;
	}
	
	return 1;
}

1;
