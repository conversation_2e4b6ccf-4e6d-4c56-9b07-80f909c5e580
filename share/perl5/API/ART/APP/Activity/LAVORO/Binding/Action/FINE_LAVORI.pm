package API::ART::APP::Activity::LAVORO::Binding::Action::FINE_LAVORI;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};
	
	my $s = $self->SUPER::can_do($activity, 'FINE_LAVORI');
	return undef unless defined $s;

	# se è assegnato a Team e non abbiamo ancora il teamId non deve essere possibile effettuare l'azione
	if ($activity->activity_property('maker') eq 'Team' && ! defined $activity->activity_property('teamId')){
		$art->last_error(__x("Unable to close activity without team assigment"));
		return undef;
	}
	
	return 1;
}

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# in questo step non si può mai modificare questi valori
	for ('cannotCloseWork', 'cannotCloseWorkReason'){
		delete $params->{PROPERTIES}->{$_} if defined $params->{PROPERTIES}->{$_};
	}

	if (defined $params->{PROPERTIES}->{startWorkDate} && defined $params->{PROPERTIES}->{endWorkDate}){

		my $startDate = $art->get_date_from_iso_date($params->{PROPERTIES}->{startWorkDate});
		my $endDate = $art->get_date_from_iso_date($params->{PROPERTIES}->{endWorkDate});
				
		$art->last_error(__x("Param {param} must be greater or equal than {param1}", param => 'startWorkDate', param1 => 'endWorkDate'))
			&& return undef
				if ($endDate < $startDate);
	}
	
	if (defined $activity->activity_property('flagFIR') && $activity->activity_property('flagFIR') == 1){
		$art->last_error(__("Attachment mandatory when FIR is required"))
			&& return undef
				if (
					! defined $params->{ATTACHMENTS}
					||
					scalar @{$params->{ATTACHMENTS}} == 0
				);
	}
	
	return 1 
}

1;
