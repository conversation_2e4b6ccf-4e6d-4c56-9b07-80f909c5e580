package API::ART::APP::Activity::LAVORO::Binding::Action::LAVORABILE_DA_SIRTI;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	if (defined $params->{PROPERTIES}->{slaStart} && defined $params->{PROPERTIES}->{slaEnd}){	
		return undef unless $activity->check_sla_dates(
			slaStart => $params->{PROPERTIES}->{slaStart},
			slaEnd => $params->{PROPERTIES}->{slaEnd},
		);
	}
	return 1;
}

1;
