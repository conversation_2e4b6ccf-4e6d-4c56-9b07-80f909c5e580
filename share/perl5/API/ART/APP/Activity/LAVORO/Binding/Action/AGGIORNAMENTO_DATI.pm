package API::ART::APP::Activity::LAVORO::Binding::Action::AGGIORNAMENTO_DATI;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub post {
	my ( $self, $activity, $params) = @_;

        my $art = $self->{ART};

	my $sender = $activity->get_sender();
        return undef unless defined $sender;

        my $data = $activity->get_data_for_notify_works();
        return undef
        unless defined $data;

        $art->last_error()
            && return undef
                unless defined $sender->notify_work_update(%{$data});

	return 1;

}

1;
