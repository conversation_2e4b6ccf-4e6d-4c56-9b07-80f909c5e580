package API::ART::APP::Activity::LAVORO::Binding::Status::IN_LAVORAZIONE_SIRTI;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){

		my $activity_properties = $activity->property();

		my $last_action;
		
		# l'eventType dovrebbe sempre essere valorizzato in questo stato ma per sicurezza lo verifico
		if (defined $activity_properties->{'eventType'}) {
			
			# se arriva dall'azione di CONFERMA non deve fare nulla in quanto è un reinoltro
			## capisco da dove sono partito prima di andare in SOSPESA per sapere dove tornare
			$last_action = $activity->get_last_action();
			return undef unless defined $last_action;

			return 1 if $last_action =~/^(CONFERMA)$/;
			
			if ($activity_properties->{'eventType'} eq 'COMPLETE'){
				if ($activity->system()->info('SYSTEM_TYPE_NAME') ne 'LAVORO') {
					return $activity->step(
						ACTION => 'FINE_LAVORI_SIRTI'
					);
				} else {
					return $activity->step(
						ACTION => 'FINE_LAVORI'
						, PROPERTIES => {
							startWorkDate => $activity_properties->{'startWorkDate'},
							endWorkDate => $activity_properties->{'endWorkDate'}
						}
					);
				}
			} elsif($activity->property('eventType') eq 'NOT_DONE'){
				return $activity->step(
					ACTION => 'SOSPENSIONE'
				);
			}
		}
		# se è gestione fiber construnction...
		if (
			$activity->system()->info('SYSTEM_TYPE_NAME') eq 'LAVORO'
		) {
			unless (defined $last_action){
				$last_action = $activity->get_last_action();
				return undef unless defined $last_action;
			}

			my $system_property = $activity->system_property();
			# ... e se l'ultima azione è RIPRESA_LAVORI devo mandare aggiornamneto
			if (
				$last_action =~/^(RIPRESA_LAVORI|EVENTO_AVVIO_LAVORI|EVENTO_ASSEGNAZIONE|RIPORTA_IN_LAVORAZIONE_SIRTI)$/
			){;
				my $sender = $activity->get_sender();
				return undef unless defined $sender;
				
				my $data = $activity->get_data_for_notify_works();
				return undef
				unless defined $data;
				
				$self->art()->last_error()
					&& return undef
						unless defined $sender->notify_work_update(%{$data});
			}
		}

		# se è un operazione contabile viene chiuso direttamente
		if ($activity_properties->{accountingOperation}){
			return $activity->step(
				ACTION => 'FINE_LAVORI'
				, DESCRIPTION => 'Chiusura automatica per operazione contabile'
				, PROPERTIES => {
					startWorkDate => $activity_properties->{'startWorkDate'},
					endWorkDate => $activity_properties->{'endWorkDate'}
				}
			);
		}
	}
	
	return 1;
};

1;
