package API::ART::APP::Activity::LAVORO::Binding::Action::_REMOTE_AUTOMATION;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_ANNULLAMENTO);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;

	my $s = $self->SUPER::pre($activity, $params);
	return undef unless defined $s;

	if (defined $params->{PROPERTIES}->{fieldServiceUserid}){
		my $teamInfo = $activity->get_team_info(userid => uc($params->{PROPERTIES}->{fieldServiceUserid}));
		return undef unless defined $teamInfo;
	
		unless (keys %$teamInfo){
			$self->art()->last_error(__x("Unknown user {userid}", userid => $params->{PROPERTIES}->{fieldServiceUserid}));
			return undef;
		}
		
		$params->{PROPERTIES}->{teamId} = $teamInfo->{CIDSAP};
		$params->{PROPERTIES}->{teamName} = $teamInfo->{COGNOME}.' '.$teamInfo->{NOME};
	}
	
	return 1 
}

1;
