package API::ART::APP::Activity::LAVORO::Binding::Action::CHIUSURA_TT;

use strict;
use warnings;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	$params->{DESCRIPTION} = 'Chiusura TT';

	#sbianco i dt dell azione SOSPENSIONE_CAUSA_TT
	$params->{PROPERTIES}->{"externalSuspensionNote"} 	= undef;
	$params->{PROPERTIES}->{"externalSuspensionId"} 	= undef;

	return 1;
}


1;
