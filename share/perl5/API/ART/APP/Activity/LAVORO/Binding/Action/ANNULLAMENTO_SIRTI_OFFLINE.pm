package API::ART::APP::Activity::LAVORO::Binding::Action::ANNULLAMENTO_SIRTI_OFFLINE;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_ANNULLAMENTO);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::can_do($activity, 'ANNULLAMENTO_SIRTI_OFFLINE');
	return undef unless defined $s;

	return 1;
}

1;
