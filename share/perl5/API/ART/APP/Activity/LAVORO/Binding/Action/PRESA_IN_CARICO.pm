package API::ART::APP::Activity::LAVORO::Binding::Action::PRESA_IN_CARICO;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};

	return 1
		if $activity->activity_property('takeInCharge') eq '0';
	
	$art->last_error(__x("Action not available for this type of activity"));
	return undef;

}

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};
	
	#imposto a 1 il dta takeInCharge
	$params->{PROPERTIES}->{"takeInCharge"} = 1;

	return 1;
}

1;
