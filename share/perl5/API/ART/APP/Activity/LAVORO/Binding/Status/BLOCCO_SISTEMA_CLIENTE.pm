package API::ART::APP::Activity::LAVORO::Binding::Status::BLOCCO_SISTEMA_CLIENTE;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){

		if ($activity->system()->info('SYSTEM_TYPE_NAME') eq 'LAVORO') {
			my $sender = $activity->get_sender();
			return undef unless defined $sender;
			
			my $data = $activity->get_data_for_notify_works();
			return undef
				unless defined $data;
			
			$self->art()->last_error()
				&& return undef
					unless defined $sender->notify_work_update(%{$data});
		}
	}
	
	return 1;
};
1;
