package API::ART::APP::Activity::LAVORO::Binding::Action::SOSPENSIONE_CAUSA_TT;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_REMOTE_AUTOMATION);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};


	# Controllo che sia un lavoro di tipo updateDatabaseF1 o updateDatabaseF2
	unless (
		$activity->system_property('workType') eq 'PTE' &&
		($activity->activity_property('updateDatabaseF1') ||
		$activity->activity_property('updateDatabaseF2'))
	){
		$art->last_error(__x("Action avaible only for updateDatabaseF1/F2 works"));
		return undef;
	}

	return 1;

}

1;
