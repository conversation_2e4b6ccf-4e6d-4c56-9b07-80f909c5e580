package API::ART::APP::Activity::LAVORO::Binding::Action::RIPRESA_LAVORI;

use strict;
use warnings;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};
	
	return 1 if $activity->_wpsoworks()->profilo()->is_admin();

	if (
		$activity->_wpsoworks()->profilo()->is_service()
		&&
		defined $activity->activity_property('takeInCharge')
		&&
		$activity->activity_property('takeInCharge') eq '0'
	){
		$art->last_error(__x("Action not available"));
		return undef;
	}

	return 1;

}

1;
