package API::ART::APP::Activity::LAVORO::Binding::Action::_ANNULLAMENTO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;

	if ($params->{ACTION} !~/^(FINE_LAVORI_OK|CHIUSURA_PARZIALE|SOSPENSIONE_LAVORI|SOSPENSIONE_BD|SOSPENSIONE_CAUSA_TT)$/ && defined $params->{DESCRIPTION} && ! defined $params->{PROPERTIES}->{cancelNote}){
		$params->{PROPERTIES}->{cancelNote} = $params->{DESCRIPTION};
	}
	
	return 1 
}

1;
