package API::ART::APP::Activity::LAVORO::Binding::Status::SMISTAMENTO_BLOCCO_SISTEMA_CLIENTE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};

	#con il metodo history vado a prendere ultimo step eseguito con azione SOSPENSIONE_CAUSA_TT
	my $history = $activity->history;
	return unless $history;

	# imposto il filtro
	return unless defined $history->set_filter(
		ACTION => [
			'SOSPENSIONE_CAUSA_TT'
		]
	);

	# recupero l'ultimo step che ha effettuato questa azione
	my $transition = $history->last;
	return unless defined $transition;

	while (1){
		if ($transition->from_status_name() eq 'BLOCCO_SISTEMA_CLIENTE') {
			$transition = $history->previous;
		} else {
			last;
		}
	}

	my $initial_status = $transition->from_status_name();
	return undef unless defined $transition;
	#arrichisco i $params

	return $activity->step(
		ACTION => 'RIPORTA_'.$initial_status
	);
		

	return 1;
	
};

1;
