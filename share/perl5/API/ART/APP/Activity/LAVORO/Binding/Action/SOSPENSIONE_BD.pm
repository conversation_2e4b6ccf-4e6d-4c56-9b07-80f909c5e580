package API::ART::APP::Activity::LAVORO::Binding::Action::SOSPENSIONE_BD;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_REMOTE_AUTOMATION);

use base qw(API::ART::Activity::Binding::Action::Base);
sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};
	my $s = $self->SUPER::can_do($activity, 'SOSPENSIONE_LAVORI');
	return undef unless defined $s;
	# Controllo che sia assegnato ai Service,sia un lavoro sul PTE e che sia di tipo updateDatabaseF1 o updateDatabaseF2
	unless (
		$activity->activity_property('maker') eq 'Subcontract' &&
		$activity->system_property('workType') eq 'PTE' &&
		($activity->activity_property('updateDatabaseF1') ||
		$activity->activity_property('updateDatabaseF2'))
	){
		$art->last_error(__x("Action avaible only for updateDatabaseF1/F2 works"));
		return undef;
	}

	return 1;
}

1;
