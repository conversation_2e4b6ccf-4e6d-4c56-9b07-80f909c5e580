package API::ART::APP::Activity::LAVORO::Binding::Action::SOCIETARIZZAZIONE_CLIENTE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	my @parts = split ('-', $activity->system->info('SYSTEM_NAME'));

	$parts[2] = $params->{PROPERTIES}->{'projectId'};

	my $description = join('-',@parts);

	my $end_date = $activity->system()->info('ENDING_DATE');

	if ($end_date){
		# aggiorna massivamente tutte le ckeys
		my $prepare = $art->_create_prepare('SOC_sis_dis', "
			update sistemi
			set data_dismissione = null
			where id_sistema = ?
		");
		return undef unless defined $prepare;
		my $res= $prepare->do($activity->system_id());

		unless ($res) {
			$art->_dbh()->do("rollback to savepoint NETWORK_sp");
			$art->last_error($art->_dbh()->get_errormessage().': '.$art->_dbh()->get_errormessage());
			return undef;
		}
	}

	return undef unless $activity->system()->set_description($description);

	if ($end_date){
		return undef
			unless $activity->system()->end(DATE => $end_date);
	}

	$params->{PROPERTIES}->{'projectIdOld'} = $activity->activity_property('projectId');

	my $assetId = $activity->system_property('assetId');

	for my $ass (@$assetId){
		$ass = $params->{PROPERTIES}->{'projectId'} if $ass eq $activity->activity_property('projectId');
	}

	return undef
		unless $activity->system()->set_property(PROPERTIES => {
			networkId		=> $params->{PROPERTIES}->{'projectId'},
			networkIdOld	=> $activity->activity_property('projectId'),
			projectId		=> $params->{PROPERTIES}->{'projectId'},
			projectIdOld	=> $activity->activity_property('projectId'),
			assetId			=> $assetId,
		});

	return 1;
}

1;
