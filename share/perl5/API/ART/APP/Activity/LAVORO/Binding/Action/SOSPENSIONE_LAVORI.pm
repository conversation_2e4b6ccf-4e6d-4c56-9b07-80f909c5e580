package API::ART::APP::Activity::LAVORO::Binding::Action::SOSPENSIONE_LAVORI;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Action::_REMOTE_AUTOMATION);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	my $art = $self->{ART};
	
	my $s = $self->SUPER::can_do($activity, 'SOSPENSIONE_LAVORI');
	return undef unless defined $s;

	# se è integrato con il Field Service la sospensione non deve essere possibile per nessuno
	if (defined $activity->activity_property('fieldService')){
		$art->last_error(__x("Unable to suspend activity when field service enabled"));
		return undef;
	}

	# Escludere azione SOSPENSIONE_LAVORI per updateDatabaseF1|updateDatabaseF2 (devono usare SOSPENSIONE_BD)
	if (
		$activity->activity_property('maker') eq 'Subcontract' &&
		$activity->system_property('workType') eq 'PTE' &&
		($activity->activity_property('updateDatabaseF1')||
		$activity->activity_property('updateDatabaseF2'))
	){
		$art->last_error(__x("Use SOSPENSIONE_BD action for updateDatabaseF1/F2 works"));
		return undef;
	}

	return 1;
}

1;
