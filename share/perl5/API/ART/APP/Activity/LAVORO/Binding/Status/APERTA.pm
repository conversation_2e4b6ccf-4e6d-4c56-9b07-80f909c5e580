package API::ART::APP::Activity::LAVORO::Binding::Status::APERTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
		my $system_type_name = $activity->system()->info('SYSTEM_TYPE_NAME');
		my $property = $activity->activity_property();
		
		# se si tratta di un PTE potrebbe essere necessario invalidare i precedenti lavori espletati
		if ($activity->system_property('workType') eq 'PTE' && $property->{invalidatePreviousReferences}){
			# cerco tutti i lavori espletati sullo stesso riferimento
			my $params_for_cerca = {
				"customerId"		=> $property->{customerId}
				,"contractId"		=> $property->{contractId}
				,"workType"			=> [$system_type_name]
				,"spWorkType"		=> [$activity->system_property('workType')]
				,"ref00"			=> $property->{ref00}
				,"ref01"			=> $property->{ref01}
				,"assetId"			=> $activity->system_property('assetId')->[0]
				,"STATUS_IN"		=> ['ESPLETATO']
			};
			for ('survey','infrastructure','laying','junction','updateDatabaseF1','updateDatabaseF2','testApp','testOTDR','restoration'){
				$params_for_cerca->{$_} = $property->{$_} if $property->{$_};
			}
			my $activities_to_invalidate = $activity->get_coll_lavoro()->cerca(%$params_for_cerca);
			return undef
				unless defined $activities_to_invalidate;

			for my $activity_to_invalidate (@{$activities_to_invalidate}){
				return undef
					unless defined $activity_to_invalidate->invalidate(
						DESCRIPTION => 'Invalidata a seguito apertura ticket '.$activity->id(),
						PROPERTIES => {
							invalidateActivityId => $activity->id()
						}
					);
			}
		}

		my $sender = $activity->get_sender();
		return undef unless defined $sender;
		
		my $data = $activity->get_data_for_notify_works();
		return undef
			unless defined $data;
		
		$self->art()->last_error()
			&& return undef
				unless defined $sender->notify_work_open(%{$data});

		if (defined $property->{'maker'} && $property->{'maker'} eq 'Team'){
			if (defined $property->{slaStart} && defined $property->{slaEnd}){
				$self->art()->last_error()
					&& return undef
						unless defined $activity->check_sla_dates(
							slaStart => $property->{slaStart},
							slaEnd => $property->{slaEnd}
						);
			}

			return $activity->step(
				ACTION => 'LAVORABILE_DA_SIRTI'
				, PROPERTIES => {
					slaStart => $property->{slaStart},
					slaEnd => $property->{slaEnd}
				}
			);
		} elsif (defined $property->{'maker'} && $property->{'maker'} eq 'Subcontract'){
			my $props = {};
			# inizializzo parametri obbligatori
			for my $key ('startPlannedDate', 'subContractCode', 'subContractName'){
				$props->{$key} = $property->{$key};
			}
			$props->{endPlannedDate} = $property->{endPlannedDate} if defined $property->{endPlannedDate};
			
			return $activity->step(
				ACTION => 'LAVORABILE_DA_SUBAPPALTO'
				, PROPERTIES => $props
			);
		}
	}

	return 1;
	
};

1;
