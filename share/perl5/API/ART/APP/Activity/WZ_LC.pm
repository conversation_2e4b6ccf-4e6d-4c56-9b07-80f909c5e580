package API::ART::APP::Activity::WZ_LC;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

#use WPSOWZ::MQ::Sender::WZ_LC;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

#sub get_sender {
#	my $self = shift;
#	$self->{SENDER} = WPSOWZ::MQ::Sender::WZ_LC->instance(DB => $self->get_db());
#	return $self->{SENDER};
#}
#
#sub update_macrotasks_count{
#	my $self = shift;
#	
#	# recupero i figli
#	my $children = $self->get_children(ACTIVITY_TYPE_NAME => ['MACRO_LAVORO']);
#	return undef unless $children;
#	
#	my $update = {
#		totalTasks				=> 0,
#		newTasks				=> 0,
#		assignedTasks			=> 0,
#		executedTasks			=> 0,
#		waitingForDesignerTasks	=> 0,
#		completedTasks			=> 0,
#		notExecutedTasks		=> 0
#	};
#	
#	my $remap ={
#		NUOVO					=> 'newTasks',
#		ASSEGNATO				=> 'assignedTasks',
#		FINE_LAVORI				=> 'executedTasks',
#		ATTESA_NOTIFICA_SINFO	=> 'waitingForDesignerTasks',
#		NON_ESEGUITO			=> 'notExecutedTasks',
#		ESPLETATO				=> 'completedTasks'
#	};
#	
#	for my $ch (@{$children}){
#		# gli stati di ANNULLATO|NON_NECESSARIO|ATTESA_CONFERMA dei figli non devono essere gestiti
#		next if $ch->get_current_status_name() =~/^(ANNULLATO|NON_NECESSARIO|ATTESA_CONFERMA)$/;
#		$update->{totalTasks}++;
#		$update->{$remap->{$ch->get_current_status_name()}}++;
#	}
#	
#	return undef
#		unless $self->system()->set_property( PROPERTIES => $update );
#	
#	$self->art()->last_error(__x("Anomaly: unable to update work zone"))
#		&& return undef
#			unless 
#				$self->get_sender()->update_stato_work_zone(
#				SOURCE_REF => $self->id(),
#				DATA => {
#					workZoneId => $self->activity_property('workZoneId')
#				}
#			);
#	
#	return 1;
#}
#
#sub get_totals_macro_lavori{
#	my $self = shift;
#	
#	my $system_property = $self->system_property();
#	
#	return {
#		totalTasks				=> $system_property->{totalTasks},
#		newTasks				=> $system_property->{newTasks},
#		assignedTasks			=> $system_property->{assignedTasks},
#		executedTasks			=> $system_property->{executedTasks},
#		waitingForDesignerTasks	=> $system_property->{waitingForDesignerTasks},
#		completedTasks			=> $system_property->{completedTasks},
#		notExecutedTasks		=> $system_property->{notExecutedTasks}
#	};
#}

1;
