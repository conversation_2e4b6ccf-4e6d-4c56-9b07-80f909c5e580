#
#file di configurazione di start_stop_daemon
#
# @users_management:log:$LOG_PATH/UsersManagement/users_management.log.$CURDATE
# @users_management:log:$LOG_PATH/UsersManagement/users_management.full.log.$CURDATE
users_management              arg   users_management
users_management              arg   --api-user=${WPSOWORKS_ADMIN_USER}
users_management              arg   --api-password=${WPSOWORKS_ADMIN_PASSWORD}
users_management              arg   --log-config=${COM}/etc/users_management.log4perl.conf
users_management              arg   --driver=API::ART::Driver::Users::Baseline
users_management              arg   --oBaselineConnectString=${SQLID_WPSOCORE}
users_management              arg   --manage=C
users_management              arg   --manage=U
users_management              arg   --manage=D
users_management              arg   --notify-email=${MAILADDR_BG}
users_management              arg   --daemon=60
users_management              arg   --transaction-mode=c
#
# @MACROTASKS_ENEL_FTTH_sync_data:log:$LOG_PATH/MACROTASKS_ENEL_FTTH_sync_data.log.$CURDATE
# @MACROTASKS_ENEL_FTTH_sync_data:log:$LOG_PATH/MACROTASKS_ENEL_FTTH_sync_data.full.log.$CURDATE
MACROTASKS_ENEL_FTTH_sync_data		arg   sync_data
MACROTASKS_ENEL_FTTH_sync_data		arg   MACROTASKS
MACROTASKS_ENEL_FTTH_sync_data		arg   ENEL
MACROTASKS_ENEL_FTTH_sync_data		arg   FTTH
MACROTASKS_ENEL_FTTH_sync_data		arg   --daemon=3600
MACROTASKS_ENEL_FTTH_sync_data		arg   --transaction-mode=c
#
#
# @CAVI_ENEL_FTTH_sync_data:log:$LOG_PATH/CAVI_ENEL_FTTH_sync_data.log.$CURDATE
# @CAVI_ENEL_FTTH_sync_data:log:$LOG_PATH/CAVI_ENEL_FTTH_sync_data.full.log.$CURDATE
CAVI_ENEL_FTTH_sync_data		arg   sync_data
CAVI_ENEL_FTTH_sync_data		arg   CAVI
CAVI_ENEL_FTTH_sync_data		arg   ENEL
CAVI_ENEL_FTTH_sync_data		arg   FTTH
CAVI_ENEL_FTTH_sync_data		arg   --daemon=900
CAVI_ENEL_FTTH_sync_data		arg   --transaction-mode=c
#
# @NODI_ENEL_FTTH_sync_data:log:$LOG_PATH/NODI_ENEL_FTTH_sync_data.log.$CURDATE
# @NODI_ENEL_FTTH_sync_data:log:$LOG_PATH/NODI_ENEL_FTTH_sync_data.full.log.$CURDATE
NODI_ENEL_FTTH_sync_data		arg   sync_data
NODI_ENEL_FTTH_sync_data		arg   NODI
NODI_ENEL_FTTH_sync_data		arg   ENEL
NODI_ENEL_FTTH_sync_data		arg   FTTH
NODI_ENEL_FTTH_sync_data		arg   --daemon=900
NODI_ENEL_FTTH_sync_data		arg   --transaction-mode=c
#
# @wz_info_for_works_ack:log:$LOG_PATH/WzInfoForAcks/consumer.log.$CURDATE
# @wz_info_for_works_ack:log:$LOG_PATH/WzInfoForAcks/ramq.log.$CURDATE
# @wz_info_for_works_ack:log:$LOG_PATH/WzInfoForAcks/ramq_full.log.$CURDATE
wz_info_for_works_ack          arg   ramq
wz_info_for_works_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
wz_info_for_works_ack          arg   --daemon=30
wz_info_for_works_ack          arg   --transaction-mode=c
wz_info_for_works_ack          arg   ${ETC}/ramq/System/WZ/InfoForWorksAck.conf
#
# @project_cities_group_creazione:log:$LOG_PATH/ProjectCitiesGroupCreazione/consumer.log.$CURDATE
# @project_cities_group_creazione:log:$LOG_PATH/ProjectCitiesGroupCreazione/ramq.log.$CURDATE
# @project_cities_group_creazione:log:$LOG_PATH/ProjectCitiesGroupCreazione/ramq_full.log.$CURDATE
project_cities_group_creazione          arg   ramq
project_cities_group_creazione          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
project_cities_group_creazione          arg   --daemon=300
project_cities_group_creazione          arg   --transaction-mode=c
project_cities_group_creazione          arg   ${ETC}/ramq/ProjectCitiesGroupCreazione.conf
#
# @lavoro_gw_richiesta_ack:log:$LOG_PATH/GWRichiestaAck/consumer.log.$CURDATE
# @lavoro_gw_richiesta_ack:log:$LOG_PATH/GWRichiestaAck/ramq.log.$CURDATE
# @lavoro_gw_richiesta_ack:log:$LOG_PATH/GWRichiestaAck/ramq_full.log.$CURDATE
lavoro_gw_richiesta_ack          arg   ramq
lavoro_gw_richiesta_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lavoro_gw_richiesta_ack          arg   --daemon=30
lavoro_gw_richiesta_ack          arg   --transaction-mode=c
lavoro_gw_richiesta_ack          arg   ${ETC}/ramq/Activity/LAVORO/GWRichiestaAck.conf
#
# @lavoro_gw_richiesta_avanzamento:log:$LOG_PATH/GWRichiestaAvanzamento/consumer.log.$CURDATE
# @lavoro_gw_richiesta_avanzamento:log:$LOG_PATH/GWRichiestaAvanzamento/ramq.log.$CURDATE
# @lavoro_gw_richiesta_avanzamento:log:$LOG_PATH/GWRichiestaAvanzamento/ramq_full.log.$CURDATE
lavoro_gw_richiesta_avanzamento          arg   ramq
lavoro_gw_richiesta_avanzamento          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lavoro_gw_richiesta_avanzamento          arg   --daemon=30
lavoro_gw_richiesta_avanzamento          arg   --transaction-mode=c
lavoro_gw_richiesta_avanzamento          arg   ${ETC}/ramq/Activity/LAVORO/GWRichiestaAvanzamento.conf
#
# @gestione_tt:log:$LOG_PATH/GestioneTT/consumer.log.$CURDATE
# @gestione_tt:log:$LOG_PATH/GestioneTT/ramq.log.$CURDATE
# @gestione_tt:log:$LOG_PATH/GestioneTT/ramq_full.log.$CURDATE
gestione_tt          arg   ramq
gestione_tt          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
gestione_tt          arg   --daemon=30
gestione_tt          arg   --transaction-mode=c
gestione_tt          arg   ${ETC}/ramq/Activity/LAVORO/GestioneTT.conf
#
# @lavoro_quarantena:log:$LOG_PATH/lavoro_quarantena.log.$CURDATE
# @lavoro_quarantena:log:$LOG_PATH/lavoro_quarantena.full.log.$CURDATE
lavoro_quarantena		arg   lavoro_quarantena
lavoro_quarantena		arg   --daemon=1800
lavoro_quarantena		arg   --transaction-mode=c
#
# @lavoro_aggiorna_rendicontazione:log:$LOG_PATH/AggiornaRendicontazione/consumer.log.$CURDATE
# @lavoro_aggiorna_rendicontazione:log:$LOG_PATH/AggiornaRendicontazione/ramq.log.$CURDATE
# @lavoro_aggiorna_rendicontazione:log:$LOG_PATH/AggiornaRendicontazione/ramq_full.log.$CURDATE
lavoro_aggiorna_rendicontazione          arg   ramq
lavoro_aggiorna_rendicontazione          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lavoro_aggiorna_rendicontazione          arg   --daemon=10
lavoro_aggiorna_rendicontazione          arg   --transaction-mode=c
lavoro_aggiorna_rendicontazione          arg   ${ETC}/ramq/Activity/LAVORO/AggiornaRendicontazione.conf
#
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/consumer.log.$CURDATE
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/ramq.log.$CURDATE
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/ramq_full.log.$CURDATE
fiber_construction_sync          arg   ramq
fiber_construction_sync          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
fiber_construction_sync          arg   --daemon=3
fiber_construction_sync          arg   --transaction-mode=c
fiber_construction_sync          arg   ${ETC}/ramq/FiberConstructionSync.conf
#
# @service_management:log:$LOG_PATH/service_management/consumer.log.$CURDATE
# @service_management:log:$LOG_PATH/service_management/ramq.log.$CURDATE
# @service_management:log:$LOG_PATH/service_management/ramq_full.log.$CURDATE
service_management          arg   ramq
service_management          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
service_management          arg   --daemon=3
service_management          arg   --transaction-mode=c
service_management          arg   ${ETC}/ramq/ServiceManagement.conf
#
# @api_art_activity_source_lavoro:log:$LOG_PATH/api-art-activity-source-LAVORO.log.$CURDATE
# @api_art_activity_source_lavoro:log:$LOG_PATH/api-art-activity-source-LAVORO_full.log.$CURDATE
api_art_activity_source_lavoro   arg   api-art-activity-source
api_art_activity_source_lavoro   arg   --daemon=1
api_art_activity_source_lavoro   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lavoro   arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_source_lavoro   arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_source_lavoro   arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_source_lavoro   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lavoro   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lavoro   arg   --log-idle=100
api_art_activity_source_lavoro   arg   --log-level=INFO
api_art_activity_source_lavoro   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lavoro   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lavoro   arg   --activity-type-name=LAVORO
#
# @api_art_activity_source_as_built:log:$LOG_PATH/api-art-activity-source-AS_BUILT.log.$CURDATE
# @api_art_activity_source_as_built:log:$LOG_PATH/api-art-activity-source-AS_BUILT_full.log.$CURDATE
api_art_activity_source_as_built   arg   api-art-activity-source
api_art_activity_source_as_built   arg   --daemon=1
api_art_activity_source_as_built   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_as_built   arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_source_as_built   arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_source_as_built   arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_source_as_built   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_as_built   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_as_built   arg   --log-idle=100
api_art_activity_source_as_built   arg   --log-level=INFO
api_art_activity_source_as_built   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_as_built   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_as_built   arg   --activity-type-name=AS_BUILT
#
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink.log.$CURDATE
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink_full.log.$CURDATE
api_art_activity_source_shrink   arg   api-art-activity-source-shrink
api_art_activity_source_shrink   arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_source_shrink   arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_source_shrink   arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_source_shrink   arg   --log-level=INFO
api_art_activity_source_shrink   arg   --daemon=3600
api_art_activity_source_shrink   arg   --transaction-mode=c
api_art_activity_source_shrink   arg   --max-rows=1000000
#
# @api_art_activity_stream_lavoro:log:$LOG_PATH/api-art-activity-stream-lavoro.log.$CURDATE
# @api_art_activity_stream_lavoro:log:$LOG_PATH/api-art-activity-stream-lavoro_full.log.$CURDATE
api_art_activity_stream_lavoro   arg   api-art-activity-stream-lavoro
api_art_activity_stream_lavoro   arg   --daemon=1
api_art_activity_stream_lavoro   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lavoro   arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_stream_lavoro   arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_stream_lavoro   arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_stream_lavoro   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lavoro   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lavoro   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lavoro   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lavoro   arg   --exclude-history
api_art_activity_stream_lavoro   arg   --log-idle=100
api_art_activity_stream_lavoro   arg   --log-level=INFO
api_art_activity_stream_lavoro   arg   --log-config=${ETC}/api-art-activity-stream-lavoro.log4perl.conf
api_art_activity_stream_lavoro   arg   --client-id=${ART_INSTANCE_NAME}-lavoro
api_art_activity_stream_lavoro   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LAVORO
api_art_activity_stream_lavoro   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_as_built:log:$LOG_PATH/api-art-activity-stream-as-built.log.$CURDATE
# @api_art_activity_stream_as_built:log:$LOG_PATH/api-art-activity-stream-as-built_full.log.$CURDATE
api_art_activity_stream_as_built  arg   api-art-activity-stream-as-built
api_art_activity_stream_as_built  arg   --daemon=1
api_art_activity_stream_as_built  arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_as_built  arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_stream_as_built  arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_stream_as_built  arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_stream_as_built  arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_as_built  arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_as_built  arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_as_built  arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_as_built  arg   --exclude-history
api_art_activity_stream_as_built  arg   --log-idle=100
api_art_activity_stream_as_built  arg   --log-level=INFO
api_art_activity_stream_as_built  arg   --log-config=${ETC}/api-art-activity-stream-as-built.log4perl.conf
api_art_activity_stream_as_built  arg   --client-id=${ART_INSTANCE_NAME}-as-built
api_art_activity_stream_as_built  arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-AS_BUILT
api_art_activity_stream_as_built  arg   --offset-group=${ART_INSTANCE_NAME}
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source.log.$CURDATE
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source_full.log.$CURDATE
api_art_activity_attachments_source   arg   api-art-activity-attachments-source
api_art_activity_attachments_source   arg   --art-id=${WPSOWORKS_ARTID}
api_art_activity_attachments_source   arg   --art-user-name=${WPSOWORKS_SCRIPT_USER}
api_art_activity_attachments_source   arg   --art-user-pwd=${WPSOWORKS_SCRIPT_PASSWORD}
api_art_activity_attachments_source   arg   --art-index=${WPSO_APPLICATION_NAME}
api_art_activity_attachments_source   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_attachments_source   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_attachments_source   arg   --log-level=INFO
api_art_activity_attachments_source   arg   --config=${COM}/share/schemas/net/sirti/ict/art/api/api-art-activity-attachments.json
api_art_activity_attachments_source   arg   --client-id=${ART_INSTANCE_NAME}
api_art_activity_attachments_source   arg   --daemon=1
#
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/consumer.log.$CURDATE
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/ramq.log.$CURDATE
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/ramq_full.log.$CURDATE
network_annullamento_fibercop          arg   ramq
network_annullamento_fibercop          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_annullamento_fibercop          arg   --daemon=5
network_annullamento_fibercop          arg   --transaction-mode=c
network_annullamento_fibercop          arg   ${ETC}/ramq/Activity/LAVORO/NetworkAnnullamentoFibercop.conf
#
# @lc02_booking:log:$LOG_PATH/LC02Booking/consumer.log.$CURDATE
# @lc02_booking:log:$LOG_PATH/LC02Booking/ramq.log.$CURDATE
# @lc02_booking:log:$LOG_PATH/LC02Booking/ramq_full.log.$CURDATE
lc02_booking          arg   ramq
lc02_booking          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lc02_booking          arg   ${ETC}/ramq/Activity/LAVORO/LC02Booking.conf
lc02_booking          arg   --daemon=5
lc02_booking          arg   --transaction-mode=c
#
# @network_booking:log:$LOG_PATH/NetworkBooking/consumer.log.$CURDATE
# @network_booking:log:$LOG_PATH/NetworkBooking/ramq.log.$CURDATE
# @network_booking:log:$LOG_PATH/NetworkBooking/ramq_full.log.$CURDATE
network_booking          arg   ramq
network_booking          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_booking          arg   ${ETC}/ramq/Activity/LAVORO/NetworkBooking.conf
network_booking          arg   --daemon=5
network_booking          arg   --transaction-mode=c
#
# @corrmaintenance_booking:log:$LOG_PATH/CorrMaintenanceBooking/consumer.log.$CURDATE
# @corrmaintenance_booking:log:$LOG_PATH/CorrMaintenanceBooking/ramq.log.$CURDATE
# @corrmaintenance_booking:log:$LOG_PATH/CorrMaintenanceBooking/ramq_full.log.$CURDATE
corrmaintenance_booking          arg   ramq
corrmaintenance_booking          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
corrmaintenance_booking          arg   ${ETC}/ramq/Activity/LAVORO/CorrMaintenanceBooking.conf
corrmaintenance_booking          arg   --daemon=5
corrmaintenance_booking          arg   --transaction-mode=c
#
# @rashrink:log:$LOG_PATH/rashrink.log.$CURDATE
rashrink          arg   rashrink
rashrink          arg   --connect-string=${SQLID_RA}
rashrink          arg   --retention=1
rashrink          arg   --max-sessions=100
rashrink          arg   --runs=0
rashrink          arg   --delay=30
rashrink          arg   --force
rashrink          arg   --do-commit
#
