{
	ID_TAG				: "GWRichiestaAvanzamento",
	SESSION_TYPE		: "TARGET",
	SESSION_DESCRIPTION	: "Gestione avanzamento richiesta su GW",
	CLASS				: "WPSOWORKS::MQ::Consumer::Activity::LAVORO::GWRichiestaAvanzamento",
	SOURCE_SERVICE		: "ENFTTH_WORKSGW",
	SOURCE_CONTEXT		: "LAVORI",
	TARGET_SERVICE		: "ENFTTH_WORKS",
	TARGET_CONTEXT		: "LAVORI",
	DB					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE	: "yes",
	WORK_CONTEXT		: {}
}
