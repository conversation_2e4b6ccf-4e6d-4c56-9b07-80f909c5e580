{
	ID_TAG				: "Gestione<PERSON>",
	SESSION_TYPE		: "TARGET",
	SESSION_DESCRIPTION	: "Gestione TT da CORE",
	CLASS				: "WPSOWORKS::MQ::Consumer::Activity::LAVORO::GestioneTT",
	SOURCE_SERVICE		: "ENFTTH_CORE",
	SOURCE_CONTEXT		: "CORE_TT",
	TARGET_SERVICE		: "ENFTTH_WORKS",
	TARGET_CONTEXT		: "CORE_TT",
	DB					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE	: "yes",
	WORK_CONTEXT		: {}
}
