
{
	ID_TAG				:	"<PERSON><PERSON><PERSON><PERSON>Ack",
	SESSION_TYPE		:	"SOURCE",
	SESSION_DESCRIPTION	:	"WORKS - GW Richiesta Ack Ack",
	CLASS				:	"WPSOWORKS::MQ::Consumer::Activity::LAVORO::GWRichiestaAck",
	SOURCE_SERVICE		:	"ENFTTH_WORKS",
	SOURCE_CONTEXT		:	"LAVORI",
	TARGET_SERVICE		:	"ENFTTH_WORKSGW",
	TARGET_CONTEXT		:	"LAVORI",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	USE_ACK_TYPE : "ALL",
	WORK_CONTEXT		: {}
}


