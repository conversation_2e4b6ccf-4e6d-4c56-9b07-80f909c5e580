{
	ID_TAG				: "WzInfoForAcks",
	SESSION_TYPE		: "SOURCE",
	SESSION_DESCRIPTION	: "WZ - Ack richiesta info AP",
	CLASS				: "WPSOWORKS::MQ::Consumer::System::WZ::InfoForWorksAck",
	SOURCE_SERVICE		: "ENFTTH_WORKS",
	SOURCE_CONTEXT		: "WORK_ZONE",
	TARGET_SERVICE		: "ENFTTH_AP",
	TARGET_CONTEXT		: "AREA_PERMESSI",
	DB					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE	: "yes",
	WORK_CONTEXT		: {}
}
