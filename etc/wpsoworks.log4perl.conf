
log4perl.logger.rootLogger = FATAL, ErrScreen
log4perl.logger.WPSOWORKS   = DEBUG, FullLog
log4perl.logger.COMMON     = WARN, FullLog

log4perl.logger.WPSOWORKS.BIN = DEBUG, Log, Screen
log4perl.logger.WPSOWORKS.LIB = DEBUG, Log, Screen

log4perl.appender.ErrScreen = Log::Dispatch::Screen
log4perl.appender.ErrScreen.stderr = 1
log4perl.appender.ErrScreen.Threshold = FATAL
log4perl.appender.ErrScreen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.ErrScreen.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

log4perl.appender.Screen = Log::Dispatch::Screen
log4perl.appender.Screen.Threshold = INFO
log4perl.appender.Screen.stderr = 0
log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.Screen.layout.ConversionPattern = %p> %m%n

log4perl.appender.Log = Log::Dispatch::File
log4perl.appender.Log.Threshold = INFO
log4perl.appender.Log.filename =  sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{LOG_PATH} . '/' . ( $ENV{LOG_TREE} || '' ) . '/' . ( $ENV{LOG_FILE_PREFIX} || '' ) . basename( $0 ) . '.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.Log.mode = append
log4perl.appender.Log.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.Log.layout.ConversionPattern = %d %p> %m%n

log4perl.appender.FullLog = Log::Dispatch::File
log4perl.appender.FullLog.Threshold = DEBUG
log4perl.appender.FullLog.filename = sub { use Date::Calc qw( Today_and_Now ); use File::Basename; $ENV{LOG_PATH} . '/' . ( $ENV{FULL_LOG_FILE} ? $ENV{FULL_LOG_FILE} . '.' . sprintf("%04d%02d%02d", Today_and_Now) : ( $ENV{LOG_TREE} || '' ) . '/' . ( $ENV{LOG_FILE_PREFIX} || '' ) . basename( $0 ) . '.full.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) ) }
log4perl.appender.FullLog.mode = append
log4perl.appender.FullLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.FullLog.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

