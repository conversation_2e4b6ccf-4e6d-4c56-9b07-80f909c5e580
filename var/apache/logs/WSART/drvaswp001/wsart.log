[API::ART::REST:103162] info @2025-07-14 15:13:08> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103161] info @2025-07-14 15:13:08> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103163] info @2025-07-14 15:13:08> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103164] info @2025-07-14 15:13:09> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103160] info @2025-07-14 15:13:09> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103163] info @2025-07-14 15:13:09> Error:
$VAR1 = {
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => 'b1877d54ac43fa8f04ebfcfd98da8ffe',
          'message' => 'Forbidden'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103162] info @2025-07-14 15:13:09> Error:
$VAR1 = {
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'message' => 'Forbidden',
          'UUID' => '6c25027abe618ae56b5f304485752bfa'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103161] info @2025-07-14 15:13:09> Error:
$VAR1 = {
          'UUID' => 'cefa07d798220f31ec00c8fbba2fb9c4',
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'message' => 'Forbidden'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103160] info @2025-07-14 15:13:10> Error:
$VAR1 = {
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => '7c294b9d449463c65c8ceb19562ad5ad',
          'message' => 'Forbidden'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103164] info @2025-07-14 15:13:10> Error:
$VAR1 = {
          'message' => 'Forbidden',
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => '07b5d01a2bad99ac843675379e48e878'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103371] info @2025-07-14 15:13:10> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103371] info @2025-07-14 15:13:10> Error:
$VAR1 = {
          'message' => 'Forbidden',
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => '4b9a0d4183328ebcb04d1384dfd8370b'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:103163] info @2025-07-14 15:15:19> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103162] info @2025-07-14 15:15:20> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103376] info @2025-07-14 15:15:20> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103375] info @2025-07-14 15:15:20> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103374] info @2025-07-14 15:15:20> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103377] info @2025-07-14 15:15:21> Instance ELK configuration loaded from /home/<USER>/WPSOWORKS/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:103376] info @2025-07-14 15:15:21> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103375] info @2025-07-14 15:15:21> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103374] info @2025-07-14 15:15:21> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103377] info @2025-07-14 15:15:21> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103374] warning @2025-07-14 15:15:21> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkapp002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:103377] warning @2025-07-14 15:15:22> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkapp002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:103160] info @2025-07-16 12:31:50> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:103160] warning @2025-07-16 12:31:50> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkapp002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
