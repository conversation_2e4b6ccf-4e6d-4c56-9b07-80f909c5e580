[Mon Jul 14 14:31:11.655201 2025] [mpm_prefork:notice] [pid 99015] AH00163: Apache/2.4.37 (Red Hat Enterprise Linux) mod_perl/2.0.12 Perl/v5.26.3 configured -- resuming normal operations
[Mon Jul 14 14:31:11.655390 2025] [core:notice] [pid 99015] AH00094: Command line: '/usr/sbin/httpd -f /home/<USER>/WPSOWORKS/etc/httpd.WSART.conf'
[Mon Jul 14 14:52:17.626920 2025] [perl:error] [pid 99019] [client 10.1.28.28:44050] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:40.807675 2025] [perl:error] [pid 99019] [client 10.1.28.28:44662] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: Attempt to reload API/ART/REST.pm aborted.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:40.859896 2025] [perl:error] [pid 99019] [client 10.1.28.28:44664] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: Attempt to reload API/ART/REST.pm aborted.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:41.205303 2025] [perl:error] [pid 99022] [client 10.1.28.28:44658] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:41.281522 2025] [perl:error] [pid 99021] [client 10.1.28.28:44656] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:41.317591 2025] [perl:error] [pid 99020] [client 10.1.28.28:44654] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:04:41.318847 2025] [perl:error] [pid 99018] [client 10.1.28.28:44660] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:06:52.881274 2025] [mpm_prefork:notice] [pid 102640] AH00163: Apache/2.4.37 (Red Hat Enterprise Linux) mod_perl/2.0.12 Perl/v5.26.3 configured -- resuming normal operations
[Mon Jul 14 15:06:52.881485 2025] [core:notice] [pid 102640] AH00094: Command line: '/usr/sbin/httpd -f /home/<USER>/WPSOWORKS/etc/httpd.WSART.conf'
[Mon Jul 14 15:07:35.956910 2025] [perl:error] [pid 102643] [client 10.1.28.28:44782] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:07:36.035991 2025] [perl:error] [pid 102646] [client 10.1.28.28:44784] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:07:36.043962 2025] [perl:error] [pid 102647] [client 10.1.28.28:44776] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:07:36.273546 2025] [perl:error] [pid 102644] [client 10.1.28.28:44778] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:07:36.278119 2025] [perl:error] [pid 102645] [client 10.1.28.28:44780] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:07:36.444553 2025] [perl:error] [pid 102706] [client 10.1.28.28:44786] Error while loading /home/<USER>/WPSOWORKS/bin/WSART/app.psgi: isa check for "log_dir" failed: log directory "/home/<USER>/WPSOWORKS/var/apache/logs/WSART" does not exist and unable to create it. at /usr/local/share/perl5/Dancer2/Logger/File.pm line 31.\nBEGIN failed--compilation aborted at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 34.\nCompilation failed in require at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\nBEGIN failed--compilation aborted at /home/<USER>/WPSOWORKS/bin/WSART/app.psgi line 6.\n, referer: http://drvaswp001.corp.sirti.net:10160/
[Mon Jul 14 15:11:15.837430 2025] [mpm_prefork:notice] [pid 103157] AH00163: Apache/2.4.37 (Red Hat Enterprise Linux) mod_perl/2.0.12 Perl/v5.26.3 configured -- resuming normal operations
[Mon Jul 14 15:11:15.837619 2025] [core:notice] [pid 103157] AH00094: Command line: '/usr/sbin/httpd -f /home/<USER>/WPSOWORKS/etc/httpd.WSART.conf'
DEBUG> [103163] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103163] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103162] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103162] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103163] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103163] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103163] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103163] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103163] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103163] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103163] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103162] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103162] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103162] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103162] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103162] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103162] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103162] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103161] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103161] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103161] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103161] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103161] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103161] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103161] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103161] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103161] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103160] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103160] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103160] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103160] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103160] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103160] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103160] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103160] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103160] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103164] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103164] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103164] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103164] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103164] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103164] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103164] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103164] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103164] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103371] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103371] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103371] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103371] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103371] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103371] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103371] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103371] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103371] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103376] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103376] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103376] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103376] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103376] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103376] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103376] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103376] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103376] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103375] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103375] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103375] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103375] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103375] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103375] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103375] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103375] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103375] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103374] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103374] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103374] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103374] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103374] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103374] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103374] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103374] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103374] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
DEBUG> [103377] WPSOWORKS::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server
DEBUG> [103377] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd)
DEBUG> [103377] WPSOWORKS::register_master_sessions: La sessione master dell'utente sinfo (84c31cc0eddc6a121f3507c23d2cf5cd) è stata salvata
DEBUG> [103377] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85)
DEBUG> [103377] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsocore (5b37aba606dbec6abf7d0307377cdc85) è stata salvata
DEBUG> [103377] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca)
DEBUG> [103377] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoap (de6c2b5d131218d6543241e4c9adc4ca) è stata salvata
DEBUG> [103377] WPSOWORKS::register_master_sessions: Registro nel cache server la sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f)
DEBUG> [103377] WPSOWORKS::register_master_sessions: La sessione master dell'utente wpsoworks (81ae39a935d496e8bef2320a3451181f) è stata salvata
